{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\ReportSummary.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportSummary = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null\n}) => {\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | Acme Print\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-4\",\n          style: headingTextStyle,\n          children: \"Executive Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg leading-relaxed text-gray-700 mb-6\",\n          style: contentTextStyle,\n          children: \"Acme Print has demonstrated significant financial improvement as of January 2025, reporting a notable increase in net profit compared to the previous year. The company achieved a YTD Net Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3 million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up from a loss in the previous year. This positive trend is driven by increased sales, improved cost management, and operational efficiency.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl font-semibold text-teal-600 mb-5\",\n        style: headingTextStyle,\n        children: \"Analysis and Actionable Recommendations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: '-7px'\n          },\n          children: \"Profitability Ratios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Net Profit Margin has increased 33% from a negative margin the previous year. This indicates improved profitability and cost management.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Gross Profit Margin has risen to 56%, highlighting better control over production costs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: \"bold\",\n            color: 'black',\n            marginTop: '-4px',\n            marginBottom: \"-4px\"\n          },\n          children: [\"Recommendation\", /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              marginTop: '-4px'\n            },\n            children: \"Maintain the current cost management strategies, particularly focusing on sustaining high-margin product lines and reducing labor inefficiencies.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Revenue and Income Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                ...subHeadingTextStyle,\n                fontWeight: \"bold\",\n                color: \"black\",\n                marginTop: '-4px',\n                marginBottom: '-4px'\n              },\n              children: \"Significant Sales Growth\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), \" Total income increased from $3.4 million in Jan 2024 to $5.2 million in Jan 2025.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Key Product Performance:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 05 - Top performer with sales of $1.63 million.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Freight Sales - Substantial growth to $1.31 million, up from $485K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Packaging Sales - Increased to $633K from $295K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Underperforming Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 02- Decreased sales from $72K to $6K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 04- Signifcant drop from $158K to $1K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Focus on promoting high-performing products (like Product 05) while reassessing the strategy for underperforming items, particularly Products 02 and 04.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | Acme Print\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Cost Management and COGS Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"COGS as a percentage of income reduced from 65% to 44%.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Cost Reductions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Freight COGS - Increased by 21%, indicating higher volume of sales.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Labor COGS - Improved management with costs decreasing in several product lines.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Material Costs - Significant reductions in Product 04 and Product 05.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Continue focusing on labor efficiency and explore more strategic sourcing for raw materials to reduce material cost variations.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Expense Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Total expenses decreased by 14.5%, saving over $205K compared to the previous year.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Major Reductions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Depreciation Expense- Reduced by $161K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Sales Commissions- Down by $136K, aligning commission structures with performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Areas of Increase\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Rent Expense- Increased by 49.6K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Salaries - G&A- Up by $87K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Investigate the rise in G&A salaries and rent. Consider evaluating ofce space utilization and exploring remote work options to mitigate rent costs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Return on Equity (ROE) improved to 12%, reflecting better management of equity resources.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Quick Ratio improved to 1.48, indicating better short-term liquidity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Maintain or enhance operational efficiency by investing in technology that maximizes asset utilization and minimizes downtime.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Liquidity and Cash Flow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Quick Ratio improved to 1.48, indicating better short-term liquidity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Net Change in Cash was positive $1.4 million in January 2025.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Continue maintaining liquidity by preserving cash reserves and managing receivables more efciently.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-2 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 pt-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | Acme Print\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Expense Breakdown and Recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\"\n            },\n            children: \"High-Impact Expenses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Depreciation- Despite reductions, still the largest single expense.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Salaries - G&A- Continues to be a signifcant cost driver.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Consider asset revaluation to reduce depreciation.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: contentTextStyle,\n            children: \"Optimize G&A expenses by streamlining administrative processes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white pb-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: headingTextStyle,\n          children: \"Key Focus Areas for 2025\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg leading-relaxed text-gray-700\",\n          style: contentTextStyle,\n          children: \"Acme Print has demonstrated significant financial improvement, particularly in profitability and operational efficiency. Strategic cost management and increased sales have positively impacted the bottom line. Moving forward, focusing on optimizing underperforming products, managing rent and salary costs, and maintaining liquidity will ensure continued financial stability and growth.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs py-9 px-12 mt-auto\",\n        style: {\n          position: 'absolute',\n          left: 0,\n          right: 0,\n          bottom: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or fnancial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable eforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_c = ReportSummary;\nexport default ReportSummary;\nvar _c;\n$RefreshReg$(_c, \"ReportSummary\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ReportSummary", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "formatHeaderPeriod", "startYear", "startMonth", "monthNames", "startMonthName", "className", "children", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYStartYear", "FYStartMonth", "color", "marginBottom", "fontWeight", "marginTop", "position", "left", "right", "bottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/ReportSummary.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ReportSummary = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null\r\n}) => {\r\n\r\n    const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  \r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\">\r\n\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Executive Summary Section */}\r\n        <div className=\"bg-white mb-4\">\r\n          <div className=\"text-2xl font-semibold text-teal-600 mb-4\" style={headingTextStyle}>\r\n            Executive Summary\r\n          </div>\r\n          <p className=\"text-lg leading-relaxed text-gray-700 mb-6\" style={contentTextStyle}>\r\n            Acme Print has demonstrated significant financial improvement as of January 2025, reporting a\r\n            notable increase in net profit compared to the previous year. The company achieved a YTD Net\r\n            Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3\r\n            million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up\r\n            from a loss in the previous year. This positive trend is driven by increased sales, improved cost\r\n            management, and operational efficiency.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Analysis and Recommendations Header */}\r\n        <div className=\"text-2xl font-semibold text-teal-600 mb-5\" style={headingTextStyle}>\r\n          Analysis and Actionable Recommendations\r\n        </div>\r\n\r\n        {/* Profitability Ratios */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: '-7px' }}>\r\n            Profitability Ratios\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Net Profit Margin has increased 33% from a negative margin the previous year. This\r\n              indicates improved profitability and cost management.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Gross Profit Margin has risen to 56%, highlighting better control over production\r\n              costs.\r\n            </p>\r\n          </div>\r\n          <div\r\n           style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n            Recommendation\r\n            <div style={{ ...contentTextStyle, marginTop: '-4px' }}>\r\n              Maintain the current cost management strategies, particularly focusing on sustaining\r\n              high-margin product lines and reducing labor inefficiencies.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Revenue and Income Analysis */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Revenue and Income Analysis\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              <span style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>Significant Sales Growth</span> Total income increased from $3.4 million\r\n              in Jan 2024 to $5.2 million in Jan 2025.\r\n            </p>\r\n            <p style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Key Product Performance:\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 05 - Top performer with sales of $1.63 million.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Freight Sales - Substantial growth to $1.31 million, up from $485K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Packaging Sales - Increased to $633K from $295K.\r\n              </p>\r\n            </div>\r\n            <p style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Underperforming Products\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 02- Decreased sales from $72K to $6K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 04- Signifcant drop from $158K to $1K.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Focus on promoting high-performing products (like Product 05) while reassessing the\r\n              strategy for underperforming items, particularly Products 02 and 04.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-8\">\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Cost Management and COGS Analysis */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Cost Management and COGS Analysis\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              COGS as a percentage of income reduced from 65% to 44%.\r\n            </p>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Cost Reductions:\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Freight COGS - Increased by 21%, indicating higher volume of sales.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Labor COGS - Improved management with costs decreasing in several product lines.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Material Costs - Significant reductions in Product 04 and Product 05.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Continue focusing on labor efficiency and explore more strategic sourcing for raw\r\n              materials to reduce material cost variations.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Expense Management */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Expense Management\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Total expenses decreased by 14.5%, saving over $205K compared to the previous year.\r\n            </p>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Major Reductions\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Depreciation Expense- Reduced by $161K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Sales Commissions- Down by $136K, aligning commission structures with performance.\r\n              </p>\r\n            </div>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Areas of Increase\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Rent Expense- Increased by 49.6K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Salaries - G&A- Up by $87K.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\" mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Investigate the rise in G&A salaries and rent. Consider evaluating ofce space utilization and\r\n              exploring remote work options to mitigate rent costs.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Operational Efficiency */}\r\n        <div className='mb-2'>\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Operational Efficiency\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Return on Equity (ROE) improved to 12%, reflecting better management of equity\r\n              resources.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Quick Ratio improved to 1.48, indicating better short-term liquidity.\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\"font-semibold mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Maintain or enhance operational efficiency by investing in technology that maximizes\r\n              asset utilization and minimizes downtime.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Liquidity and Cash Flow */}\r\n        <div className='mb-2'>\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Liquidity and Cash Flow\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Quick Ratio improved to 1.48, indicating better short-term liquidity.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Net Change in Cash was positive $1.4 million in January 2025.\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\"font-semibold mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Continue maintaining liquidity by preserving cash reserves and managing receivables more\r\n              efciently.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-2 relative\">\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 pt-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Expense Breakdown and Recommendations*/}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Expense Breakdown and Recommendations\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\" }}>\r\n              High-Impact Expenses\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Depreciation- Despite reductions, still the largest single expense.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Salaries - G&A- Continues to be a signifcant cost driver.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\" mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <p className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Consider asset revaluation to reduce depreciation.\r\n            </p>\r\n            <p style={contentTextStyle}>\r\n              Optimize G&A expenses by streamlining administrative processes.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Key Focus Areas for 2025 */}\r\n        <div className=\"bg-white pb-5\">\r\n          <div className=\"text-2xl font-semibold text-teal-600 mb-5\" style={headingTextStyle}>\r\n            Key Focus Areas for 2025\r\n          </div>\r\n          <p className=\"text-lg leading-relaxed text-gray-700\" style={contentTextStyle}>\r\n            Acme Print has demonstrated significant financial improvement, particularly in profitability and\r\n            operational efficiency. Strategic cost management and increased sales have positively impacted\r\n            the bottom line. Moving forward, focusing on optimizing underperforming products, managing\r\n            rent and salary costs, and maintaining liquidity will ensure continued financial stability and\r\n            growth.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer - Always stick to bottom */}\r\n        <div\r\n          className='text-center text-slate-300 text-xs py-9 px-12 mt-auto'\r\n          style={{ position: 'absolute', left: 0, right: 0, bottom: 0 }}\r\n        >\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or fnancial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable eforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportSummary;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EACrBC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG;AACf,CAAC,KAAK;EAEF,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACtD,MAAMC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACF,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAME,cAAc,GAAGD,UAAU,CAACD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGE,cAAc,IAAIH,SAAS,EAAE;EACzC,CAAC;EAID,oBACER,OAAA;IAAKY,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/Bb,OAAA;MAAKY,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAGjFb,OAAA;QAAKY,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAClGb,OAAA;UAAIc,KAAK,EAAEZ,eAAgB;UAAAW,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/ClB,OAAA;UAAGY,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEV,mBAAoB;UAAAS,QAAA,GACjEN,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,WAAW,EAAEb,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEc,YAAY,CAAC,EAAC,eACzE;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5Bb,OAAA;UAAKY,SAAS,EAAC,2CAA2C;UAACE,KAAK,EAAEX,gBAAiB;UAAAU,QAAA,EAAC;QAEpF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlB,OAAA;UAAGY,SAAS,EAAC,4CAA4C;UAACE,KAAK,EAAET,gBAAiB;UAAAQ,QAAA,EAAC;QAOnF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,2CAA2C;QAACE,KAAK,EAAEX,gBAAiB;QAAAU,QAAA,EAAC;MAEpF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlB,OAAA;UACCc,KAAK,EAAE;YAAE,GAAGV,mBAAmB;YAAEmB,UAAU,EAAE,MAAM;YAAEF,KAAK,EAAE,OAAO;YAAEG,SAAS,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,GAAC,gBAE/G,eAAAb,OAAA;YAAKc,KAAK,EAAE;cAAE,GAAGT,gBAAgB;cAAEmB,SAAS,EAAE;YAAO,CAAE;YAAAX,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,gBACrDb,OAAA;cAAMc,KAAK,EAAE;gBAAE,GAAGV,mBAAmB;gBAAEmB,UAAU,EAAE,MAAM;gBAAEF,KAAK,EAAE,OAAO;gBAAEG,SAAS,EAAE,MAAM;gBAAEF,YAAY,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,sFAEvJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGc,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEnH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlB,OAAA;YAAGc,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEnH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YACEc,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAElH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAKY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA;MAAKY,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBACjFb,OAAA;QAAKY,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAClGb,OAAA;UAAIc,KAAK,EAAEZ,eAAgB;UAAAW,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/ClB,OAAA;UAAGY,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEV,mBAAoB;UAAAS,QAAA,GACjEN,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,WAAW,EAAEb,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEc,YAAY,CAAC,EAAC,eACzE;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAE7J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YACCc,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAKY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAE7J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlB,OAAA;YAAGY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAE7J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YACEY,SAAS,EAAC,OAAO;YAClBE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAKY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YACEY,SAAS,EAAC,oBAAoB;YAC/BE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAKY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGY,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YACEY,SAAS,EAAC,oBAAoB;YAC/BE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAKY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlB,OAAA;MAAKY,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAC1Fb,OAAA;QAAKY,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvGb,OAAA;UAAIc,KAAK,EAAEZ,eAAgB;UAAAW,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/ClB,OAAA;UAAGY,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEV,mBAAoB;UAAAS,QAAA,GACjEN,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEa,WAAW,EAAEb,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEc,YAAY,CAAC,EAAC,eACzE;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBb,OAAA;UAAIc,KAAK,EAAE;YAAE,GAAGX,gBAAgB;YAAEkB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAGY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE;YAAQ,CAAE;YAAAR,QAAA,EAAC;UAEpH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAAa,QAAA,gBACEb,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA;cAAGY,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAET,gBAAiB;cAAAQ,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YACEY,SAAS,EAAC,OAAO;YAClBE,KAAK,EAAE;cAAE,GAAGV,mBAAmB;cAAEmB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlB,OAAA;YAAGY,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlB,OAAA;YAAGc,KAAK,EAAET,gBAAiB;YAAAQ,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA;QAAKY,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5Bb,OAAA;UAAKY,SAAS,EAAC,2CAA2C;UAACE,KAAK,EAAEX,gBAAiB;UAAAU,QAAA,EAAC;QAEpF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlB,OAAA;UAAGY,SAAS,EAAC,uCAAuC;UAACE,KAAK,EAAET,gBAAiB;UAAAQ,QAAA,EAAC;QAM9E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlB,OAAA;QACEY,SAAS,EAAC,uDAAuD;QACjEE,KAAK,EAAE;UAAEW,QAAQ,EAAE,UAAU;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAf,QAAA,eAE9Db,OAAA;UAAAa,QAAA,EAAG;QAKH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACW,EAAA,GA/VI5B,aAAa;AAiWnB,eAAeA,aAAa;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}