{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\Reports.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"react-datepicker/dist/react-datepicker.css\";\nimport EditReport from \"./EditReport\";\nimport ReportModal from \"./ReportModal\";\nimport { deleteRequest, getAllCompanyReports } from \"../../../services/report\";\nimport { formatDate } from \"../../../utils/shared\";\nimport Swal from \"sweetalert2\";\nimport { CircularProgress } from \"@mui/material\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport Button from '@mui/material/Button';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Reports = ({\n  companyFiles,\n  companyId,\n  companyFYEndDate,\n  setActiveTab\n}) => {\n  _s();\n  console.log(\"FyDate: \", companyFYEndDate);\n  const [reportModal, setReportModal] = useState(false);\n  const [reportDetail, setReportDetail] = useState(null);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [reports, setReports] = useState([]);\n  const [isloading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const params = useParams();\n\n  // Get company ID from props or params\n  const currentCompanyId = companyId || params.id;\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n  const handleSubmitSuccess = () => {\n    companyReports();\n    Swal.fire({\n      toast: true,\n      position: \"top-end\",\n      icon: \"success\",\n      title: \"Report added successfully!\",\n      showConfirmButton: false,\n      timer: 1000,\n      timerProgressBar: true\n    });\n  };\n  const requestTypeLabels = {\n    Monthly: \"ProfitPulse (Monthly)\",\n    Benchmark: \"KPITrack (Benchmark)\",\n    \"GAAP Analyzer\": \"GAAP Align\",\n    CSFA: \"FinCheck (Current State)\",\n    \"13weeks\": \"FlowCast (13 Week)\"\n  };\n  const handleView = reportId => {\n    // Navigate to the custom template route with the current company ID and report ID\n    navigate(`/company/${currentCompanyId}/custom-template/${reportId}`);\n  };\n  const handleReportNameClick = row => {\n    // Only allow navigation for Deepsight reports\n    if (row.name.toLowerCase().includes('deepsight')) {\n      handleView(row.id);\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      var _response$data, _response$data2;\n      setLoading(true);\n      const response = await deleteRequest(id);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.request && (response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.statusCode) === 200) {\n        Swal.fire({\n          toast: true,\n          position: \"top-end\",\n          icon: \"success\",\n          title: \"Report request deleted successfully!\",\n          showConfirmButton: false,\n          timer: 1000,\n          timerProgressBar: true\n        });\n        await companyReports();\n      }\n    } catch (err) {\n      console.error(\"Error in deleting request report:\", err);\n      Swal.fire({\n        toast: true,\n        position: \"top-end\",\n        icon: \"error\",\n        title: \"Failed to delete request. Please try again.\",\n        showConfirmButton: false,\n        timer: 3000,\n        timerProgressBar: true\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getBorderColor = status => {\n    switch (status) {\n      case \"download\":\n        return \"green\";\n      case \"edit\":\n        return \"blue\";\n      case \"error\":\n        return \"red\";\n      case \"processing\":\n        return \"orange\";\n      default:\n        return \"grey\";\n    }\n  };\n  const companyReports = async () => {\n    setLoading(true); // Start loading\n    try {\n      const reports = await getAllCompanyReports(currentCompanyId);\n      if (reports.status === 200) {\n        var _reports$data;\n        setReports(reports === null || reports === void 0 ? void 0 : (_reports$data = reports.data) === null || _reports$data === void 0 ? void 0 : _reports$data.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching reports:\", error);\n    } finally {\n      setLoading(false); // End loading\n    }\n  };\n  useEffect(() => {\n    if (currentCompanyId) {\n      companyReports();\n    }\n  }, [currentCompanyId, reportDetail]);\n  const handleDownload = documents => {\n    var _documents$;\n    const signedUrl = (_documents$ = documents[0]) === null || _documents$ === void 0 ? void 0 : _documents$.file_key;\n    window.open(`${signedUrl}`, \"_blank\");\n  };\n  const handleEditReport = row => {\n    setReportDetail(row);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"px-2 py-3\",\n    children: [reportDetail ? /*#__PURE__*/_jsxDEV(EditReport, {\n      report: reportDetail,\n      setReportDetail: setReportDetail\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-semibold\",\n          children: \"Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: () => setReportModal(true),\n          sx: {\n            width: 'auto',\n            minWidth: 160,\n            px: 2,\n            py: 1,\n            fontWeight: 500,\n            textTransform: 'uppercase',\n            fontSize: '0.875rem'\n          },\n          children: \"REQUEST NEW REPORT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: isloading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center p-6\",\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-4 font-semibold text-gray-600 sm:w-[45%] md:w-[55%] lg:w-[65%]\",\n                  children: \"Report Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-4 font-semibold text-gray-600  sm:w-[15%] md:w-[20%] lg:w-[15%]\",\n                  children: \"Date Submitted\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-4 font-semibold text-gray-600  sm:w-[15%] md:w-[10%] lg:w-[10%]\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-center p-4 font-semibold text-gray-600 sm:w-[15%] md:w-[15%] lg:w-[10%]\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: (reports === null || reports === void 0 ? void 0 : reports.length) > 0 ? reports === null || reports === void 0 ? void 0 : reports.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map(row => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-t\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: `p-4 sm:w-[45%] md:w-[55%] lg:w-[65%] ${row.name.toLowerCase().includes('deepsight') ? 'cursor-pointer' : ''}`,\n                  onClick: () => handleReportNameClick(row),\n                  title: row.name.toLowerCase().includes('deepsight') ? 'Click to view report' : '',\n                  children: row.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 29\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 sm:w-[15%] md:w-[20%] lg:w-[15%]\",\n                  children: formatDate(row.date_requested)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 29\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 cursor-pointer\",\n                  onClick: row.status === \"download\" ? () => handleDownload(row.documents) : row.status === \"edit\" ? () => handleEditReport(row) : null,\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-3 py-1 rounded-full text-sm sm:w-[15%] md:w-[10%] lg:w-[10%]\",\n                    style: {\n                      border: `2px solid ${getBorderColor(row.status)}`,\n                      paddingBottom: '7px',\n                      color: getBorderColor(row.status)\n                    },\n                    children: row.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 29\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-4 text-center sm:w-[15%] md:w-[15%] lg:w-[10%]\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDelete(row.id),\n                      className: \"text-gray-500 hover:text-red-600 transition-colors duration-200\",\n                      title: \"Delete\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: \"2\",\n                          d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 251,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 29\n                }, this)]\n              }, row.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 27\n              }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"4\",\n                  className: \"p-4 text-center text-gray-600\",\n                  children: \"No Report requests\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-4 border-t\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Rows per page:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: rowsPerPage,\n                onChange: handleChangeRowsPerPage,\n                className: \"border rounded p-1\",\n                children: [5, 10, 25].map(n => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: n,\n                  children: n\n                }, n, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => handleChangePage(e, page - 1),\n                disabled: page === 0,\n                className: \"p-2 rounded hover:bg-gray-100 disabled:opacity-50\",\n                children: \"Previous\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Page \", page + 1, \" of\", \" \", Math.ceil(reports.length / rowsPerPage)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => handleChangePage(e, page + 1),\n                disabled: page >= Math.ceil(reports.length / rowsPerPage) - 1,\n                className: \"p-2 rounded hover:bg-gray-100 disabled:opacity-50\",\n                children: \"Next\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), reportModal && /*#__PURE__*/_jsxDEV(ReportModal, {\n      setActiveTab: setActiveTab,\n      companyFiles: companyFiles,\n      companyId: currentCompanyId,\n      companyFYEndDate: companyFYEndDate,\n      onClose: () => setReportModal(false),\n      onSubmitSuccess: handleSubmitSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"kPfrwTbff1HKQtxML8u3qM0shRQ=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "EditReport", "ReportModal", "deleteRequest", "getAllCompanyReports", "formatDate", "<PERSON><PERSON>", "CircularProgress", "useNavigate", "useParams", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Reports", "companyFiles", "companyId", "companyFYEndDate", "setActiveTab", "_s", "console", "log", "reportModal", "setReportModal", "reportDetail", "setReportDetail", "page", "setPage", "rowsPerPage", "setRowsPerPage", "reports", "setReports", "isloading", "setLoading", "navigate", "params", "currentCompanyId", "id", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleSubmitSuccess", "companyReports", "fire", "toast", "position", "icon", "title", "showConfirmButton", "timer", "timerP<PERSON>ressBar", "requestTypeLabels", "Monthly", "Benchmark", "CSFA", "handleView", "reportId", "handleReportNameClick", "row", "name", "toLowerCase", "includes", "handleDelete", "_response$data", "_response$data2", "response", "data", "request", "statusCode", "err", "error", "getBorderColor", "status", "_reports$data", "handleDownload", "documents", "_documents$", "signedUrl", "file_key", "window", "open", "handleEditReport", "className", "children", "report", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "onClick", "sx", "width", "min<PERSON><PERSON><PERSON>", "px", "py", "fontWeight", "textTransform", "fontSize", "length", "slice", "map", "date_requested", "style", "border", "paddingBottom", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "colSpan", "onChange", "n", "e", "disabled", "Math", "ceil", "onClose", "onSubmitSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/Reports.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport EditReport from \"./EditReport\";\r\nimport ReportModal from \"./ReportModal\";\r\nimport { deleteRequest, getAllCompanyReports } from \"../../../services/report\";\r\nimport { formatDate } from \"../../../utils/shared\";\r\nimport Swal from \"sweetalert2\";\r\nimport { CircularProgress } from \"@mui/material\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport Button from '@mui/material/Button';\r\n\r\nconst Reports = ({ companyFiles, companyId, companyFYEndDate, setActiveTab }) => {\r\n  console.log(\"FyDate: \", companyFYEndDate);\r\n  const [reportModal, setReportModal] = useState(false);\r\n  const [reportDetail, setReportDetail] = useState(null);\r\n  const [page, setPage] = useState(0);\r\n  const [rowsPerPage, setRowsPerPage] = useState(10);\r\n  const [reports, setReports] = useState([]);\r\n  const [isloading, setLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n  const params = useParams();\r\n\r\n  // Get company ID from props or params\r\n  const currentCompanyId = companyId || params.id;\r\n\r\n  const handleChangePage = (event, newPage) => {\r\n    setPage(newPage);\r\n  };\r\n\r\n  const handleChangeRowsPerPage = (event) => {\r\n    setRowsPerPage(parseInt(event.target.value, 10));\r\n    setPage(0);\r\n  };\r\n\r\n  const handleSubmitSuccess = () => {\r\n    companyReports();\r\n    Swal.fire({\r\n      toast: true,\r\n      position: \"top-end\",\r\n      icon: \"success\",\r\n      title: \"Report added successfully!\",\r\n      showConfirmButton: false,\r\n      timer: 1000,\r\n      timerProgressBar: true,\r\n    });\r\n  };\r\n\r\n  const requestTypeLabels = {\r\n    Monthly: \"ProfitPulse (Monthly)\",\r\n    Benchmark: \"KPITrack (Benchmark)\",\r\n    \"GAAP Analyzer\": \"GAAP Align\",\r\n    CSFA: \"FinCheck (Current State)\",\r\n    \"13weeks\": \"FlowCast (13 Week)\",\r\n  };\r\n\r\n  const handleView = (reportId) => {\r\n    // Navigate to the custom template route with the current company ID and report ID\r\n    navigate(`/company/${currentCompanyId}/custom-template/${reportId}`);\r\n  };\r\n\r\n  const handleReportNameClick = (row) => {\r\n    // Only allow navigation for Deepsight reports\r\n    if (row.name.toLowerCase().includes('deepsight')) {\r\n      handleView(row.id);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await deleteRequest(id);\r\n      if (response?.data?.request && response?.data?.statusCode === 200) {\r\n        Swal.fire({\r\n          toast: true,\r\n          position: \"top-end\",\r\n          icon: \"success\",\r\n          title: \"Report request deleted successfully!\",\r\n          showConfirmButton: false,\r\n          timer: 1000,\r\n          timerProgressBar: true,\r\n        });\r\n        await companyReports();\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error in deleting request report:\", err);\r\n      Swal.fire({\r\n        toast: true,\r\n        position: \"top-end\",\r\n        icon: \"error\",\r\n        title: \"Failed to delete request. Please try again.\",\r\n        showConfirmButton: false,\r\n        timer: 3000,\r\n        timerProgressBar: true,\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getBorderColor = (status) => {\r\n    switch (status) {\r\n      case \"download\":\r\n        return \"green\";\r\n      case \"edit\":\r\n        return \"blue\";\r\n      case \"error\":\r\n        return \"red\";\r\n      case \"processing\":\r\n        return \"orange\";\r\n      default:\r\n        return \"grey\";\r\n    }\r\n  };\r\n\r\n  const companyReports = async () => {\r\n    setLoading(true); // Start loading\r\n    try {\r\n      const reports = await getAllCompanyReports(currentCompanyId);\r\n      if (reports.status === 200) {\r\n        setReports(reports?.data?.data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching reports:\", error);\r\n    } finally {\r\n      setLoading(false); // End loading\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (currentCompanyId) {\r\n      companyReports();\r\n    }\r\n  }, [currentCompanyId, reportDetail]);\r\n\r\n  const handleDownload = (documents) => {\r\n    const signedUrl = documents[0]?.file_key;\r\n    window.open(`${signedUrl}`, \"_blank\");\r\n  };\r\n\r\n  const handleEditReport = (row) => {\r\n    setReportDetail(row);\r\n  };\r\n\r\n  return (\r\n    <div className=\"px-2 py-3\">\r\n      {reportDetail ? (\r\n        <EditReport report={reportDetail} setReportDetail={setReportDetail} />\r\n      ) : (\r\n        <>\r\n          <div className=\"flex justify-between items-center mb-6\">\r\n            <h1 className=\"text-2xl font-semibold\">Reports</h1>\r\n            <Button\r\n              variant=\"contained\"\r\n              color=\"primary\"\r\n              onClick={() => setReportModal(true)}\r\n              sx={{\r\n                width: 'auto',\r\n                minWidth: 160,\r\n                px: 2,\r\n                py: 1,\r\n                fontWeight: 500,\r\n                textTransform: 'uppercase',\r\n                fontSize: '0.875rem',\r\n              }}\r\n            >\r\n              REQUEST NEW REPORT\r\n            </Button>\r\n          </div>\r\n          <div className=\"bg-white rounded-lg shadow\">\r\n            {isloading ? (\r\n              <div className=\"flex justify-center items-center p-6\">\r\n                <CircularProgress />\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <table className=\"w-full\">\r\n                  <thead>\r\n                    <tr className=\"bg-gray-50\">\r\n                      <th className=\"text-left p-4 font-semibold text-gray-600 sm:w-[45%] md:w-[55%] lg:w-[65%]\">\r\n                        Report Name\r\n                      </th>\r\n                      <th className=\"text-left p-4 font-semibold text-gray-600  sm:w-[15%] md:w-[20%] lg:w-[15%]\">\r\n                        Date Submitted\r\n                      </th>\r\n                      <th className=\"text-left p-4 font-semibold text-gray-600  sm:w-[15%] md:w-[10%] lg:w-[10%]\">\r\n                        Status\r\n                      </th>\r\n                      <th className=\"text-center p-4 font-semibold text-gray-600 sm:w-[15%] md:w-[15%] lg:w-[10%]\">\r\n                        Actions\r\n                      </th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    {reports?.length > 0 ? (\r\n                      reports\r\n                        ?.slice(\r\n                          page * rowsPerPage,\r\n                          page * rowsPerPage + rowsPerPage\r\n                        )\r\n                        .map((row) => (\r\n                          <tr key={row.id} className=\"border-t\">\r\n                            <td\r\n                              className={`p-4 sm:w-[45%] md:w-[55%] lg:w-[65%] ${row.name.toLowerCase().includes('deepsight')\r\n                                  ? 'cursor-pointer'\r\n                                  : ''\r\n                                }`}\r\n                              onClick={() => handleReportNameClick(row)}\r\n                              title={row.name.toLowerCase().includes('deepsight') ? 'Click to view report' : ''}\r\n                            >\r\n                              {row.name}\r\n                            </td>\r\n                            <td className=\"p-4 sm:w-[15%] md:w-[20%] lg:w-[15%]\">\r\n                              {formatDate(row.date_requested)}\r\n                            </td>\r\n                            <td\r\n                              className=\"p-4 cursor-pointer\"\r\n                              onClick={\r\n                                row.status === \"download\"\r\n                                  ? () => handleDownload(row.documents)\r\n                                  : row.status === \"edit\"\r\n                                    ? () => handleEditReport(row)\r\n                                    : null\r\n                              }\r\n                            >\r\n                              <span\r\n                                className=\"px-3 py-1 rounded-full text-sm sm:w-[15%] md:w-[10%] lg:w-[10%]\"\r\n                                style={{\r\n                                  border: `2px solid ${getBorderColor(\r\n                                    row.status\r\n                                  )}`,\r\n                                  paddingBottom : '7px',\r\n                                  color: getBorderColor(row.status),\r\n                                }}\r\n                              >\r\n                                {row.status}\r\n                              </span>\r\n                            </td>\r\n                            <td className=\"p-4 text-center sm:w-[15%] md:w-[15%] lg:w-[10%]\">\r\n                              <div className=\"flex items-center justify-center\">\r\n                                <button\r\n                                  onClick={() => handleDelete(row.id)}\r\n                                  className=\"text-gray-500 hover:text-red-600 transition-colors duration-200\"\r\n                                  title=\"Delete\"\r\n                                >\r\n                                  <svg\r\n                                    className=\"w-5 h-5\"\r\n                                    fill=\"none\"\r\n                                    stroke=\"currentColor\"\r\n                                    viewBox=\"0 0 24 24\"\r\n                                  >\r\n                                    <path\r\n                                      strokeLinecap=\"round\"\r\n                                      strokeLinejoin=\"round\"\r\n                                      strokeWidth=\"2\"\r\n                                      d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\r\n                                    />\r\n                                  </svg>\r\n                                </button>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ))\r\n                    ) : (\r\n                      <tr>\r\n                        <td\r\n                          colSpan=\"4\"\r\n                          className=\"p-4 text-center text-gray-600\"\r\n                        >\r\n                          No Report requests\r\n                        </td>\r\n                      </tr>\r\n                    )}\r\n                  </tbody>\r\n                </table>\r\n                <div className=\"flex items-center justify-between p-4 border-t\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <span className=\"text-sm text-gray-600\">\r\n                      Rows per page:\r\n                    </span>\r\n                    <select\r\n                      value={rowsPerPage}\r\n                      onChange={handleChangeRowsPerPage}\r\n                      className=\"border rounded p-1\"\r\n                    >\r\n                      {[5, 10, 25].map((n) => (\r\n                        <option key={n} value={n}>\r\n                          {n}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <button\r\n                      onClick={(e) => handleChangePage(e, page - 1)}\r\n                      disabled={page === 0}\r\n                      className=\"p-2 rounded hover:bg-gray-100 disabled:opacity-50\"\r\n                    >\r\n                      Previous\r\n                    </button>\r\n                    <span className=\"text-sm text-gray-600\">\r\n                      Page {page + 1} of{\" \"}\r\n                      {Math.ceil(reports.length / rowsPerPage)}\r\n                    </span>\r\n                    <button\r\n                      onClick={(e) => handleChangePage(e, page + 1)}\r\n                      disabled={\r\n                        page >= Math.ceil(reports.length / rowsPerPage) - 1\r\n                      }\r\n                      className=\"p-2 rounded hover:bg-gray-100 disabled:opacity-50\"\r\n                    >\r\n                      Next\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        </>\r\n      )}\r\n      {reportModal && (\r\n        <ReportModal\r\n          setActiveTab={setActiveTab}\r\n          companyFiles={companyFiles}\r\n          companyId={currentCompanyId}\r\n          companyFYEndDate={companyFYEndDate}\r\n          onClose={() => setReportModal(false)}\r\n          onSubmitSuccess={handleSubmitSuccess}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Reports;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,4CAA4C;AACnD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,0BAA0B;AAC9E,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,OAAO,GAAGA,CAAC;EAAEC,YAAY;EAAEC,SAAS;EAAEC,gBAAgB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC/EC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEJ,gBAAgB,CAAC;EACzC,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiC,SAAS,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMmC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,MAAM,GAAG3B,SAAS,CAAC,CAAC;;EAE1B;EACA,MAAM4B,gBAAgB,GAAGpB,SAAS,IAAImB,MAAM,CAACE,EAAE;EAE/C,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3Cb,OAAO,CAACa,OAAO,CAAC;EAClB,CAAC;EAED,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzCV,cAAc,CAACa,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChDjB,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAMkB,mBAAmB,GAAGA,CAAA,KAAM;IAChCC,cAAc,CAAC,CAAC;IAChBzC,IAAI,CAAC0C,IAAI,CAAC;MACRC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,SAAS;MACnBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,4BAA4B;MACnCC,iBAAiB,EAAE,KAAK;MACxBC,KAAK,EAAE,IAAI;MACXC,gBAAgB,EAAE;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAG;IACxBC,OAAO,EAAE,uBAAuB;IAChCC,SAAS,EAAE,sBAAsB;IACjC,eAAe,EAAE,YAAY;IAC7BC,IAAI,EAAE,0BAA0B;IAChC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,UAAU,GAAIC,QAAQ,IAAK;IAC/B;IACA1B,QAAQ,CAAC,YAAYE,gBAAgB,oBAAoBwB,QAAQ,EAAE,CAAC;EACtE,CAAC;EAED,MAAMC,qBAAqB,GAAIC,GAAG,IAAK;IACrC;IACA,IAAIA,GAAG,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChDN,UAAU,CAACG,GAAG,CAACzB,EAAE,CAAC;IACpB;EACF,CAAC;EAED,MAAM6B,YAAY,GAAG,MAAO7B,EAAE,IAAK;IACjC,IAAI;MAAA,IAAA8B,cAAA,EAAAC,eAAA;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoC,QAAQ,GAAG,MAAMnE,aAAa,CAACmC,EAAE,CAAC;MACxC,IAAIgC,QAAQ,aAARA,QAAQ,gBAAAF,cAAA,GAARE,QAAQ,CAAEC,IAAI,cAAAH,cAAA,eAAdA,cAAA,CAAgBI,OAAO,IAAI,CAAAF,QAAQ,aAARA,QAAQ,wBAAAD,eAAA,GAARC,QAAQ,CAAEC,IAAI,cAAAF,eAAA,uBAAdA,eAAA,CAAgBI,UAAU,MAAK,GAAG,EAAE;QACjEnE,IAAI,CAAC0C,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI;UACXC,QAAQ,EAAE,SAAS;UACnBC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,sCAAsC;UAC7CC,iBAAiB,EAAE,KAAK;UACxBC,KAAK,EAAE,IAAI;UACXC,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACF,MAAMR,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZrD,OAAO,CAACsD,KAAK,CAAC,mCAAmC,EAAED,GAAG,CAAC;MACvDpE,IAAI,CAAC0C,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,SAAS;QACnBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,6CAA6C;QACpDC,iBAAiB,EAAE,KAAK;QACxBC,KAAK,EAAE,IAAI;QACXC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,OAAO;MAChB,KAAK,MAAM;QACT,OAAO,MAAM;MACf,KAAK,OAAO;QACV,OAAO,KAAK;MACd,KAAK,YAAY;QACf,OAAO,QAAQ;MACjB;QACE,OAAO,MAAM;IACjB;EACF,CAAC;EAED,MAAM9B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCb,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAClB,IAAI;MACF,MAAMH,OAAO,GAAG,MAAM3B,oBAAoB,CAACiC,gBAAgB,CAAC;MAC5D,IAAIN,OAAO,CAAC8C,MAAM,KAAK,GAAG,EAAE;QAAA,IAAAC,aAAA;QAC1B9C,UAAU,CAACD,OAAO,aAAPA,OAAO,wBAAA+C,aAAA,GAAP/C,OAAO,CAAEwC,IAAI,cAAAO,aAAA,uBAAbA,aAAA,CAAeP,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACd,IAAIsC,gBAAgB,EAAE;MACpBU,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACV,gBAAgB,EAAEZ,YAAY,CAAC,CAAC;EAEpC,MAAMsD,cAAc,GAAIC,SAAS,IAAK;IAAA,IAAAC,WAAA;IACpC,MAAMC,SAAS,IAAAD,WAAA,GAAGD,SAAS,CAAC,CAAC,CAAC,cAAAC,WAAA,uBAAZA,WAAA,CAAcE,QAAQ;IACxCC,MAAM,CAACC,IAAI,CAAC,GAAGH,SAAS,EAAE,EAAE,QAAQ,CAAC;EACvC,CAAC;EAED,MAAMI,gBAAgB,GAAIvB,GAAG,IAAK;IAChCrC,eAAe,CAACqC,GAAG,CAAC;EACtB,CAAC;EAED,oBACEnD,OAAA;IAAK2E,SAAS,EAAC,WAAW;IAAAC,QAAA,GACvB/D,YAAY,gBACXb,OAAA,CAACX,UAAU;MAACwF,MAAM,EAAEhE,YAAa;MAACC,eAAe,EAAEA;IAAgB;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEtEjF,OAAA,CAAAE,SAAA;MAAA0E,QAAA,gBACE5E,OAAA;QAAK2E,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5E,OAAA;UAAI2E,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnDjF,OAAA,CAACF,MAAM;UACLoF,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMxE,cAAc,CAAC,IAAI,CAAE;UACpCyE,EAAE,EAAE;YACFC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,GAAG;YACbC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,UAAU,EAAE,GAAG;YACfC,aAAa,EAAE,WAAW;YAC1BC,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNjF,OAAA;QAAK2E,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EACxCvD,SAAS,gBACRrB,OAAA;UAAK2E,SAAS,EAAC,sCAAsC;UAAAC,QAAA,eACnD5E,OAAA,CAACL,gBAAgB;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENjF,OAAA,CAAAE,SAAA;UAAA0E,QAAA,gBACE5E,OAAA;YAAO2E,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACvB5E,OAAA;cAAA4E,QAAA,eACE5E,OAAA;gBAAI2E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACxB5E,OAAA;kBAAI2E,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,EAAC;gBAE3F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLjF,OAAA;kBAAI2E,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EAAC;gBAE5F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLjF,OAAA;kBAAI2E,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,EAAC;gBAE5F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLjF,OAAA;kBAAI2E,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,EAAC;gBAE7F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRjF,OAAA;cAAA4E,QAAA,EACG,CAAAzD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0E,MAAM,IAAG,CAAC,GAClB1E,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CACH2E,KAAK,CACL/E,IAAI,GAAGE,WAAW,EAClBF,IAAI,GAAGE,WAAW,GAAGA,WACvB,CAAC,CACA8E,GAAG,CAAE5C,GAAG,iBACPnD,OAAA;gBAAiB2E,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACnC5E,OAAA;kBACE2E,SAAS,EAAE,wCAAwCxB,GAAG,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,GACzF,gBAAgB,GAChB,EAAE,EACH;kBACL8B,OAAO,EAAEA,CAAA,KAAMlC,qBAAqB,CAACC,GAAG,CAAE;kBAC1CX,KAAK,EAAEW,GAAG,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,GAAG,sBAAsB,GAAG,EAAG;kBAAAsB,QAAA,EAEjFzB,GAAG,CAACC;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLjF,OAAA;kBAAI2E,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EACjDnF,UAAU,CAAC0D,GAAG,CAAC6C,cAAc;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC,eACLjF,OAAA;kBACE2E,SAAS,EAAC,oBAAoB;kBAC9BS,OAAO,EACLjC,GAAG,CAACc,MAAM,KAAK,UAAU,GACrB,MAAME,cAAc,CAAChB,GAAG,CAACiB,SAAS,CAAC,GACnCjB,GAAG,CAACc,MAAM,KAAK,MAAM,GACnB,MAAMS,gBAAgB,CAACvB,GAAG,CAAC,GAC3B,IACP;kBAAAyB,QAAA,eAED5E,OAAA;oBACE2E,SAAS,EAAC,iEAAiE;oBAC3EsB,KAAK,EAAE;sBACLC,MAAM,EAAE,aAAalC,cAAc,CACjCb,GAAG,CAACc,MACN,CAAC,EAAE;sBACHkC,aAAa,EAAG,KAAK;sBACrBhB,KAAK,EAAEnB,cAAc,CAACb,GAAG,CAACc,MAAM;oBAClC,CAAE;oBAAAW,QAAA,EAEDzB,GAAG,CAACc;kBAAM;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLjF,OAAA;kBAAI2E,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAC9D5E,OAAA;oBAAK2E,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,eAC/C5E,OAAA;sBACEoF,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACJ,GAAG,CAACzB,EAAE,CAAE;sBACpCiD,SAAS,EAAC,iEAAiE;sBAC3EnC,KAAK,EAAC,QAAQ;sBAAAoC,QAAA,eAEd5E,OAAA;wBACE2E,SAAS,EAAC,SAAS;wBACnByB,IAAI,EAAC,MAAM;wBACXC,MAAM,EAAC,cAAc;wBACrBC,OAAO,EAAC,WAAW;wBAAA1B,QAAA,eAEnB5E,OAAA;0BACEuG,aAAa,EAAC,OAAO;0BACrBC,cAAc,EAAC,OAAO;0BACtBC,WAAW,EAAC,GAAG;0BACfC,CAAC,EAAC;wBAA8H;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GA3DE9B,GAAG,CAACzB,EAAE;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4DX,CACL,CAAC,gBAEJjF,OAAA;gBAAA4E,QAAA,eACE5E,OAAA;kBACE2G,OAAO,EAAC,GAAG;kBACXhC,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EAC1C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRjF,OAAA;YAAK2E,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7D5E,OAAA;cAAK2E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC5E,OAAA;gBAAM2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAExC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjF,OAAA;gBACEiC,KAAK,EAAEhB,WAAY;gBACnB2F,QAAQ,EAAE9E,uBAAwB;gBAClC6C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAE7B,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAACmB,GAAG,CAAEc,CAAC,iBACjB7G,OAAA;kBAAgBiC,KAAK,EAAE4E,CAAE;kBAAAjC,QAAA,EACtBiC;gBAAC,GADSA,CAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEN,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNjF,OAAA;cAAK2E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtC5E,OAAA;gBACEoF,OAAO,EAAG0B,CAAC,IAAKnF,gBAAgB,CAACmF,CAAC,EAAE/F,IAAI,GAAG,CAAC,CAAE;gBAC9CgG,QAAQ,EAAEhG,IAAI,KAAK,CAAE;gBACrB4D,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjF,OAAA;gBAAM2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,OACjC,EAAC7D,IAAI,GAAG,CAAC,EAAC,KAAG,EAAC,GAAG,EACrBiG,IAAI,CAACC,IAAI,CAAC9F,OAAO,CAAC0E,MAAM,GAAG5E,WAAW,CAAC;cAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACPjF,OAAA;gBACEoF,OAAO,EAAG0B,CAAC,IAAKnF,gBAAgB,CAACmF,CAAC,EAAE/F,IAAI,GAAG,CAAC,CAAE;gBAC9CgG,QAAQ,EACNhG,IAAI,IAAIiG,IAAI,CAACC,IAAI,CAAC9F,OAAO,CAAC0E,MAAM,GAAG5E,WAAW,CAAC,GAAG,CACnD;gBACD0D,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,eACN;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACN,CACH,EACAtE,WAAW,iBACVX,OAAA,CAACV,WAAW;MACViB,YAAY,EAAEA,YAAa;MAC3BH,YAAY,EAAEA,YAAa;MAC3BC,SAAS,EAAEoB,gBAAiB;MAC5BnB,gBAAgB,EAAEA,gBAAiB;MACnC4G,OAAO,EAAEA,CAAA,KAAMtG,cAAc,CAAC,KAAK,CAAE;MACrCuG,eAAe,EAAEjF;IAAoB;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzE,EAAA,CAhUIL,OAAO;EAAA,QAQMP,WAAW,EACbC,SAAS;AAAA;AAAAuH,EAAA,GATpBjH,OAAO;AAkUb,eAAeA,OAAO;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}