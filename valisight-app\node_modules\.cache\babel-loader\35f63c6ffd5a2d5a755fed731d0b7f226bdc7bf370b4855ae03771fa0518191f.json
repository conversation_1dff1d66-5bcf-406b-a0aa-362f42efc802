{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\ReportSettings.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Switch, Card, Stack, Divider, TextField, Chip, IconButton, Tooltip, Button, useTheme, alpha, Snackbar, Alert, FormControl, Select, MenuItem, InputLabel, CircularProgress } from '@mui/material';\nimport { TuneOutlined as SettingsIcon, InfoOutlined as InfoIcon, CheckCircleOutlined as CheckIcon, SaveOutlined as SaveIcon, ScheduleOutlined as ComingSoonIcon } from '@mui/icons-material';\nimport { getContentSettingsByReportType, updateContentSettings } from '../../../services/contentSettings';\n\n// Fallback component for unsupported report types\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComingSoonFallback = ({\n  reportType,\n  reportTypeOptions\n}) => {\n  _s();\n  var _reportTypeOptions$fi;\n  const theme = useTheme();\n\n  // Find the label for the selected report type\n  const selectedReportLabel = ((_reportTypeOptions$fi = reportTypeOptions.find(option => option.value === reportType)) === null || _reportTypeOptions$fi === void 0 ? void 0 : _reportTypeOptions$fi.label) || reportType;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      elevation: 0,\n      sx: {\n        mb: 3,\n        p: 4,\n        backgroundColor: alpha('#1976d2', 0.05),\n        border: `1px solid ${alpha('#1976d2', 0.2)}`,\n        borderRadius: 2,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(ComingSoonIcon, {\n          sx: {\n            color: '#1976d2',\n            fontSize: '3rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              color: '#1976d2',\n              fontWeight: 600,\n              fontSize: '1.3rem',\n              mb: 1\n            },\n            children: [selectedReportLabel, \" - Coming Soon!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              color: 'text.secondary',\n              fontSize: '1rem',\n              maxWidth: '500px',\n              margin: '0 auto'\n            },\n            children: \"This report type will be implemented soon.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(ComingSoonFallback, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = ComingSoonFallback;\nconst ReportSettings = ({\n  companyId\n}) => {\n  _s2();\n  const theme = useTheme();\n  const [settings, setSettings] = useState({\n    reportType: 'DEEPSIGHT',\n    incomeStatement: {\n      incomeSummary: true,\n      netIncome: true,\n      grossProfitMargin: true,\n      netProfitMargin: true,\n      roaAndRoe: true\n    },\n    prompt: \"\"\n  });\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [showSuccess, setShowSuccess] = useState(false);\n  const [showError, setShowError] = useState(false);\n  const [errorMessage, setErrorMessage] = useState('');\n  const [hasChanges, setHasChanges] = useState(false);\n\n  // Report type options\n  const reportTypeOptions = [{\n    value: 'DEEPSIGHT',\n    label: 'Deepsight'\n  }, {\n    value: \"profitpulse\",\n    label: 'ProfitPulse (Monthly)'\n  }, {\n    value: \"kpitrack\",\n    label: 'KPITrack (Benchmark)'\n  }, {\n    value: \"gaap\",\n    label: 'GAAP Align'\n  }, {\n    value: \"fincheck\",\n    label: 'FinCheck (Current State)'\n  }, {\n    value: \"flowcast\",\n    label: 'FlowCast (13 Week)'\n  }];\n\n  // Check if the current report type is supported\n  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';\n\n  // Load existing settings on component mount\n  useEffect(() => {\n    const loadSettings = async () => {\n      if (!companyId) return;\n      try {\n        setIsLoading(true);\n        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');\n        if (response.data.success && response.data.data) {\n          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\n          if (data) {\n            setSettings(prev => ({\n              ...prev,\n              incomeStatement: data.chartSettings || prev.incomeStatement,\n              prompt: data.promptDescription || prev.prompt\n            }));\n          }\n        }\n      } catch (error) {\n        console.error('Error loading settings:', error);\n        setErrorMessage('Failed to load settings');\n        setShowError(true);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    loadSettings();\n  }, [companyId]);\n  const handleSwitchChange = (category, field) => event => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [field]: event.target.checked\n      }\n    }));\n    setHasChanges(true);\n  };\n  const handlePromptChange = event => {\n    setSettings(prev => ({\n      ...prev,\n      prompt: event.target.value\n    }));\n    setHasChanges(true);\n  };\n\n  // Updated handler for report type change\n  const handleReportTypeChange = async event => {\n    const newReportType = event.target.value;\n    setSettings(prev => ({\n      ...prev,\n      reportType: newReportType\n    }));\n\n    // If it's not DEEPSIGHT, don't try to load data - just show fallback\n    if (newReportType !== 'DEEPSIGHT') {\n      setHasChanges(false);\n      return;\n    }\n\n    // Load settings for DEEPSIGHT\n    if (!companyId) return;\n    try {\n      setIsLoading(true);\n      const response = await getContentSettingsByReportType(companyId, newReportType);\n      if (response.data.success && response.data.data) {\n        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\n        if (data) {\n          setSettings(prev => ({\n            ...prev,\n            incomeStatement: data.chartSettings || {\n              incomeSummary: true,\n              netIncome: true,\n              grossProfitMargin: true,\n              netProfitMargin: true,\n              roaAndRoe: true\n            },\n            prompt: data.promptDescription || ''\n          }));\n        } else {\n          setSettings(prev => ({\n            ...prev,\n            incomeStatement: {\n              incomeSummary: true,\n              netIncome: true,\n              grossProfitMargin: true,\n              netProfitMargin: true,\n              roaAndRoe: true\n            },\n            prompt: ''\n          }));\n        }\n      }\n      setHasChanges(false);\n    } catch (error) {\n      console.error('Error loading settings for report type:', error);\n      setErrorMessage('Failed to load settings');\n      setShowError(true);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async () => {\n    if (!companyId || !isCurrentReportTypeSupported) return;\n    setIsSaving(true);\n    try {\n      const payload = {\n        chartSettings: settings.incomeStatement,\n        promptDescription: settings.prompt\n      };\n      await updateContentSettings(companyId, settings.reportType, payload);\n      setHasChanges(false);\n      setShowSuccess(true);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error saving settings:', error);\n      setErrorMessage(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to save settings');\n      setShowError(true);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleCloseSuccess = () => {\n    setShowSuccess(false);\n  };\n  const handleCloseError = () => {\n    setShowError(false);\n    setErrorMessage('');\n  };\n  const settingsOptions = [{\n    key: 'incomeSummary',\n    label: 'Income Summary',\n    tooltip: 'Provides a high-level overview of total income for the selected period, showing revenue sources and major income categories.'\n  }, {\n    key: 'netIncome',\n    label: 'Net Income',\n    tooltip: 'Shows the company\\’s bottom-line profit after deducting all expenses, taxes, and interest. Indicates overall profitability for the period.'\n  }, {\n    key: 'grossProfitMargin',\n    label: 'Gross Profit Margin',\n    tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'\n  }, {\n    key: 'netProfitMargin',\n    label: 'Net Profit Margin',\n    tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'\n  }, {\n    key: 'roaAndRoe',\n    label: 'ROA and ROE',\n    tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'\n  }];\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '400px'\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '300px',\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: \"report-type-label\",\n            sx: {\n              fontSize: '1rem',\n              fontWeight: 500,\n              color: 'text.secondary'\n            },\n            children: \"Report Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"report-type-label\",\n            id: \"report-type-select\",\n            value: settings.reportType,\n            label: \"Report Type\",\n            onChange: handleReportTypeChange,\n            sx: {\n              '& .MuiSelect-select': {\n                fontSize: '0.875rem',\n                padding: '15px 10px',\n                fontWeight: 500\n              },\n              '& .MuiOutlinedInput-notchedOutline': {\n                borderColor: 'grey.400'\n              },\n              '&:hover .MuiOutlinedInput-notchedOutline': {\n                borderColor: '#1976d2'\n              },\n              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                borderColor: '#1976d2'\n              }\n            },\n            children: reportTypeOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: option.value,\n              sx: {\n                fontSize: '0.875rem',\n                fontWeight: 500\n              },\n              children: option.label\n            }, option.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), !isCurrentReportTypeSupported ? /*#__PURE__*/_jsxDEV(ComingSoonFallback, {\n      reportType: settings.reportType,\n      reportTypeOptions: reportTypeOptions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        elevation: 0,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            maxWidth: '300px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 600,\n              color: 'text.primary',\n              fontSize: '1rem'\n            },\n            children: \"Report Components\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: 'text.secondary'\n            },\n            children: \"Select which financial metrics to include in your analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '600px'\n            },\n            children: settingsOptions.map((option, index) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between',\n                py: 1,\n                borderBottom: index < settingsOptions.length - 1 ? '1px solid' : 'none',\n                borderBottomColor: 'grey.200'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: option.tooltip,\n                arrow: true,\n                placement: \"top-start\",\n                sx: {\n                  '& .MuiTooltip-tooltip': {\n                    fontSize: '0.875rem',\n                    maxWidth: '350px',\n                    lineHeight: 1.4\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1,\n                    '&:hover': {\n                      backgroundColor: alpha('#1976d2', 0.04),\n                      borderRadius: 1,\n                      padding: '4px 8px',\n                      margin: '-4px -8px'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500,\n                      color: 'text.primary',\n                      fontSize: '0.95rem',\n                      lineHeight: 1.3\n                    },\n                    children: option.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                checked: settings.incomeStatement[option.key],\n                onChange: handleSwitchChange('incomeStatement', option.key),\n                inputProps: {\n                  'aria-label': 'controlled'\n                },\n                sx: {\n                  ml: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this)]\n            }, option.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        elevation: 0,\n        sx: {\n          mb: 3,\n          width: 600,\n          transition: 'all 0.2s ease-in-out',\n          '&:hover': {\n            borderColor: '#1976d2',\n            backgroundColor: alpha('#1976d2', 0.02)\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          component: \"fieldset\",\n          sx: {\n            border: '2px solid',\n            borderColor: 'grey.400',\n            borderRadius: 1,\n            margin: 0,\n            position: 'relative',\n            backgroundColor: 'white',\n            transition: 'border-color 0.2s ease-in-out',\n            '&:focus-within': {\n              borderColor: '#1976d2'\n            },\n            '&:focus-within legend': {\n              color: '#1976d2'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            component: \"legend\",\n            sx: {\n              fontSize: '0.875rem',\n              fontWeight: 500,\n              fontFamily: '\"Roboto\",\"Helvetica\",\"Arial\",sans-serif',\n              color: 'text.secondary',\n              padding: '0 2px',\n              marginLeft: 1,\n              transition: 'color 0.2s ease-in-out'\n            },\n            children: \"Prompt\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            multiline: true,\n            rows: 5,\n            value: settings.prompt,\n            onChange: handlePromptChange,\n            placeholder: \"Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025\\r\\n\",\n            fullWidth: true,\n            variant: \"outlined\",\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                '& fieldset': {\n                  border: 'none'\n                },\n                '&:hover fieldset': {\n                  border: 'none'\n                },\n                '&.Mui-focused fieldset': {\n                  border: 'none'\n                }\n              },\n              '& .MuiInputBase-input': {\n                fontSize: '0.875rem',\n                lineHeight: 1.6,\n                '&::placeholder': {\n                  color: 'text.secondary',\n                  opacity: 0.7\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4,\n          width: '10%',\n          justifySelf: 'flex-start'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          size: \"medium\",\n          onClick: handleSave,\n          disabled: !hasChanges || isSaving,\n          fullWidth: true,\n          sx: {\n            textTransform: 'none'\n          },\n          children: isSaving ? 'SAVING...' : 'SAVE'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showSuccess,\n      autoHideDuration: 4000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        variant: \"filled\",\n        sx: {\n          backgroundColor: '#1976d2',\n          '& .MuiAlert-icon': {\n            color: 'white'\n          }\n        },\n        children: \"Settings saved successfully!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        variant: \"filled\",\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 332,\n    columnNumber: 5\n  }, this);\n};\n_s2(ReportSettings, \"lmlxA3sgJcNCTlzAIwKWLouJZg4=\", false, function () {\n  return [useTheme];\n});\n_c2 = ReportSettings;\nexport default ReportSettings;\nvar _c, _c2;\n$RefreshReg$(_c, \"ComingSoonFallback\");\n$RefreshReg$(_c2, \"ReportSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Switch", "Card", "<PERSON><PERSON>", "Divider", "TextField", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "useTheme", "alpha", "Snackbar", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "InputLabel", "CircularProgress", "TuneOutlined", "SettingsIcon", "InfoOutlined", "InfoIcon", "CheckCircleOutlined", "CheckIcon", "SaveOutlined", "SaveIcon", "ScheduleOutlined", "ComingSoonIcon", "getContentSettingsByReportType", "updateContentSettings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComingSoon<PERSON>allback", "reportType", "reportTypeOptions", "_s", "_reportTypeOptions$fi", "theme", "selectedReport<PERSON><PERSON>l", "find", "option", "value", "label", "sx", "width", "children", "elevation", "mb", "p", "backgroundColor", "border", "borderRadius", "textAlign", "display", "flexDirection", "alignItems", "gap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "max<PERSON><PERSON><PERSON>", "margin", "_c", "ReportSettings", "companyId", "_s2", "settings", "setSettings", "incomeStatement", "incomeSummary", "netIncome", "grossProfitMargin", "netProfitMargin", "roaAndRoe", "prompt", "isSaving", "setIsSaving", "isLoading", "setIsLoading", "showSuccess", "setShowSuccess", "showError", "setShowError", "errorMessage", "setErrorMessage", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "isCurrentReportTypeSupported", "loadSettings", "response", "data", "success", "Array", "isArray", "prev", "chartSettings", "promptDescription", "error", "console", "handleSwitchChange", "category", "field", "event", "target", "checked", "handlePromptChange", "handleReportTypeChange", "newReportType", "handleSave", "payload", "_error$response", "_error$response$data", "message", "handleCloseSuccess", "handleCloseError", "settingsOptions", "key", "tooltip", "justifyContent", "minHeight", "fullWidth", "size", "id", "labelId", "onChange", "padding", "borderColor", "map", "index", "py", "borderBottom", "length", "borderBottomColor", "title", "arrow", "placement", "lineHeight", "flex", "inputProps", "ml", "transition", "component", "position", "fontFamily", "marginLeft", "multiline", "rows", "placeholder", "opacity", "mt", "justifySelf", "onClick", "disabled", "textTransform", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/ReportSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  <PERSON>,\r\n  Typo<PERSON>,\r\n  Switch,\r\n  Card,\r\n  Stack,\r\n  Divider,\r\n  TextField,\r\n  Chip,\r\n  IconButton,\r\n  Tooltip,\r\n  Button,\r\n  useTheme,\r\n  alpha,\r\n  Snackbar,\r\n  Alert,\r\n  FormControl,\r\n  Select,\r\n  MenuItem,\r\n  InputLabel,\r\n  CircularProgress,\r\n} from '@mui/material';\r\n\r\nimport {\r\n  TuneOutlined as SettingsIcon,\r\n  InfoOutlined as InfoIcon,\r\n  CheckCircleOutlined as CheckIcon,\r\n  SaveOutlined as SaveIcon,\r\n  ScheduleOutlined as ComingSoonIcon,\r\n} from '@mui/icons-material';\r\n\r\nimport {\r\n  getContentSettingsByReportType,\r\n  updateContentSettings\r\n} from '../../../services/contentSettings';\r\n\r\n// Fallback component for unsupported report types\r\nconst ComingSoonFallback = ({ reportType, reportTypeOptions }) => {\r\n  const theme = useTheme();\r\n  \r\n  // Find the label for the selected report type\r\n  const selectedReportLabel = reportTypeOptions.find(\r\n    option => option.value === reportType\r\n  )?.label || reportType;\r\n\r\n  return (\r\n    <Box sx={{ width: '100%' }}>\r\n      {/* Coming Soon Banner */}\r\n      <Card\r\n        elevation={0}\r\n        sx={{\r\n          mb: 3,\r\n          p: 4,\r\n          backgroundColor: alpha('#1976d2', 0.05),\r\n          border: `1px solid ${alpha('#1976d2', 0.2)}`,\r\n          borderRadius: 2,\r\n          textAlign: 'center',\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>\r\n          <ComingSoonIcon sx={{ color: '#1976d2', fontSize: '3rem' }} />\r\n          <Box>\r\n            <Typography\r\n              variant=\"h5\"\r\n              sx={{\r\n                color: '#1976d2',\r\n                fontWeight: 600,\r\n                fontSize: '1.3rem',\r\n                mb: 1,\r\n              }}\r\n            >\r\n              {selectedReportLabel} - Coming Soon!\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body1\"\r\n              sx={{\r\n                color: 'text.secondary',\r\n                fontSize: '1rem',\r\n                maxWidth: '500px',\r\n                margin: '0 auto',\r\n              }}\r\n            >\r\n              This report type will be implemented soon.\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Card>\r\n    </Box>\r\n  );\r\n};\r\n\r\nconst ReportSettings = ({ companyId }) => {\r\n  const theme = useTheme();\r\n  const [settings, setSettings] = useState({\r\n    reportType: 'DEEPSIGHT',\r\n    incomeStatement: {\r\n      incomeSummary: true,\r\n      netIncome: true,\r\n      grossProfitMargin: true,\r\n      netProfitMargin: true,\r\n      roaAndRoe: true,\r\n    },\r\n    prompt: \"\",\r\n  });\r\n\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [showSuccess, setShowSuccess] = useState(false);\r\n  const [showError, setShowError] = useState(false);\r\n  const [errorMessage, setErrorMessage] = useState('');\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n\r\n  // Report type options\r\n  const reportTypeOptions = [\r\n    {\r\n      value: 'DEEPSIGHT',\r\n      label: 'Deepsight'\r\n    },\r\n    {\r\n      value: \"profitpulse\",\r\n      label: 'ProfitPulse (Monthly)'\r\n    },\r\n    {\r\n      value: \"kpitrack\",\r\n      label: 'KPITrack (Benchmark)'\r\n    },\r\n    {\r\n      value: \"gaap\",\r\n      label: 'GAAP Align'\r\n    },\r\n    {\r\n      value: \"fincheck\",\r\n      label: 'FinCheck (Current State)'\r\n    },\r\n    {\r\n      value: \"flowcast\",\r\n      label: 'FlowCast (13 Week)'\r\n    }\r\n  ];\r\n\r\n  // Check if the current report type is supported\r\n  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';\r\n\r\n  // Load existing settings on component mount\r\n  useEffect(() => {\r\n    const loadSettings = async () => {\r\n      if (!companyId) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');\r\n\r\n        if (response.data.success && response.data.data) {\r\n          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\r\n          \r\n          if (data) {\r\n            setSettings(prev => ({\r\n              ...prev,\r\n              incomeStatement: data.chartSettings || prev.incomeStatement,\r\n              prompt: data.promptDescription || prev.prompt,\r\n            }));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading settings:', error);\r\n        setErrorMessage('Failed to load settings');\r\n        setShowError(true);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    loadSettings();\r\n  }, [companyId]);\r\n\r\n  const handleSwitchChange = (category, field) => (event) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      [category]: {\r\n        ...prev[category],\r\n        [field]: event.target.checked\r\n      }\r\n    }));\r\n    setHasChanges(true);\r\n  };\r\n\r\n  const handlePromptChange = (event) => {\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      prompt: event.target.value\r\n    }));\r\n    setHasChanges(true);\r\n  };\r\n\r\n  // Updated handler for report type change\r\n  const handleReportTypeChange = async (event) => {\r\n    const newReportType = event.target.value;\r\n    setSettings(prev => ({\r\n      ...prev,\r\n      reportType: newReportType\r\n    }));\r\n\r\n    // If it's not DEEPSIGHT, don't try to load data - just show fallback\r\n    if (newReportType !== 'DEEPSIGHT') {\r\n      setHasChanges(false);\r\n      return;\r\n    }\r\n\r\n    // Load settings for DEEPSIGHT\r\n    if (!companyId) return;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await getContentSettingsByReportType(companyId, newReportType);\r\n\r\n      if (response.data.success && response.data.data) {\r\n        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;\r\n        \r\n        if (data) {\r\n          setSettings(prev => ({\r\n            ...prev,\r\n            incomeStatement: data.chartSettings || {\r\n              incomeSummary: true,\r\n              netIncome: true,\r\n              grossProfitMargin: true,\r\n              netProfitMargin: true,\r\n              roaAndRoe: true,\r\n            },\r\n            prompt: data.promptDescription || '',\r\n          }));\r\n        } else {\r\n          setSettings(prev => ({\r\n            ...prev,\r\n            incomeStatement: {\r\n              incomeSummary: true,\r\n              netIncome: true,\r\n              grossProfitMargin: true,\r\n              netProfitMargin: true,\r\n              roaAndRoe: true,\r\n            },\r\n            prompt: '',\r\n          }));\r\n        }\r\n      }\r\n      setHasChanges(false);\r\n    } catch (error) {\r\n      console.error('Error loading settings for report type:', error);\r\n      setErrorMessage('Failed to load settings');\r\n      setShowError(true);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!companyId || !isCurrentReportTypeSupported) return;\r\n\r\n    setIsSaving(true);\r\n\r\n    try {\r\n      const payload = {\r\n        chartSettings: settings.incomeStatement,\r\n        promptDescription: settings.prompt,\r\n      };\r\n\r\n      await updateContentSettings(companyId, settings.reportType, payload);\r\n\r\n      setHasChanges(false);\r\n      setShowSuccess(true);\r\n    } catch (error) {\r\n      console.error('Error saving settings:', error);\r\n      setErrorMessage(error.response?.data?.message || 'Failed to save settings');\r\n      setShowError(true);\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseSuccess = () => {\r\n    setShowSuccess(false);\r\n  };\r\n\r\n  const handleCloseError = () => {\r\n    setShowError(false);\r\n    setErrorMessage('');\r\n  };\r\n\r\n  const settingsOptions = [\r\n    {\r\n      key: 'incomeSummary',\r\n      label: 'Income Summary',\r\n      tooltip: 'Provides a high-level overview of total income for the selected period, showing revenue sources and major income categories.'\r\n    },\r\n    {\r\n      key: 'netIncome',\r\n      label: 'Net Income',\r\n      tooltip: 'Shows the company\\’s bottom-line profit after deducting all expenses, taxes, and interest. Indicates overall profitability for the period.'\r\n    },\r\n    {\r\n      key: 'grossProfitMargin',\r\n      label: 'Gross Profit Margin',\r\n      tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'\r\n    },\r\n    {\r\n      key: 'netProfitMargin',\r\n      label: 'Net Profit Margin',\r\n      tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'\r\n    },\r\n    {\r\n      key: 'roaAndRoe',\r\n      label: 'ROA and ROE',\r\n      tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'\r\n    },\r\n  ];\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Box sx={{\r\n        width: '100%',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        minHeight: '400px'\r\n      }}>\r\n        <CircularProgress />\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ width: '100%' }}>\r\n      {/* Header Section with Report Type Dropdown */}\r\n      <Box sx={{ mb: 2 }}>\r\n        <Box sx={{ maxWidth: '300px', mb: 3 }}>\r\n          <FormControl fullWidth size=\"small\">\r\n            <InputLabel \r\n              id=\"report-type-label\"\r\n              sx={{\r\n                fontSize: '1rem',\r\n                fontWeight: 500,\r\n                color: 'text.secondary',\r\n              }}\r\n            >\r\n              Report Type\r\n            </InputLabel>\r\n            <Select\r\n              labelId=\"report-type-label\"\r\n              id=\"report-type-select\"\r\n              value={settings.reportType}\r\n              label=\"Report Type\"\r\n              onChange={handleReportTypeChange}\r\n              sx={{\r\n                '& .MuiSelect-select': {\r\n                  fontSize: '0.875rem',\r\n                  padding: '15px 10px',\r\n                  fontWeight: 500,\r\n                },\r\n                '& .MuiOutlinedInput-notchedOutline': {\r\n                  borderColor: 'grey.400',\r\n                },\r\n                '&:hover .MuiOutlinedInput-notchedOutline': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n              }}\r\n            >\r\n              {reportTypeOptions.map((option) => (\r\n                <MenuItem \r\n                  key={option.value} \r\n                  value={option.value}\r\n                  sx={{\r\n                    fontSize: '0.875rem',\r\n                    fontWeight: 500,\r\n                  }}\r\n                >\r\n                  {option.label}\r\n                </MenuItem>\r\n              ))}\r\n            </Select>\r\n          </FormControl>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Conditional Rendering: Show fallback for unsupported report types */}\r\n      {!isCurrentReportTypeSupported ? (\r\n        <ComingSoonFallback \r\n          reportType={settings.reportType} \r\n          reportTypeOptions={reportTypeOptions} \r\n        />\r\n      ) : (\r\n        <>\r\n          {/* Report Components Section - Only for supported types */}\r\n          <Card elevation={0}>\r\n            <Box sx={{ mb: 3, maxWidth: '300px' }}>\r\n              <Typography\r\n                variant=\"subtitle1\"\r\n                sx={{\r\n                  fontWeight: 600,\r\n                  color: 'text.primary',\r\n                  fontSize: '1rem',\r\n                }}\r\n              >\r\n                Report Components\r\n              </Typography>\r\n              <Typography\r\n                variant=\"body2\"\r\n                sx={{ color: 'text.secondary' }}\r\n              >\r\n                Select which financial metrics to include in your analysis\r\n              </Typography>\r\n\r\n              <Box sx={{ maxWidth: '600px' }}>\r\n                {settingsOptions.map((option, index) => (\r\n                  <Box \r\n                    key={option.key}\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'space-between',\r\n                      py: 1,\r\n                      borderBottom: index < settingsOptions.length - 1 ? '1px solid' : 'none',\r\n                      borderBottomColor: 'grey.200',\r\n                    }}\r\n                  >\r\n                    <Tooltip \r\n                      title={option.tooltip}\r\n                      arrow\r\n                      placement=\"top-start\"\r\n                      sx={{\r\n                        '& .MuiTooltip-tooltip': {\r\n                          fontSize: '0.875rem',\r\n                          maxWidth: '350px',\r\n                          lineHeight: 1.4,\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Box sx={{ \r\n                        flex: 1,\r\n                        '&:hover': {\r\n                          backgroundColor: alpha('#1976d2', 0.04),\r\n                          borderRadius: 1,\r\n                          padding: '4px 8px',\r\n                          margin: '-4px -8px',\r\n                        }\r\n                      }}>\r\n                        <Typography\r\n                          variant=\"body1\"\r\n                          sx={{\r\n                            fontWeight: 500,\r\n                            color: 'text.primary',\r\n                            fontSize: '0.95rem',\r\n                            lineHeight: 1.3,\r\n                          }}\r\n                        >\r\n                          {option.label}\r\n                        </Typography>\r\n                      </Box>\r\n                    </Tooltip>\r\n\r\n                    <Switch\r\n                      checked={settings.incomeStatement[option.key]}\r\n                      onChange={handleSwitchChange('incomeStatement', option.key)}\r\n                      inputProps={{ 'aria-label': 'controlled' }}\r\n                      sx={{ ml: 2 }}\r\n                    />\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            </Box>\r\n          </Card>\r\n\r\n          {/* Custom Analysis Prompt Section - Only for supported types */}\r\n          <Card\r\n            elevation={0}\r\n            sx={{\r\n              mb: 3,\r\n              width: 600,\r\n              transition: 'all 0.2s ease-in-out',\r\n              '&:hover': {\r\n                borderColor: '#1976d2',\r\n                backgroundColor: alpha('#1976d2', 0.02),\r\n              },\r\n            }}\r\n          >\r\n            <Box\r\n              component=\"fieldset\"\r\n              sx={{\r\n                border: '2px solid',\r\n                borderColor: 'grey.400',\r\n                borderRadius: 1,\r\n                margin: 0,\r\n                position: 'relative',\r\n                backgroundColor: 'white',\r\n                transition: 'border-color 0.2s ease-in-out',\r\n                '&:focus-within': {\r\n                  borderColor: '#1976d2',\r\n                },\r\n                '&:focus-within legend': {\r\n                  color: '#1976d2',\r\n                },\r\n              }}\r\n            >\r\n              <Box\r\n                component=\"legend\"\r\n                sx={{\r\n                  fontSize: '0.875rem',\r\n                  fontWeight: 500,\r\n                  fontFamily: '\"Roboto\",\"Helvetica\",\"Arial\",sans-serif',\r\n                  color: 'text.secondary',\r\n                  padding: '0 2px',\r\n                  marginLeft: 1,\r\n                  transition: 'color 0.2s ease-in-out',\r\n                }}\r\n              >\r\n                Prompt\r\n              </Box>\r\n              \r\n              <TextField\r\n                multiline\r\n                rows={5}\r\n                value={settings.prompt}\r\n                onChange={handlePromptChange}\r\n                placeholder=\"Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025\r\n\"\r\n                fullWidth\r\n                variant=\"outlined\"\r\n                sx={{\r\n                  '& .MuiOutlinedInput-root': {\r\n                    '& fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                    '&:hover fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                    '&.Mui-focused fieldset': {\r\n                      border: 'none',\r\n                    },\r\n                  },\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.875rem',\r\n                    lineHeight: 1.6,\r\n                    '&::placeholder': {\r\n                      color: 'text.secondary',\r\n                      opacity: 0.7,\r\n                    },\r\n                  },\r\n                }}\r\n              />\r\n            </Box>\r\n          </Card>\r\n\r\n          {/* Save Button - Only for supported types */}\r\n          <Box sx={{ mt: 4, width: '10%', justifySelf: 'flex-start' }}>\r\n            <Button\r\n              variant=\"contained\"\r\n              size=\"medium\"\r\n              onClick={handleSave}\r\n              disabled={!hasChanges || isSaving}\r\n              fullWidth\r\n              sx={{\r\n                textTransform: 'none',\r\n              }}\r\n            >\r\n              {isSaving ? 'SAVING...' : 'SAVE'}\r\n            </Button>\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Success Snackbar */}\r\n      <Snackbar\r\n        open={showSuccess}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSuccess}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseSuccess}\r\n          severity=\"success\"\r\n          variant=\"filled\"\r\n          sx={{\r\n            backgroundColor: '#1976d2',\r\n            '& .MuiAlert-icon': {\r\n              color: 'white',\r\n            },\r\n          }}\r\n        >\r\n          Settings saved successfully!\r\n        </Alert>\r\n      </Snackbar>\r\n\r\n      {/* Error Snackbar */}\r\n      <Snackbar\r\n        open={showError}\r\n        autoHideDuration={6000}\r\n        onClose={handleCloseError}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseError}\r\n          severity=\"error\"\r\n          variant=\"filled\"\r\n        >\r\n          {errorMessage}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ReportSettings;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,gBAAgB,QACX,eAAe;AAEtB,SACEC,YAAY,IAAIC,YAAY,EAC5BC,YAAY,IAAIC,QAAQ,EACxBC,mBAAmB,IAAIC,SAAS,EAChCC,YAAY,IAAIC,QAAQ,EACxBC,gBAAgB,IAAIC,cAAc,QAC7B,qBAAqB;AAE5B,SACEC,8BAA8B,EAC9BC,qBAAqB,QAChB,mCAAmC;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChE,MAAMC,KAAK,GAAG9B,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAM+B,mBAAmB,GAAG,EAAAF,qBAAA,GAAAF,iBAAiB,CAACK,IAAI,CAChDC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKR,UAC7B,CAAC,cAAAG,qBAAA,uBAF2BA,qBAAA,CAEzBM,KAAK,KAAIT,UAAU;EAEtB,oBACEJ,OAAA,CAACjC,GAAG;IAAC+C,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,eAEzBhB,OAAA,CAAC9B,IAAI;MACH+C,SAAS,EAAE,CAAE;MACbH,EAAE,EAAE;QACFI,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,eAAe,EAAEzC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;QACvC0C,MAAM,EAAE,aAAa1C,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;QAC5C2C,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,eAEFhB,OAAA,CAACjC,GAAG;QAAC+C,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAClFhB,OAAA,CAACJ,cAAc;UAACkB,EAAE,EAAE;YAAEc,KAAK,EAAE,SAAS;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DjC,OAAA,CAACjC,GAAG;UAAAiD,QAAA,gBACFhB,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFc,KAAK,EAAE,SAAS;cAChBO,UAAU,EAAE,GAAG;cACfN,QAAQ,EAAE,QAAQ;cAClBX,EAAE,EAAE;YACN,CAAE;YAAAF,QAAA,GAEDP,mBAAmB,EAAC,iBACvB;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,OAAO;YACfpB,EAAE,EAAE;cACFc,KAAK,EAAE,gBAAgB;cACvBC,QAAQ,EAAE,MAAM;cAChBO,QAAQ,EAAE,OAAO;cACjBC,MAAM,EAAE;YACV,CAAE;YAAArB,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3B,EAAA,CApDIH,kBAAkB;EAAA,QACRzB,QAAQ;AAAA;AAAA4D,EAAA,GADlBnC,kBAAkB;AAsDxB,MAAMoC,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,GAAA;EACxC,MAAMjC,KAAK,GAAG9B,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC;IACvCuC,UAAU,EAAE,WAAW;IACvBwC,eAAe,EAAE;MACfC,aAAa,EAAE,IAAI;MACnBC,SAAS,EAAE,IAAI;MACfC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrBC,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwF,SAAS,EAAEC,YAAY,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC0F,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4F,SAAS,EAAEC,YAAY,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMwC,iBAAiB,GAAG,CACxB;IACEO,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMkD,4BAA4B,GAAGrB,QAAQ,CAACtC,UAAU,KAAK,WAAW;;EAExE;EACAtC,SAAS,CAAC,MAAM;IACd,MAAMkG,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACxB,SAAS,EAAE;MAEhB,IAAI;QACFc,YAAY,CAAC,IAAI,CAAC;QAClB,MAAMW,QAAQ,GAAG,MAAMpE,8BAA8B,CAAC2C,SAAS,EAAE,WAAW,CAAC;QAE7E,IAAIyB,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;UAC/C,MAAMA,IAAI,GAAGE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI;UAE3F,IAAIA,IAAI,EAAE;YACRvB,WAAW,CAAC2B,IAAI,KAAK;cACnB,GAAGA,IAAI;cACP1B,eAAe,EAAEsB,IAAI,CAACK,aAAa,IAAID,IAAI,CAAC1B,eAAe;cAC3DM,MAAM,EAAEgB,IAAI,CAACM,iBAAiB,IAAIF,IAAI,CAACpB;YACzC,CAAC,CAAC,CAAC;UACL;QACF;MACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/Cb,eAAe,CAAC,yBAAyB,CAAC;QAC1CF,YAAY,CAAC,IAAI,CAAC;MACpB,CAAC,SAAS;QACRJ,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDU,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACxB,SAAS,CAAC,CAAC;EAEf,MAAMmC,kBAAkB,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAMC,KAAK,IAAK;IACzDnC,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACM,QAAQ,GAAG;QACV,GAAGN,IAAI,CAACM,QAAQ,CAAC;QACjB,CAACC,KAAK,GAAGC,KAAK,CAACC,MAAM,CAACC;MACxB;IACF,CAAC,CAAC,CAAC;IACHlB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMmB,kBAAkB,GAAIH,KAAK,IAAK;IACpCnC,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPpB,MAAM,EAAE4B,KAAK,CAACC,MAAM,CAACnE;IACvB,CAAC,CAAC,CAAC;IACHkD,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMoB,sBAAsB,GAAG,MAAOJ,KAAK,IAAK;IAC9C,MAAMK,aAAa,GAAGL,KAAK,CAACC,MAAM,CAACnE,KAAK;IACxC+B,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPlE,UAAU,EAAE+E;IACd,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIA,aAAa,KAAK,WAAW,EAAE;MACjCrB,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;;IAEA;IACA,IAAI,CAACtB,SAAS,EAAE;IAEhB,IAAI;MACFc,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMW,QAAQ,GAAG,MAAMpE,8BAA8B,CAAC2C,SAAS,EAAE2C,aAAa,CAAC;MAE/E,IAAIlB,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIF,QAAQ,CAACC,IAAI,CAACA,IAAI,EAAE;QAC/C,MAAMA,IAAI,GAAGE,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACC,IAAI,CAACA,IAAI;QAE3F,IAAIA,IAAI,EAAE;UACRvB,WAAW,CAAC2B,IAAI,KAAK;YACnB,GAAGA,IAAI;YACP1B,eAAe,EAAEsB,IAAI,CAACK,aAAa,IAAI;cACrC1B,aAAa,EAAE,IAAI;cACnBC,SAAS,EAAE,IAAI;cACfC,iBAAiB,EAAE,IAAI;cACvBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAE;YACb,CAAC;YACDC,MAAM,EAAEgB,IAAI,CAACM,iBAAiB,IAAI;UACpC,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL7B,WAAW,CAAC2B,IAAI,KAAK;YACnB,GAAGA,IAAI;YACP1B,eAAe,EAAE;cACfC,aAAa,EAAE,IAAI;cACnBC,SAAS,EAAE,IAAI;cACfC,iBAAiB,EAAE,IAAI;cACvBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAE;YACb,CAAC;YACDC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;QACL;MACF;MACAY,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/Db,eAAe,CAAC,yBAAyB,CAAC;MAC1CF,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRJ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM8B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC5C,SAAS,IAAI,CAACuB,4BAA4B,EAAE;IAEjDX,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,MAAMiC,OAAO,GAAG;QACdd,aAAa,EAAE7B,QAAQ,CAACE,eAAe;QACvC4B,iBAAiB,EAAE9B,QAAQ,CAACQ;MAC9B,CAAC;MAED,MAAMpD,qBAAqB,CAAC0C,SAAS,EAAEE,QAAQ,CAACtC,UAAU,EAAEiF,OAAO,CAAC;MAEpEvB,aAAa,CAAC,KAAK,CAAC;MACpBN,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MACdb,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9Cb,eAAe,CAAC,EAAA0B,eAAA,GAAAb,KAAK,CAACR,QAAQ,cAAAqB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpB,IAAI,cAAAqB,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,yBAAyB,CAAC;MAC3E9B,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,SAAS;MACRN,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMqC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjC,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhC,YAAY,CAAC,KAAK,CAAC;IACnBE,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EAED,MAAM+B,eAAe,GAAG,CACtB;IACEC,GAAG,EAAE,eAAe;IACpB/E,KAAK,EAAE,gBAAgB;IACvBgF,OAAO,EAAE;EACX,CAAC,EACD;IACED,GAAG,EAAE,WAAW;IAChB/E,KAAK,EAAE,YAAY;IACnBgF,OAAO,EAAE;EACX,CAAC,EACD;IACED,GAAG,EAAE,mBAAmB;IACxB/E,KAAK,EAAE,qBAAqB;IAC5BgF,OAAO,EAAE;EACX,CAAC,EACD;IACED,GAAG,EAAE,iBAAiB;IACtB/E,KAAK,EAAE,mBAAmB;IAC1BgF,OAAO,EAAE;EACX,CAAC,EACD;IACED,GAAG,EAAE,WAAW;IAChB/E,KAAK,EAAE,aAAa;IACpBgF,OAAO,EAAE;EACX,CAAC,CACF;EAED,IAAIxC,SAAS,EAAE;IACb,oBACErD,OAAA,CAACjC,GAAG;MAAC+C,EAAE,EAAE;QACPC,KAAK,EAAE,MAAM;QACbS,OAAO,EAAE,MAAM;QACfsE,cAAc,EAAE,QAAQ;QACxBpE,UAAU,EAAE,QAAQ;QACpBqE,SAAS,EAAE;MACb,CAAE;MAAA/E,QAAA,eACAhB,OAAA,CAACd,gBAAgB;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEjC,OAAA,CAACjC,GAAG;IAAC+C,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAEzBhB,OAAA,CAACjC,GAAG;MAAC+C,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eACjBhB,OAAA,CAACjC,GAAG;QAAC+C,EAAE,EAAE;UAAEsB,QAAQ,EAAE,OAAO;UAAElB,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACpChB,OAAA,CAAClB,WAAW;UAACkH,SAAS;UAACC,IAAI,EAAC,OAAO;UAAAjF,QAAA,gBACjChB,OAAA,CAACf,UAAU;YACTiH,EAAE,EAAC,mBAAmB;YACtBpF,EAAE,EAAE;cACFe,QAAQ,EAAE,MAAM;cAChBM,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAZ,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACjB,MAAM;YACLoH,OAAO,EAAC,mBAAmB;YAC3BD,EAAE,EAAC,oBAAoB;YACvBtF,KAAK,EAAE8B,QAAQ,CAACtC,UAAW;YAC3BS,KAAK,EAAC,aAAa;YACnBuF,QAAQ,EAAElB,sBAAuB;YACjCpE,EAAE,EAAE;cACF,qBAAqB,EAAE;gBACrBe,QAAQ,EAAE,UAAU;gBACpBwE,OAAO,EAAE,WAAW;gBACpBlE,UAAU,EAAE;cACd,CAAC;cACD,oCAAoC,EAAE;gBACpCmE,WAAW,EAAE;cACf,CAAC;cACD,0CAA0C,EAAE;gBAC1CA,WAAW,EAAE;cACf,CAAC;cACD,gDAAgD,EAAE;gBAChDA,WAAW,EAAE;cACf;YACF,CAAE;YAAAtF,QAAA,EAEDX,iBAAiB,CAACkG,GAAG,CAAE5F,MAAM,iBAC5BX,OAAA,CAAChB,QAAQ;cAEP4B,KAAK,EAAED,MAAM,CAACC,KAAM;cACpBE,EAAE,EAAE;gBACFe,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE;cACd,CAAE;cAAAnB,QAAA,EAEDL,MAAM,CAACE;YAAK,GAPRF,MAAM,CAACC,KAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAAC8B,4BAA4B,gBAC5B/D,OAAA,CAACG,kBAAkB;MACjBC,UAAU,EAAEsC,QAAQ,CAACtC,UAAW;MAChCC,iBAAiB,EAAEA;IAAkB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,gBAEFjC,OAAA,CAAAE,SAAA;MAAAc,QAAA,gBAEEhB,OAAA,CAAC9B,IAAI;QAAC+C,SAAS,EAAE,CAAE;QAAAD,QAAA,eACjBhB,OAAA,CAACjC,GAAG;UAAC+C,EAAE,EAAE;YAAEI,EAAE,EAAE,CAAC;YAAEkB,QAAQ,EAAE;UAAQ,CAAE;UAAApB,QAAA,gBACpChB,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,WAAW;YACnBpB,EAAE,EAAE;cACFqB,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE,cAAc;cACrBC,QAAQ,EAAE;YACZ,CAAE;YAAAb,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAAChC,UAAU;YACTkE,OAAO,EAAC,OAAO;YACfpB,EAAE,EAAE;cAAEc,KAAK,EAAE;YAAiB,CAAE;YAAAZ,QAAA,EACjC;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbjC,OAAA,CAACjC,GAAG;YAAC+C,EAAE,EAAE;cAAEsB,QAAQ,EAAE;YAAQ,CAAE;YAAApB,QAAA,EAC5B2E,eAAe,CAACY,GAAG,CAAC,CAAC5F,MAAM,EAAE6F,KAAK,kBACjCxG,OAAA,CAACjC,GAAG;cAEF+C,EAAE,EAAE;gBACFU,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBoE,cAAc,EAAE,eAAe;gBAC/BW,EAAE,EAAE,CAAC;gBACLC,YAAY,EAAEF,KAAK,GAAGb,eAAe,CAACgB,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM;gBACvEC,iBAAiB,EAAE;cACrB,CAAE;cAAA5F,QAAA,gBAEFhB,OAAA,CAACxB,OAAO;gBACNqI,KAAK,EAAElG,MAAM,CAACkF,OAAQ;gBACtBiB,KAAK;gBACLC,SAAS,EAAC,WAAW;gBACrBjG,EAAE,EAAE;kBACF,uBAAuB,EAAE;oBACvBe,QAAQ,EAAE,UAAU;oBACpBO,QAAQ,EAAE,OAAO;oBACjB4E,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAhG,QAAA,eAEFhB,OAAA,CAACjC,GAAG;kBAAC+C,EAAE,EAAE;oBACPmG,IAAI,EAAE,CAAC;oBACP,SAAS,EAAE;sBACT7F,eAAe,EAAEzC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC;sBACvC2C,YAAY,EAAE,CAAC;sBACf+E,OAAO,EAAE,SAAS;sBAClBhE,MAAM,EAAE;oBACV;kBACF,CAAE;kBAAArB,QAAA,eACAhB,OAAA,CAAChC,UAAU;oBACTkE,OAAO,EAAC,OAAO;oBACfpB,EAAE,EAAE;sBACFqB,UAAU,EAAE,GAAG;sBACfP,KAAK,EAAE,cAAc;sBACrBC,QAAQ,EAAE,SAAS;sBACnBmF,UAAU,EAAE;oBACd,CAAE;oBAAAhG,QAAA,EAEDL,MAAM,CAACE;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEVjC,OAAA,CAAC/B,MAAM;gBACL+G,OAAO,EAAEtC,QAAQ,CAACE,eAAe,CAACjC,MAAM,CAACiF,GAAG,CAAE;gBAC9CQ,QAAQ,EAAEzB,kBAAkB,CAAC,iBAAiB,EAAEhE,MAAM,CAACiF,GAAG,CAAE;gBAC5DsB,UAAU,EAAE;kBAAE,YAAY,EAAE;gBAAa,CAAE;gBAC3CpG,EAAE,EAAE;kBAAEqG,EAAE,EAAE;gBAAE;cAAE;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA,GAlDGtB,MAAM,CAACiF,GAAG;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmDZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjC,OAAA,CAAC9B,IAAI;QACH+C,SAAS,EAAE,CAAE;QACbH,EAAE,EAAE;UACFI,EAAE,EAAE,CAAC;UACLH,KAAK,EAAE,GAAG;UACVqG,UAAU,EAAE,sBAAsB;UAClC,SAAS,EAAE;YACTd,WAAW,EAAE,SAAS;YACtBlF,eAAe,EAAEzC,KAAK,CAAC,SAAS,EAAE,IAAI;UACxC;QACF,CAAE;QAAAqC,QAAA,eAEFhB,OAAA,CAACjC,GAAG;UACFsJ,SAAS,EAAC,UAAU;UACpBvG,EAAE,EAAE;YACFO,MAAM,EAAE,WAAW;YACnBiF,WAAW,EAAE,UAAU;YACvBhF,YAAY,EAAE,CAAC;YACfe,MAAM,EAAE,CAAC;YACTiF,QAAQ,EAAE,UAAU;YACpBlG,eAAe,EAAE,OAAO;YACxBgG,UAAU,EAAE,+BAA+B;YAC3C,gBAAgB,EAAE;cAChBd,WAAW,EAAE;YACf,CAAC;YACD,uBAAuB,EAAE;cACvB1E,KAAK,EAAE;YACT;UACF,CAAE;UAAAZ,QAAA,gBAEFhB,OAAA,CAACjC,GAAG;YACFsJ,SAAS,EAAC,QAAQ;YAClBvG,EAAE,EAAE;cACFe,QAAQ,EAAE,UAAU;cACpBM,UAAU,EAAE,GAAG;cACfoF,UAAU,EAAE,yCAAyC;cACrD3F,KAAK,EAAE,gBAAgB;cACvByE,OAAO,EAAE,OAAO;cAChBmB,UAAU,EAAE,CAAC;cACbJ,UAAU,EAAE;YACd,CAAE;YAAApG,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENjC,OAAA,CAAC3B,SAAS;YACRoJ,SAAS;YACTC,IAAI,EAAE,CAAE;YACR9G,KAAK,EAAE8B,QAAQ,CAACQ,MAAO;YACvBkD,QAAQ,EAAEnB,kBAAmB;YAC7B0C,WAAW,EAAC,qUAC3B;YACe3B,SAAS;YACT9D,OAAO,EAAC,UAAU;YAClBpB,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1B,YAAY,EAAE;kBACZO,MAAM,EAAE;gBACV,CAAC;gBACD,kBAAkB,EAAE;kBAClBA,MAAM,EAAE;gBACV,CAAC;gBACD,wBAAwB,EAAE;kBACxBA,MAAM,EAAE;gBACV;cACF,CAAC;cACD,uBAAuB,EAAE;gBACvBQ,QAAQ,EAAE,UAAU;gBACpBmF,UAAU,EAAE,GAAG;gBACf,gBAAgB,EAAE;kBAChBpF,KAAK,EAAE,gBAAgB;kBACvBgG,OAAO,EAAE;gBACX;cACF;YACF;UAAE;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjC,OAAA,CAACjC,GAAG;QAAC+C,EAAE,EAAE;UAAE+G,EAAE,EAAE,CAAC;UAAE9G,KAAK,EAAE,KAAK;UAAE+G,WAAW,EAAE;QAAa,CAAE;QAAA9G,QAAA,eAC1DhB,OAAA,CAACvB,MAAM;UACLyD,OAAO,EAAC,WAAW;UACnB+D,IAAI,EAAC,QAAQ;UACb8B,OAAO,EAAE3C,UAAW;UACpB4C,QAAQ,EAAE,CAACnE,UAAU,IAAIV,QAAS;UAClC6C,SAAS;UACTlF,EAAE,EAAE;YACFmH,aAAa,EAAE;UACjB,CAAE;UAAAjH,QAAA,EAEDmC,QAAQ,GAAG,WAAW,GAAG;QAAM;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH,eAGDjC,OAAA,CAACpB,QAAQ;MACPsJ,IAAI,EAAE3E,WAAY;MAClB4E,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE3C,kBAAmB;MAC5B4C,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAvH,QAAA,eAExDhB,OAAA,CAACnB,KAAK;QACJuJ,OAAO,EAAE3C,kBAAmB;QAC5B+C,QAAQ,EAAC,SAAS;QAClBtG,OAAO,EAAC,QAAQ;QAChBpB,EAAE,EAAE;UACFM,eAAe,EAAE,SAAS;UAC1B,kBAAkB,EAAE;YAClBQ,KAAK,EAAE;UACT;QACF,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXjC,OAAA,CAACpB,QAAQ;MACPsJ,IAAI,EAAEzE,SAAU;MAChB0E,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE1C,gBAAiB;MAC1B2C,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAvH,QAAA,eAExDhB,OAAA,CAACnB,KAAK;QACJuJ,OAAO,EAAE1C,gBAAiB;QAC1B8C,QAAQ,EAAC,OAAO;QAChBtG,OAAO,EAAC,QAAQ;QAAAlB,QAAA,EAEf2C;MAAY;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACQ,GAAA,CAvgBIF,cAAc;EAAA,QACJ7D,QAAQ;AAAA;AAAA+J,GAAA,GADlBlG,cAAc;AAygBpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAmG,GAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}