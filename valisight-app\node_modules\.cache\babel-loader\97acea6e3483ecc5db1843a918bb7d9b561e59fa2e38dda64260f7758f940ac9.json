{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\TableOfContents.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDynamicPageNumbers, getTocItemsWithDynamicPages } from '../../../utils/pageNumbering';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TableOfContents = ({\n  headingTextStyle = {},\n  contentTextStyle = {},\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  reportData = null\n}) => {\n  _s();\n  // Use the dynamic page numbering hook\n  const dynamicPageNumbers = useDynamicPageNumbers();\n\n  // Get table of contents items with dynamic page numbers\n  const tocItems = getTocItemsWithDynamicPages(dynamicPageNumbers);\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col min-h-screen p-10  h-[297mm]\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | \", (reportData === null || reportData === void 0 ? void 0 : reportData.companyName) || \"Acme Print\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col justify-center h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center flex justify-center   pb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl font-light text-gray-800 m-0\",\n            style: headingTextStyle,\n            children: \"Table of Contents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-2xl mx-auto w-full px-20 space-y-1\",\n          children: tocItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n                flex items-baseline\t py-2 px-2\n                ${index === tocItems.length - 1 ? '' : ''}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-gray-700 font-medium flex-shrink-0\",\n              style: contentTextStyle,\n              children: item.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 mx-4 border-b-[2.5px] border-dotted border-black min-w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg text-teal-600 font-semibold flex-shrink-0\",\n              style: contentTextStyle,\n              children: item.page\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(TableOfContents, \"dggBSnHBxxkMktDr5zxU4kLF1r8=\", false, function () {\n  return [useDynamicPageNumbers];\n});\n_c = TableOfContents;\nexport default TableOfContents;\nvar _c;\n$RefreshReg$(_c, \"TableOfContents\");", "map": {"version": 3, "names": ["React", "useDynamicPageNumbers", "getTocItemsWithDynamicPages", "jsxDEV", "_jsxDEV", "TableOfContents", "headingTextStyle", "contentTextStyle", "headerTextStyle", "subHeadingTextStyle", "reportData", "_s", "dynamicPageNumbers", "tocItems", "formatHeaderPeriod", "startYear", "startMonth", "monthNames", "startMonthName", "className", "children", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYStartYear", "FYStartMonth", "companyName", "map", "item", "index", "length", "text", "page", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/TableOfContents.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useDynamicPageNumbers, getTocItemsWithDynamicPages } from '../../../utils/pageNumbering';\r\n\r\nconst TableOfContents = ({\r\n  headingTextStyle = {},\r\n  contentTextStyle = {},\r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  reportData = null\r\n}) => {\r\n  // Use the dynamic page numbering hook\r\n  const dynamicPageNumbers = useDynamicPageNumbers();\r\n\r\n  // Get table of contents items with dynamic page numbers\r\n  const tocItems = getTocItemsWithDynamicPages(dynamicPageNumbers);\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col min-h-screen p-10  h-[297mm]\">\r\n\r\n         {/* Header Section */}\r\n    <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | {reportData?.companyName || \"Acme Print\"}\r\n          </p>\r\n        </div>\r\n        \r\n        <div className='flex flex-col justify-center h-full'>\r\n\r\n        {/* Header Section */}\r\n        <div className=\"text-center flex justify-center   pb-6\">\r\n          <h1 \r\n            className=\"text-5xl font-light text-gray-800 m-0\"\r\n            style={headingTextStyle}\r\n          >\r\n            Table of Contents\r\n          </h1>\r\n        </div>\r\n\r\n       {/* Table of Contents Items */}\r\n        <div className=\"max-w-2xl mx-auto w-full px-20 space-y-1\">\r\n          {tocItems.map((item, index) => (\r\n            <div \r\n              key={index}\r\n              className={`\r\n                flex items-baseline\t py-2 px-2\r\n                ${index === tocItems.length - 1 ? '' : ''}\r\n              `}\r\n            >\r\n              <span \r\n                className=\"text-lg text-gray-700 font-medium flex-shrink-0\"\r\n                style={contentTextStyle}\r\n              >\r\n                {item.text}\r\n              </span>\r\n              <div className=\"flex-1 mx-4 border-b-[2.5px] border-dotted border-black min-w-4\"></div>\r\n              <span \r\n                className=\"text-lg text-teal-600 font-semibold flex-shrink-0\"\r\n                style={contentTextStyle}\r\n              >\r\n                {item.page}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        \r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TableOfContents;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,qBAAqB,EAAEC,2BAA2B,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElG,MAAMC,eAAe,GAAGA,CAAC;EACvBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAMC,kBAAkB,GAAGX,qBAAqB,CAAC,CAAC;;EAElD;EACA,MAAMY,QAAQ,GAAGX,2BAA2B,CAACU,kBAAkB,CAAC;EAEhE,MAAME,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAMC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACF,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAME,cAAc,GAAGD,UAAU,CAACD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGE,cAAc,IAAIH,SAAS,EAAE;EACzC,CAAC;EAED,oBACEX,OAAA;IAAKe,SAAS,EAAC,KAAK;IAAAC,QAAA,eAGlBhB,OAAA;MAAKe,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAGlGhB,OAAA;QAAKe,SAAS,EAAC,+FAA+F;QAAAC,QAAA,gBACxGhB,OAAA;UACEe,SAAS,EAAC,sCAAsC;UAChDE,KAAK,EAAEb;QAAgB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAErB,CAAC,eACLrB,OAAA;UAAGe,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEZ,mBAAoB;UAAAW,QAAA,GACjEN,kBAAkB,CAACJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgB,WAAW,EAAEhB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiB,YAAY,CAAC,EAAC,KAAG,EAAC,CAAAjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,WAAW,KAAI,YAAY;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENrB,OAAA;QAAKe,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAGpDhB,OAAA;UAAKe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDhB,OAAA;YACEe,SAAS,EAAC,uCAAuC;YACjDE,KAAK,EAAEf,gBAAiB;YAAAc,QAAA,EACzB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNrB,OAAA;UAAKe,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EACtDP,QAAQ,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxB3B,OAAA;YAEEe,SAAS,EAAE;AACzB;AACA,kBAAkBY,KAAK,KAAKlB,QAAQ,CAACmB,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;AACzD,eAAgB;YAAAZ,QAAA,gBAEFhB,OAAA;cACEe,SAAS,EAAC,iDAAiD;cAC3DE,KAAK,EAAEd,gBAAiB;cAAAa,QAAA,EAEvBU,IAAI,CAACG;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACPrB,OAAA;cAAKe,SAAS,EAAC;YAAiE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvFrB,OAAA;cACEe,SAAS,EAAC,mDAAmD;cAC7DE,KAAK,EAAEd,gBAAiB;cAAAa,QAAA,EAEvBU,IAAI,CAACI;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAlBFM,KAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAED,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CA1FIN,eAAe;EAAA,QAQQJ,qBAAqB;AAAA;AAAAkC,EAAA,GAR5C9B,eAAe;AA4FrB,eAAeA,eAAe;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}