[{"C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\layout\\LayOut.jsx": "4", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\EditReportPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Auth.jsx": "6", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetLink.jsx": "7", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ForgotPassword.jsx": "8", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\CheckEmail.jsx": "9", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\Login.jsx": "10", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetPassword.jsx": "11", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\faqs\\Faqs.jsx": "12", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Dashboard\\Dashboard.jsx": "13", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\examples\\Examples.jsx": "14", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\View.jsx": "15", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx": "16", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\faq.js": "17", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\report.js": "18", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\auth.js": "19", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\example.js": "20", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\EditReport.jsx": "21", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\TopBar.jsx": "22", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\company.js": "23", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\settings.js": "24", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Companies.jsx": "25", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Create.jsx": "26", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Reports.jsx": "27", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\BreadCrumbs.jsx": "28", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\shared.js": "29", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx": "30", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\CSVModal.jsx": "31", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\hooks\\useDebounceSearch.jsx": "32", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Edit.jsx": "33", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx": "34", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\axiosInstance.js": "35", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\cookies.js": "36", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\NoDataFound.jsx": "37", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\markets.js": "38", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportModal.jsx": "39", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx": "40", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\Table.jsx": "41", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\enums\\report.enum.js": "42", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\CustomizeReport.jsx": "43", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QboCallback\\qboCallback.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\pdf.js": "45", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx": "46", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx": "48", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx": "50", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\YearToDate.jsx": "51", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\Monthly.jsx": "52", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx": "53", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx": "55", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\CoverPage.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\pageNumbering.js": "57", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\qbo.js": "58", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\UploadableSection.jsx": "59", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportSettings.jsx": "61", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\contentSettings.js": "62", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\customizeReportService.js": "63"}, {"size": 507, "mtime": 1756456910065, "results": "64", "hashOfConfig": "65"}, {"size": 2753, "mtime": 1756464224224, "results": "66", "hashOfConfig": "65"}, {"size": 375, "mtime": 1756456910096, "results": "67", "hashOfConfig": "65"}, {"size": 305, "mtime": 1756456910067, "results": "68", "hashOfConfig": "65"}, {"size": 886, "mtime": 1756464224277, "results": "69", "hashOfConfig": "65"}, {"size": 182, "mtime": 1756456910069, "results": "70", "hashOfConfig": "65"}, {"size": 2138, "mtime": 1756464224236, "results": "71", "hashOfConfig": "65"}, {"size": 4322, "mtime": 1756456910072, "results": "72", "hashOfConfig": "65"}, {"size": 2441, "mtime": 1756456910071, "results": "73", "hashOfConfig": "65"}, {"size": 5637, "mtime": 1756464224236, "results": "74", "hashOfConfig": "65"}, {"size": 6189, "mtime": 1756464210205, "results": "75", "hashOfConfig": "65"}, {"size": 2963, "mtime": 1756456910095, "results": "76", "hashOfConfig": "65"}, {"size": 2268, "mtime": 1756456910093, "results": "77", "hashOfConfig": "65"}, {"size": 13394, "mtime": 1756464224274, "results": "78", "hashOfConfig": "65"}, {"size": 45608, "mtime": 1756818065452, "results": "79", "hashOfConfig": "65"}, {"size": 2343, "mtime": 1756464224249, "results": "80", "hashOfConfig": "65"}, {"size": 139, "mtime": 1756456910115, "results": "81", "hashOfConfig": "65"}, {"size": 801, "mtime": 1756456910118, "results": "82", "hashOfConfig": "65"}, {"size": 549, "mtime": 1756456910108, "results": "83", "hashOfConfig": "65"}, {"size": 311, "mtime": 1756456910114, "results": "84", "hashOfConfig": "65"}, {"size": 34297, "mtime": 1756464224241, "results": "85", "hashOfConfig": "65"}, {"size": 3608, "mtime": 1756464224294, "results": "86", "hashOfConfig": "65"}, {"size": 1491, "mtime": 1756456910112, "results": "87", "hashOfConfig": "65"}, {"size": 557, "mtime": 1756463597466, "results": "88", "hashOfConfig": "65"}, {"size": 13482, "mtime": 1756464224237, "results": "89", "hashOfConfig": "65"}, {"size": 26570, "mtime": 1756464224239, "results": "90", "hashOfConfig": "65"}, {"size": 12544, "mtime": 1756818358314, "results": "91", "hashOfConfig": "65"}, {"size": 1201, "mtime": 1756464224292, "results": "92", "hashOfConfig": "65"}, {"size": 688, "mtime": 1756464224299, "results": "93", "hashOfConfig": "65"}, {"size": 4023, "mtime": 1756456910091, "results": "94", "hashOfConfig": "65"}, {"size": 9364, "mtime": 1756456910082, "results": "95", "hashOfConfig": "65"}, {"size": 398, "mtime": 1756456910064, "results": "96", "hashOfConfig": "65"}, {"size": 25971, "mtime": 1756464224240, "results": "97", "hashOfConfig": "65"}, {"size": 5490, "mtime": 1756456910092, "results": "98", "hashOfConfig": "65"}, {"size": 1370, "mtime": 1756464224289, "results": "99", "hashOfConfig": "65"}, {"size": 756, "mtime": 1756464224297, "results": "100", "hashOfConfig": "65"}, {"size": 1596, "mtime": 1756456910125, "results": "101", "hashOfConfig": "65"}, {"size": 703, "mtime": 1756456910117, "results": "102", "hashOfConfig": "65"}, {"size": 23707, "mtime": 1756818793494, "results": "103", "hashOfConfig": "65"}, {"size": 6399, "mtime": 1756456910088, "results": "104", "hashOfConfig": "65"}, {"size": 4226, "mtime": 1756464224293, "results": "105", "hashOfConfig": "65"}, {"size": 191, "mtime": 1756464224235, "results": "106", "hashOfConfig": "65"}, {"size": 93055, "mtime": 1756811316720, "results": "107", "hashOfConfig": "65"}, {"size": 16373, "mtime": 1756797941810, "results": "108", "hashOfConfig": "65"}, {"size": 1331, "mtime": 1756464224290, "results": "109", "hashOfConfig": "65"}, {"size": 17093, "mtime": 1756817392125, "results": "110", "hashOfConfig": "65"}, {"size": 35402, "mtime": 1756720750889, "results": "111", "hashOfConfig": "65"}, {"size": 35046, "mtime": 1756816741743, "results": "112", "hashOfConfig": "65"}, {"size": 31898, "mtime": 1756707769296, "results": "113", "hashOfConfig": "65"}, {"size": 19506, "mtime": 1756716216299, "results": "114", "hashOfConfig": "65"}, {"size": 16880, "mtime": 1756716602567, "results": "115", "hashOfConfig": "65"}, {"size": 18116, "mtime": 1756710570276, "results": "116", "hashOfConfig": "65"}, {"size": 3072, "mtime": 1756812334379, "results": "117", "hashOfConfig": "65"}, {"size": 9630, "mtime": 1756708981945, "results": "118", "hashOfConfig": "65"}, {"size": 20717, "mtime": 1756710764156, "results": "119", "hashOfConfig": "65"}, {"size": 3302, "mtime": 1756816973707, "results": "120", "hashOfConfig": "65"}, {"size": 4323, "mtime": 1756713640371, "results": "121", "hashOfConfig": "65"}, {"size": 1198, "mtime": 1756464224291, "results": "122", "hashOfConfig": "65"}, {"size": 11921, "mtime": 1756467113937, "results": "123", "hashOfConfig": "65"}, {"size": 10932, "mtime": 1756797941807, "results": "124", "hashOfConfig": "65"}, {"size": 18654, "mtime": 1756811168674, "results": "125", "hashOfConfig": "65"}, {"size": 1226, "mtime": 1756464224289, "results": "126", "hashOfConfig": "65"}, {"size": 1117, "mtime": 1756797485414, "results": "127", "hashOfConfig": "65"}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cg5eed", {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\layout\\LayOut.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\EditReportPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Auth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetLink.jsx", ["317"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\CheckEmail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\Login.jsx", ["318"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\faqs\\Faqs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Dashboard\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\examples\\Examples.jsx", ["319"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\View.jsx", ["320", "321", "322", "323"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\faq.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\report.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\example.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\EditReport.jsx", ["324", "325", "326", "327", "328", "329", "330", "331", "332", "333"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\TopBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\company.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\settings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Companies.jsx", ["334", "335"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Create.jsx", ["336", "337"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Reports.jsx", ["338", "339"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\BreadCrumbs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\shared.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\CSVModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\hooks\\useDebounceSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Edit.jsx", ["340", "341", "342", "343"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\cookies.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\NoDataFound.jsx", ["344"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\markets.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportModal.jsx", ["345", "346", "347", "348"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx", ["349", "350"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\Table.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\enums\\report.enum.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\CustomizeReport.jsx", ["351", "352", "353", "354", "355", "356", "357", "358"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QboCallback\\qboCallback.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\pdf.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx", ["359", "360", "361"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx", ["362", "363", "364", "365"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx", ["366", "367"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx", ["368"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\YearToDate.jsx", ["369"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\Monthly.jsx", ["370"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx", ["371"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\CoverPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\pageNumbering.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\qbo.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\UploadableSection.jsx", ["372", "373", "374", "375", "376", "377", "378"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx", ["379", "380", "381", "382", "383"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportSettings.jsx", ["384", "385", "386", "387", "388", "389", "390", "391", "392", "393", "394"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\contentSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\customizeReportService.js", [], [], {"ruleId": "395", "severity": 1, "message": "396", "line": 2, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 2, "endColumn": 21}, {"ruleId": "395", "severity": 1, "message": "399", "line": 23, "column": 32, "nodeType": "397", "messageId": "398", "endLine": 23, "endColumn": 42}, {"ruleId": "400", "severity": 1, "message": "401", "line": 363, "column": 21, "nodeType": "402", "endLine": 363, "endColumn": 66}, {"ruleId": "395", "severity": 1, "message": "403", "line": 16, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 16, "endColumn": 14}, {"ruleId": "395", "severity": 1, "message": "404", "line": 31, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 31, "endColumn": 19}, {"ruleId": "395", "severity": 1, "message": "405", "line": 31, "column": 21, "nodeType": "397", "messageId": "398", "endLine": 31, "endColumn": 30}, {"ruleId": "395", "severity": 1, "message": "406", "line": 59, "column": 24, "nodeType": "397", "messageId": "398", "endLine": 59, "endColumn": 39}, {"ruleId": "407", "severity": 1, "message": "408", "line": 222, "column": 37, "nodeType": "409", "messageId": "410", "endLine": 232, "endColumn": 12}, {"ruleId": "407", "severity": 1, "message": "411", "line": 235, "column": 37, "nodeType": "409", "messageId": "410", "endLine": 376, "endColumn": 12}, {"ruleId": "395", "severity": 1, "message": "412", "line": 237, "column": 28, "nodeType": "397", "messageId": "398", "endLine": 237, "endColumn": 33}, {"ruleId": "395", "severity": 1, "message": "413", "line": 237, "column": 42, "nodeType": "397", "messageId": "398", "endLine": 237, "endColumn": 48}, {"ruleId": "414", "severity": 1, "message": "415", "line": 282, "column": 30, "nodeType": "416", "messageId": "417", "endLine": 282, "endColumn": 32}, {"ruleId": "414", "severity": 1, "message": "415", "line": 283, "column": 30, "nodeType": "416", "messageId": "417", "endLine": 283, "endColumn": 32}, {"ruleId": "414", "severity": 1, "message": "415", "line": 284, "column": 30, "nodeType": "416", "messageId": "417", "endLine": 284, "endColumn": 32}, {"ruleId": "414", "severity": 1, "message": "415", "line": 285, "column": 30, "nodeType": "416", "messageId": "417", "endLine": 285, "endColumn": 32}, {"ruleId": "418", "severity": 1, "message": "419", "line": 578, "column": 6, "nodeType": "420", "endLine": 578, "endColumn": 28, "suggestions": "421"}, {"ruleId": "395", "severity": 1, "message": "422", "line": 729, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 729, "endColumn": 21}, {"ruleId": "395", "severity": 1, "message": "423", "line": 33, "column": 8, "nodeType": "397", "messageId": "398", "endLine": 33, "endColumn": 21}, {"ruleId": "418", "severity": 1, "message": "424", "line": 138, "column": 6, "nodeType": "420", "endLine": 138, "endColumn": 58, "suggestions": "425"}, {"ruleId": "395", "severity": 1, "message": "426", "line": 29, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 29, "endColumn": 15}, {"ruleId": "395", "severity": 1, "message": "427", "line": 58, "column": 26, "nodeType": "397", "messageId": "398", "endLine": 58, "endColumn": 43}, {"ruleId": "395", "severity": 1, "message": "428", "line": 48, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 48, "endColumn": 26}, {"ruleId": "418", "severity": 1, "message": "429", "line": 133, "column": 6, "nodeType": "420", "endLine": 133, "endColumn": 38, "suggestions": "430"}, {"ruleId": "395", "severity": 1, "message": "427", "line": 48, "column": 26, "nodeType": "397", "messageId": "398", "endLine": 48, "endColumn": 43}, {"ruleId": "395", "severity": 1, "message": "431", "line": 64, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 64, "endColumn": 25}, {"ruleId": "432", "severity": 1, "message": "433", "line": 614, "column": 21, "nodeType": "434", "messageId": "417", "endLine": 614, "endColumn": 36}, {"ruleId": "432", "severity": 1, "message": "435", "line": 615, "column": 21, "nodeType": "434", "messageId": "417", "endLine": 615, "endColumn": 32}, {"ruleId": "395", "severity": 1, "message": "436", "line": 7, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 7, "endColumn": 20}, {"ruleId": "395", "severity": 1, "message": "437", "line": 6, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 6, "endColumn": 18}, {"ruleId": "395", "severity": 1, "message": "438", "line": 9, "column": 67, "nodeType": "397", "messageId": "398", "endLine": 9, "endColumn": 77}, {"ruleId": "414", "severity": 1, "message": "415", "line": 171, "column": 77, "nodeType": "416", "messageId": "417", "endLine": 171, "endColumn": 79}, {"ruleId": "414", "severity": 1, "message": "415", "line": 175, "column": 73, "nodeType": "416", "messageId": "417", "endLine": 175, "endColumn": 75}, {"ruleId": "395", "severity": 1, "message": "439", "line": 63, "column": 11, "nodeType": "397", "messageId": "398", "endLine": 63, "endColumn": 24}, {"ruleId": "418", "severity": 1, "message": "440", "line": 77, "column": 8, "nodeType": "420", "endLine": 77, "endColumn": 45, "suggestions": "441"}, {"ruleId": "395", "severity": 1, "message": "442", "line": 46, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 46, "endColumn": 25}, {"ruleId": "395", "severity": 1, "message": "443", "line": 58, "column": 10, "nodeType": "397", "messageId": "398", "endLine": 58, "endColumn": 26}, {"ruleId": "395", "severity": 1, "message": "444", "line": 58, "column": 28, "nodeType": "397", "messageId": "398", "endLine": 58, "endColumn": 47}, {"ruleId": "395", "severity": 1, "message": "445", "line": 414, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 414, "endColumn": 19}, {"ruleId": "395", "severity": 1, "message": "446", "line": 427, "column": 17, "nodeType": "397", "messageId": "398", "endLine": 427, "endColumn": 30}, {"ruleId": "395", "severity": 1, "message": "447", "line": 697, "column": 15, "nodeType": "397", "messageId": "398", "endLine": 697, "endColumn": 25}, {"ruleId": "418", "severity": 1, "message": "448", "line": 1103, "column": 6, "nodeType": "420", "endLine": 1103, "endColumn": 27, "suggestions": "449"}, {"ruleId": "418", "severity": 1, "message": "450", "line": 1119, "column": 6, "nodeType": "420", "endLine": 1119, "endColumn": 60, "suggestions": "451"}, {"ruleId": "418", "severity": 1, "message": "452", "line": 92, "column": 6, "nodeType": "420", "endLine": 92, "endColumn": 35, "suggestions": "453"}, {"ruleId": "432", "severity": 1, "message": "454", "line": 516, "column": 3, "nodeType": "434", "messageId": "417", "endLine": 516, "endColumn": 13}, {"ruleId": "395", "severity": 1, "message": "455", "line": 983, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 983, "endColumn": 25}, {"ruleId": "395", "severity": 1, "message": "455", "line": 69, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 69, "endColumn": 25}, {"ruleId": "418", "severity": 1, "message": "452", "line": 108, "column": 6, "nodeType": "420", "endLine": 108, "endColumn": 35, "suggestions": "456"}, {"ruleId": "414", "severity": 1, "message": "415", "line": 394, "column": 86, "nodeType": "416", "messageId": "417", "endLine": 394, "endColumn": 88}, {"ruleId": "414", "severity": 1, "message": "415", "line": 396, "column": 79, "nodeType": "416", "messageId": "417", "endLine": 396, "endColumn": 81}, {"ruleId": "395", "severity": 1, "message": "455", "line": 77, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 77, "endColumn": 25}, {"ruleId": "418", "severity": 1, "message": "452", "line": 142, "column": 6, "nodeType": "420", "endLine": 142, "endColumn": 23, "suggestions": "457"}, {"ruleId": "418", "severity": 1, "message": "452", "line": 77, "column": 6, "nodeType": "420", "endLine": 77, "endColumn": 21, "suggestions": "458"}, {"ruleId": "395", "severity": 1, "message": "459", "line": 103, "column": 15, "nodeType": "397", "messageId": "398", "endLine": 103, "endColumn": 30}, {"ruleId": "395", "severity": 1, "message": "459", "line": 121, "column": 15, "nodeType": "397", "messageId": "398", "endLine": 121, "endColumn": 30}, {"ruleId": "395", "severity": 1, "message": "460", "line": 121, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 121, "endColumn": 32}, {"ruleId": "395", "severity": 1, "message": "461", "line": 11, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 11, "endColumn": 19}, {"ruleId": "395", "severity": 1, "message": "462", "line": 13, "column": 8, "nodeType": "397", "messageId": "398", "endLine": 13, "endColumn": 16}, {"ruleId": "395", "severity": 1, "message": "463", "line": 14, "column": 8, "nodeType": "397", "messageId": "398", "endLine": 14, "endColumn": 18}, {"ruleId": "395", "severity": 1, "message": "464", "line": 15, "column": 8, "nodeType": "397", "messageId": "398", "endLine": 15, "endColumn": 23}, {"ruleId": "395", "severity": 1, "message": "465", "line": 80, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 80, "endColumn": 20}, {"ruleId": "395", "severity": 1, "message": "466", "line": 81, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 81, "endColumn": 17}, {"ruleId": "395", "severity": 1, "message": "467", "line": 82, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 82, "endColumn": 25}, {"ruleId": "395", "severity": 1, "message": "468", "line": 10, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 10, "endColumn": 7}, {"ruleId": "395", "severity": 1, "message": "469", "line": 18, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 18, "endColumn": 10}, {"ruleId": "395", "severity": 1, "message": "470", "line": 24, "column": 11, "nodeType": "397", "messageId": "398", "endLine": 24, "endColumn": 19}, {"ruleId": "395", "severity": 1, "message": "471", "line": 25, "column": 13, "nodeType": "397", "messageId": "398", "endLine": 25, "endColumn": 23}, {"ruleId": "395", "severity": 1, "message": "472", "line": 46, "column": 24, "nodeType": "397", "messageId": "398", "endLine": 46, "endColumn": 39}, {"ruleId": "395", "severity": 1, "message": "473", "line": 7, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 7, "endColumn": 8}, {"ruleId": "395", "severity": 1, "message": "474", "line": 8, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 8, "endColumn": 10}, {"ruleId": "395", "severity": 1, "message": "475", "line": 10, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 10, "endColumn": 7}, {"ruleId": "395", "severity": 1, "message": "476", "line": 11, "column": 3, "nodeType": "397", "messageId": "398", "endLine": 11, "endColumn": 13}, {"ruleId": "395", "severity": 1, "message": "477", "line": 26, "column": 19, "nodeType": "397", "messageId": "398", "endLine": 26, "endColumn": 31}, {"ruleId": "395", "severity": 1, "message": "470", "line": 27, "column": 19, "nodeType": "397", "messageId": "398", "endLine": 27, "endColumn": 27}, {"ruleId": "395", "severity": 1, "message": "478", "line": 28, "column": 26, "nodeType": "397", "messageId": "398", "endLine": 28, "endColumn": 35}, {"ruleId": "395", "severity": 1, "message": "479", "line": 29, "column": 19, "nodeType": "397", "messageId": "398", "endLine": 29, "endColumn": 27}, {"ruleId": "395", "severity": 1, "message": "480", "line": 40, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 40, "endColumn": 14}, {"ruleId": "395", "severity": 1, "message": "480", "line": 94, "column": 9, "nodeType": "397", "messageId": "398", "endLine": 94, "endColumn": 14}, {"ruleId": "481", "severity": 1, "message": "482", "line": 298, "column": 34, "nodeType": "483", "messageId": "484", "endLine": 298, "endColumn": 35, "suggestions": "485"}, "no-unused-vars", "'useNavigate' is defined but never used.", "Identifier", "unusedVar", "'rememberMe' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'Link' is defined but never used.", "'getCookie' is defined but never used.", "'setCookie' is defined but never used.", "'setSelectedFile' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'sectionHeaderX'.", "ArrowFunctionExpression", "unsafeRefs", "Function declared in a loop contains unsafe references to variable(s) 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'sectionHeaderX', 'isReportSummaryHeading', 'hasInsertedLineBreak', 'styledHTML', 'hasInsertedLineBreak', 'styledHTML', 'currentSection', 'currentSection', 'isReportSummaryHeading', 'isReportSummaryHeading', 'styledHTML'.", "'skewX' is assigned a value but never used.", "'scaleY' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'report?.request_type'. Either include it or remove the dependency array.", "ArrayExpression", ["486"], "'response' is assigned a value but never used.", "'FilterAltIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["487"], "'revokeAccess' is defined but never used.", "'setIsCreatedModal' is assigned a value but never used.", "'requestTypeLabels' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'companyReports'. Either include it or remove the dependency array.", ["488"], "'handleRemoveUser' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'backgroundColor'.", "ObjectExpression", "Duplicate key 'borderColor'.", "'dialogOpen' is assigned a value but never used.", "'GrRevert' is defined but never used.", "'InputLabel' is defined but never used.", "'handleAddUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. If 'fetchUsers' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["489"], "'initialSettings' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'failed' is assigned a value but never used.", "'uploadResults' is assigned a value but never used.", "'metricGrid' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchContentSettings', 'initializeDataWithConnectionCheck', and 'initializeSettings'. Either include them or remove the dependency array.", ["490"], "React Hook useEffect has missing dependencies: 'clearRedirectTimer' and 'startRedirectTimer'. Either include them or remove the dependency array.", ["491"], "React Hook useEffect has missing dependencies: 'initializeCharts' and 'isDataLoaded'. Either include them or remove the dependency array.", ["492"], "Duplicate key 'dataLabels'.", "'hasAnyUsableData' is assigned a value but never used.", ["493"], ["494"], ["495"], "'variancePercent' is assigned a value but never used.", "'isNegativePriorYear' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'SyncIcon' is defined but never used.", "'UploadIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'minutes' is assigned a value but never used.", "'ampm' is assigned a value but never used.", "'displayHours' is assigned a value but never used.", "'Fade' is defined but never used.", "'Tooltip' is defined but never used.", "'InfoIcon' is defined but never used.", "'LaunchIcon' is defined but never used.", "'setShowBenefits' is assigned a value but never used.", "'Stack' is defined but never used.", "'Divider' is defined but never used.", "'Chip' is defined but never used.", "'IconButton' is defined but never used.", "'SettingsIcon' is defined but never used.", "'CheckIcon' is defined but never used.", "'SaveIcon' is defined but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\’.", "Literal", "unnecessaryEscape", ["496", "497"], {"desc": "498", "fix": "499"}, {"desc": "500", "fix": "501"}, {"desc": "502", "fix": "503"}, {"desc": "504", "fix": "505"}, {"desc": "506", "fix": "507"}, {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, {"desc": "512", "fix": "513"}, {"desc": "514", "fix": "515"}, {"desc": "516", "fix": "517"}, {"messageId": "518", "fix": "519", "desc": "520"}, {"messageId": "521", "fix": "522", "desc": "523"}, "Update the dependencies array to be: [pdfUrl, report?.request_type, report.text]", {"range": "524", "text": "525"}, "Update the dependencies array to be: [page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", {"range": "526", "text": "527"}, "Update the dependencies array to be: [companyReports, currentCompanyId, reportDetail]", {"range": "528", "text": "529"}, "Update the dependencies array to be: [page, itemsPerPage, debouncedSearch, fetchUsers]", {"range": "530", "text": "531"}, "Update the dependencies array to be: [companyId, fetchContentSettings, initializeDataWithConnectionCheck, initializeSettings, reportId]", {"range": "532", "text": "533"}, "Update the dependencies array to be: [dataError, qboConnectionStatus, isCheckingConnection, startRedirectTimer, clearRedirectTimer]", {"range": "534", "text": "535"}, "Update the dependencies array to be: [reportData, contentSettings, isDataLoaded, initializeCharts]", {"range": "536", "text": "537"}, "Update the dependencies array to be: [fiscalData, contentSettings, isDataLoaded, initializeCharts]", {"range": "538", "text": "539"}, "Update the dependencies array to be: [initializeCharts, isDataLoaded, operationalData]", {"range": "540", "text": "541"}, "Update the dependencies array to be: [initializeCharts, isDataLoaded, liquidityData]", {"range": "542", "text": "543"}, "removeEscape", {"range": "544", "text": "545"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "546", "text": "547"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [19774, 19796], "[pdfUrl, report?.request_type, report.text]", [4476, 4528], "[page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", [3969, 4001], "[companyReports, currentCompanyId, reportDetail]", [2176, 2213], "[page, itemsPerPage, debouncedSearch, fetchUsers]", [42184, 42205], "[companyId, fetchContentSettings, initializeDataWithConnectionCheck, initializeSettings, reportId]", [42778, 42832], "[dataError, qboConnectionStatus, isCheckingConnection, startRedirectTimer, clearRedirectTimer]", [3521, 3550], "[reportData, contentSettings, isDataLoaded, initializeCharts]", [3941, 3970], "[fiscalData, contentSettings, isDataLoaded, initializeCharts]", [6707, 6724], "[initializeCharts, isDataLoaded, operationalData]", [2674, 2689], "[initializeCharts, isDataLoaded, liquidityData]", [7945, 7946], "", [7945, 7945], "\\"]