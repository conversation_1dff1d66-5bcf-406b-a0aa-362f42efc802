{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\ReportModal.jsx\",\n  _s = $RefreshSig$();\nimport { Calendar, X } from \"lucide-react\";\nimport DatePicker from \"react-datepicker\";\nimport { useState } from \"react\";\nimport \"react-datepicker/dist/react-datepicker.css\";\nimport InsertDriveFileIcon from \"@mui/icons-material/InsertDriveFile\";\nimport { GrRevert } from \"react-icons/gr\";\nimport { createRequest } from \"../../../services/report\";\nimport Swal from \"sweetalert2\";\nimport { Button, CircularProgress } from \"@mui/material\";\nimport { ReportRequest } from \"../../../enums/report.enum\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ConfirmationModal = ({\n  onBack,\n  onConfirm,\n  loading,\n  setActiveTab\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"fixed inset-0 bg-black/50 flex items-center justify-center\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white w-[400px] rounded-2xl shadow-xl p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold\",\n        children: \"Confirm to proceed!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"text-gray-500 hover:text-gray-700\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-[#2E3A44] mb-6\",\n      children: \"Have you uploaded the latest data?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: () => setActiveTab(\"companyInfo\"),\n        disabled: loading,\n        sx: {\n          width: '50%',\n          // Fixed width matching Upload/Sync buttons\n          textTransform: 'none' // Keep original text case for \"Go back\"\n        },\n        children: \"GO BACK\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: onConfirm,\n        disabled: loading,\n        sx: {\n          width: '50%',\n          // Fixed width matching Upload/Sync buttons\n          textTransform: 'none',\n          // Keep original text case for \"Yes, Continue\"\n          // Loading state styling\n          ...(loading && {\n            cursor: \"not-allowed\",\n            animation: \"pulse 2s infinite\",\n            \"@keyframes pulse\": {\n              \"0%\": {\n                opacity: 0.8\n              },\n              \"50%\": {\n                opacity: 1\n              },\n              \"100%\": {\n                opacity: 0.8\n              }\n            },\n            // Disable hover effects during loading\n            \"&:hover\": {\n              transform: \"none\"\n            }\n          }),\n          \"&:disabled\": {\n            cursor: \"not-allowed\"\n          }\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20,\n          color: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this) : \"YES, CONTINUE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 14,\n  columnNumber: 3\n}, this);\n_c = ConfirmationModal;\nconst ReportModal = ({\n  onClose,\n  companyFiles,\n  companyId,\n  companyFYEndDate,\n  onSubmitSuccess,\n  setActiveTab\n}) => {\n  _s();\n  var _companyFiles$filter$, _companyFiles$filter$2, _companyFiles$filter$3, _companyFiles$filter$4, _companyFiles$filter$5, _companyFiles$filter$6;\n  const [error, setError] = useState(\"\");\n  const [dates, setDates] = useState({\n    monthly: null,\n    Quarterly: null,\n    csfa: null,\n    gaap: null,\n    weekly: null,\n    deepsight: null\n  });\n  const [files, setFiles] = useState({\n    ar: null,\n    ap: null,\n    balance_sheet: null,\n    income_statement: null,\n    previous_flow_cash: null,\n    transaction: null\n  });\n  const [showConfirmation, setShowConfirmation] = useState(false);\n  const [selectedReport, setSelectedReport] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  // Fiscal Year Logic for DeepSight\n  const calculateFiscalYearOptions = () => {\n    if (!companyFYEndDate) return [];\n    const fyEndDate = new Date(companyFYEndDate);\n    const fyEndMonth = fyEndDate.getMonth() + 1; // Convert to 1-based month (1-12)\n    const today = new Date();\n    const currentMonth = today.getMonth() + 1;\n    const currentCalendarYear = today.getFullYear();\n\n    // Determine current fiscal year\n    let currentFY;\n    if (currentMonth <= fyEndMonth) {\n      currentFY = currentCalendarYear;\n    } else {\n      currentFY = currentCalendarYear + 1;\n    }\n\n    // Check if current FY should be enabled\n    // Current FY is enabled only after the FY end month has completed\n    const currentFYEnabled = currentMonth > fyEndMonth;\n\n    // Generate selectable years (current FY + previous 4 FYs)\n    const selectableYears = [];\n    const startYear = currentFYEnabled ? currentFY : currentFY - 1;\n    for (let i = 0; i < 5; i++) {\n      const year = startYear - i;\n      selectableYears.push({\n        year,\n        enabled: year < currentFY || year === currentFY && currentFYEnabled\n      });\n    }\n    return selectableYears.sort((a, b) => b.year - a.year); // Sort descending\n  };\n  const fiscalYearOptions = calculateFiscalYearOptions();\n\n  // Helper function to get fiscal year period display\n  const getFiscalYearPeriod = fy => {\n    if (!companyFYEndDate) return '';\n    const fyEndDate = new Date(companyFYEndDate);\n    const fyEndMonth = fyEndDate.getMonth() + 1;\n\n    // FY start is fyEndMonth + 1 of previous year\n    // FY end is fyEndMonth of current year\n    const startMonth = fyEndMonth === 12 ? 1 : fyEndMonth + 1;\n    const startYear = fyEndMonth === 12 ? fy : fy - 1;\n    const endYear = fy;\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    return `(${monthNames[startMonth - 1]} ${startYear} - ${monthNames[fyEndMonth - 1]} ${endYear})`;\n  };\n  const hasChartOfAccounts = companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.some(file => {\n    var _file$documents;\n    return (file === null || file === void 0 ? void 0 : file.type) === \"CHART_OF_ACCOUNTS\" && (file === null || file === void 0 ? void 0 : (_file$documents = file.documents) === null || _file$documents === void 0 ? void 0 : _file$documents.length) == 0;\n  });\n  const hasTrialBalance = companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.some(file => {\n    var _file$documents2;\n    return (file === null || file === void 0 ? void 0 : file.type) === \"TRIAL_BALANCE\" && (file === null || file === void 0 ? void 0 : (_file$documents2 = file.documents) === null || _file$documents2 === void 0 ? void 0 : _file$documents2.length) == 0;\n  });\n  const available_report_requests = [{\n    label: \"ProfitPulse (Monthly)\",\n    value: ReportRequest.MONTHLY\n  }, {\n    label: \"KPITrack (Benchmark)\",\n    value: ReportRequest.BENCHMARK\n  }, {\n    label: \"GAAP Align\",\n    value: ReportRequest.GAAP\n  }, {\n    label: \"FinCheck (Current State)\",\n    value: ReportRequest.CSFA\n  }, {\n    label: \"FlowCast (13 Week)\",\n    value: ReportRequest.WEEKLY\n  }, {\n    label: \"DeepSight\",\n    value: ReportRequest.DEEPSIGHT\n  }];\n  const handleFileUpload = (type, file) => {\n    if (type === \"transaction\" && file) {\n      setError(\"\");\n    }\n    if (type === \"previous_flow_cash\" && file) {\n      setError(\"\");\n    }\n    if (type === \"balance_sheet\" || type === \"income_statement\") {\n      setError(\"\");\n    }\n    if (!file) {\n      setFiles(prev => ({\n        ...prev,\n        [type]: null\n      }));\n      return;\n    }\n    const reader = new FileReader();\n    reader.onload = () => {\n      const base64Content = reader.result.split(\",\")[1]; // Extract base64 content\n      setFiles(prev => ({\n        ...prev,\n        [type]: {\n          name: file.name,\n          size: file.size,\n          base64: base64Content\n        }\n      }));\n    };\n    reader.onerror = () => {\n      console.error(\"File could not be read.\");\n    };\n    reader.readAsDataURL(file); // Read file as a data URL to get base64\n  };\n  const FileUploadSection = ({\n    type,\n    label\n  }) => {\n    const file = files[type];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-[#2E3A44] w-40 font-medium\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex justify-center ml-[8rem] \",\n        children: !file ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-24 py-2 text-blue-600 border text-sm border-blue-600 rounded hover:bg-blue-50\",\n          onClick: () => document.getElementById(`file-${type}`).click(),\n          children: \"Upload\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex min-w-[200px] items-center  justify-between border-[1px] border-slate-300 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 px-2 py-[2px] rounded\",\n            children: [/*#__PURE__*/_jsxDEV(InsertDriveFileIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-[#344054] \",\n                children: file.name.length > 12 ? file.name.slice(0, 15) + \"...\" : file.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-[#475467]\",\n                children: [\"(\", Math.round(file.size / 1024), \" KB)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleFileUpload(type, null),\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"w-5 m-1 h-5 text-gray-400 hover:text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        id: `file-${type}`,\n        type: \"file\",\n        className: \"hidden\",\n        onChange: e => handleFileUpload(type, e.target.files[0]),\n        accept: \".xlsx,.csv\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this);\n  };\n  const handleDateChange = (date, reportType) => {\n    setDates(prev => ({\n      ...prev,\n      [reportType]: date\n    }));\n  };\n  const formatDate = date => {\n    if (!date) return null;\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\n    const day = date.getDate().toString().padStart(2, \"0\");\n    const year = date.getFullYear();\n    return `${month}/${day}/${year}`;\n  };\n  const handleSubmit = () => {\n    if (selectedReport === ReportRequest.GAAP) {\n      if (!files.balance_sheet && !files.income_statement) {\n        setError(\"Both Balance Sheet and Income Statement files are required.\");\n        return; // Prevent submission if both are missing\n      } else if (!files.balance_sheet) {\n        setError(\"Balance Sheet file is required.\");\n        return;\n      } else if (!files.income_statement) {\n        setError(\"Income Statement file is required.\");\n        return;\n      }\n    }\n    if (selectedReport === ReportRequest.WEEKLY && !files.transaction) {\n      setError(\"Transaction detail file is required.\");\n      return;\n    }\n    if (selectedReport !== ReportRequest.WEEKLY) {\n      setShowConfirmation(true);\n    } else {\n      handleConfirm();\n    }\n  };\n  const handleConfirm = async () => {\n    let payload;\n    const fileArray = [];\n    if (selectedReport === ReportRequest.WEEKLY || selectedReport === ReportRequest.GAAP) {\n      Object.entries(files).forEach(([type, file]) => {\n        if (file) {\n          fileArray.push({\n            type,\n            name: file.name,\n            content: file.base64\n          });\n        }\n      });\n      payload = {\n        request_type: selectedReport === ReportRequest.WEEKLY ? ReportRequest.WEEKLY : selectedReport,\n        date: formatDate(dates[selectedReport]),\n        files: fileArray\n      };\n    } else {\n      payload = {\n        request_type: selectedReport,\n        date: formatDate(dates[selectedReport])\n      };\n    }\n    payload[\"companyId\"] = companyId;\n    setLoading(true);\n    try {\n      var _response$data;\n      const response = await createRequest(payload);\n      if ((response === null || response === void 0 ? void 0 : (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.statusCode) === 201) {\n        Swal.fire({\n          toast: true,\n          position: \"top-end\",\n          icon: \"success\",\n          title: \"Reports are sent successfully!\",\n          showConfirmButton: false,\n          timer: 1000,\n          timerProgressBar: true\n        });\n        onSubmitSuccess();\n        onClose();\n      }\n    } catch (err) {\n      Swal.fire({\n        toast: true,\n        position: \"top-end\",\n        icon: \"error\",\n        title: \"Failed to send reports in email. Please try again.\",\n        showConfirmButton: false,\n        timer: 3000,\n        timerProgressBar: true\n      });\n    } finally {\n      setLoading(false); // Hide loader after API call ends\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: showConfirmation ? /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      setActiveTab: setActiveTab,\n      onBack: () => setShowConfirmation(false),\n      onConfirm: handleConfirm,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black/50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white w-[600px] rounded-2xl shadow-xl max-h-[700px] overflow-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center p-6 border-b\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold\",\n            children: \"Request new report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-500 hover:text-gray-700\",\n            onClick: onClose,\n            children: /*#__PURE__*/_jsxDEV(X, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 space-y-4\",\n          children: [available_report_requests === null || available_report_requests === void 0 ? void 0 : available_report_requests.map(report => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center flex-1 gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"radio\",\n                name: \"reportType\",\n                id: report.value,\n                checked: selectedReport === report.value,\n                onChange: () => setSelectedReport(report.value),\n                className: \"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: report.value,\n                className: \"text-[#344054] font-medium\",\n                children: report.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this), report.value === ReportRequest.DEEPSIGHT ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"select\", {\n                value: dates[report.value] ? dates[report.value].getFullYear() : \"\",\n                onChange: e => {\n                  const selectedYear = parseInt(e.target.value);\n                  if (selectedYear) {\n                    // Check if the selected year is enabled\n                    const yearOption = fiscalYearOptions.find(opt => opt.year === selectedYear);\n                    if (yearOption && yearOption.enabled) {\n                      const yearDate = new Date(selectedYear, 0, 1); // January 1st of selected year\n                      handleDateChange(yearDate, report.value);\n                    }\n                  } else {\n                    handleDateChange(null, report.value);\n                  }\n                },\n                className: \"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white cursor-pointer\",\n                style: {\n                  backgroundImage: `url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\")`,\n                  backgroundPosition: 'right 0.5rem center',\n                  backgroundRepeat: 'no-repeat',\n                  backgroundSize: '1.5em 1.5em'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select fiscal year\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 25\n                }, this), fiscalYearOptions.map(({\n                  year,\n                  enabled\n                }) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: year,\n                  disabled: !enabled,\n                  style: {\n                    color: enabled ? 'inherit' : '#ccc',\n                    backgroundColor: enabled ? 'inherit' : '#f5f5f5'\n                  },\n                  children: [\"FY \", year, \" \", getFiscalYearPeriod(year), \" \", !enabled ? '(Not Available)' : '']\n                }, year, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 27\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-48\",\n              children: [/*#__PURE__*/_jsxDEV(DatePicker, {\n                selected: dates[report.value],\n                onChange: date => handleDateChange(date, report.value),\n                placeholderText: \"Select date\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 21\n            }, this)]\n          }, report.value, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 17\n          }, this)), selectedReport && selectedReport !== ReportRequest.DEEPSIGHT && (companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.filter(file => (file === null || file === void 0 ? void 0 : file.type) === \"TRIAL_BALANCE\")) && (companyFiles === null || companyFiles === void 0 ? void 0 : (_companyFiles$filter$ = companyFiles.filter(file => (file === null || file === void 0 ? void 0 : file.type) === \"TRIAL_BALANCE\")[0]) === null || _companyFiles$filter$ === void 0 ? void 0 : (_companyFiles$filter$2 = _companyFiles$filter$.documents) === null || _companyFiles$filter$2 === void 0 ? void 0 : _companyFiles$filter$2.length) === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-500 text-sm\",\n            children: \"Trial Balance File is required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 18\n          }, this), selectedReport === ReportRequest.GAAP && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: (companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.filter(file => (file === null || file === void 0 ? void 0 : file.type) === \"CHART_OF_ACCOUNTS\")) && (companyFiles === null || companyFiles === void 0 ? void 0 : (_companyFiles$filter$3 = companyFiles.filter(file => (file === null || file === void 0 ? void 0 : file.type) === \"CHART_OF_ACCOUNTS\")[0]) === null || _companyFiles$filter$3 === void 0 ? void 0 : (_companyFiles$filter$4 = _companyFiles$filter$3.documents) === null || _companyFiles$filter$4 === void 0 ? void 0 : _companyFiles$filter$4.length) === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm\",\n              children: \"Chart Of Account File is required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 17\n          }, this), selectedReport === ReportRequest.WEEKLY && (companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.filter(file => (file === null || file === void 0 ? void 0 : file.type) === \"TRIAL_BALANCE\")) && (companyFiles === null || companyFiles === void 0 ? void 0 : (_companyFiles$filter$5 = companyFiles.filter(file => (file === null || file === void 0 ? void 0 : file.type) === \"TRIAL_BALANCE\")[0]) === null || _companyFiles$filter$5 === void 0 ? void 0 : (_companyFiles$filter$6 = _companyFiles$filter$5.documents) === null || _companyFiles$filter$6 === void 0 ? void 0 : _companyFiles$filter$6.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 pt-4 border-t\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mb-4\",\n              children: \"(Please upload the latest data)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadSection, {\n              type: \"ar\",\n              label: \"AR aging detail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadSection, {\n              type: \"ap\",\n              label: \"AP aging detail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadSection, {\n              type: \"previous_flow_cash\",\n              label: \"Previous FlowCast Edited\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(FileUploadSection, {\n              type: \"transaction\",\n              label: \"Transaction detail\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 21\n            }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-sm\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 31\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 19\n          }, this), !hasChartOfAccounts && !hasTrialBalance && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: selectedReport === ReportRequest.GAAP && (companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.some(file => (file === null || file === void 0 ? void 0 : file.type) === \"TRIAL_BALANCE\")) && (companyFiles === null || companyFiles === void 0 ? void 0 : companyFiles.some(file => (file === null || file === void 0 ? void 0 : file.type) === \"CHART_OF_ACCOUNTS\")) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 pt-4 border-t\",\n              children: [/*#__PURE__*/_jsxDEV(FileUploadSection, {\n                type: \"balance_sheet\",\n                label: \"Balance sheet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(FileUploadSection, {\n                type: \"income_statement\",\n                label: \"Income statement (monthly)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 25\n              }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500 text-sm\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 flex gap-3 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: onClose,\n            disabled: loading,\n            sx: {\n              width: '50%',\n              // Fixed width matching Upload/Sync buttons\n              textTransform: 'uppercase'\n            },\n            children: \"CANCEL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSubmit,\n            disabled: !dates[selectedReport] || !dates[selectedReport] || loading,\n            sx: {\n              width: '50%',\n              // Fixed width matching Upload/Sync buttons\n              textTransform: 'uppercase',\n              // Loading state styling\n              ...(loading && {\n                cursor: \"not-allowed\",\n                animation: \"pulse 2s infinite\",\n                \"@keyframes pulse\": {\n                  \"0%\": {\n                    opacity: 0.8\n                  },\n                  \"50%\": {\n                    opacity: 1\n                  },\n                  \"100%\": {\n                    opacity: 0.8\n                  }\n                },\n                // Disable hover effects during loading\n                \"&:hover\": {\n                  transform: \"none\"\n                }\n              }),\n              \"&:disabled\": {\n                cursor: \"not-allowed\"\n              }\n            },\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 19\n            }, this) : \"REQUEST\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n};\n_s(ReportModal, \"adVa54t2lFnPwmjMTfnKCm72v4k=\");\n_c2 = ReportModal;\nexport default ReportModal;\nvar _c, _c2;\n$RefreshReg$(_c, \"ConfirmationModal\");\n$RefreshReg$(_c2, \"ReportModal\");", "map": {"version": 3, "names": ["Calendar", "X", "DatePicker", "useState", "InsertDriveFileIcon", "GrRevert", "createRequest", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "ReportRequest", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ConfirmationModal", "onBack", "onConfirm", "loading", "setActiveTab", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "variant", "disabled", "sx", "width", "textTransform", "color", "cursor", "animation", "opacity", "transform", "_c", "ReportModal", "onClose", "companyFiles", "companyId", "companyFYEndDate", "onSubmitSuccess", "_s", "_companyFiles$filter$", "_companyFiles$filter$2", "_companyFiles$filter$3", "_companyFiles$filter$4", "_companyFiles$filter$5", "_companyFiles$filter$6", "error", "setError", "dates", "setDates", "monthly", "Quarterly", "csfa", "gaap", "weekly", "deepsight", "files", "setFiles", "ar", "ap", "balance_sheet", "income_statement", "previous_flow_cash", "transaction", "showConfirmation", "setShowConfirmation", "selectedReport", "setSelectedReport", "setLoading", "calculateFiscalYearOptions", "fyEndDate", "Date", "fyEndMonth", "getMonth", "today", "currentMonth", "currentCalendarYear", "getFullYear", "currentFY", "currentFYEnabled", "selectableYears", "startYear", "i", "year", "push", "enabled", "sort", "a", "b", "fiscalYearOptions", "getFiscalYearPeriod", "fy", "startMonth", "endYear", "monthNames", "hasChartOfAccounts", "some", "file", "_file$documents", "type", "documents", "length", "hasTrialBalance", "_file$documents2", "available_report_requests", "label", "value", "MONTHLY", "BENCHMARK", "GAAP", "CSFA", "WEEKLY", "DEEPSIGHT", "handleFileUpload", "prev", "reader", "FileReader", "onload", "base64Content", "result", "split", "name", "base64", "onerror", "console", "readAsDataURL", "FileUploadSection", "document", "getElementById", "click", "slice", "Math", "round", "id", "onChange", "e", "target", "accept", "handleDateChange", "date", "reportType", "formatDate", "month", "toString", "padStart", "day", "getDate", "handleSubmit", "handleConfirm", "payload", "fileArray", "Object", "entries", "for<PERSON>ach", "content", "request_type", "_response$data", "response", "data", "statusCode", "fire", "toast", "position", "icon", "title", "showConfirmButton", "timer", "timerP<PERSON>ressBar", "err", "map", "report", "checked", "htmlFor", "selected<PERSON>ear", "parseInt", "yearOption", "find", "opt", "yearDate", "style", "backgroundImage", "backgroundPosition", "backgroundRepeat", "backgroundSize", "backgroundColor", "selected", "placeholderText", "filter", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/ReportModal.jsx"], "sourcesContent": ["import { Calendar, X } from \"lucide-react\";\r\nimport DatePicker from \"react-datepicker\";\r\nimport { useState } from \"react\";\r\nimport \"react-datepicker/dist/react-datepicker.css\";\r\nimport InsertDriveFileIcon from \"@mui/icons-material/InsertDriveFile\";\r\nimport { GrRevert } from \"react-icons/gr\";\r\nimport { createRequest } from \"../../../services/report\";\r\nimport Swal from \"sweetalert2\";\r\nimport { Button, CircularProgress } from \"@mui/material\";\r\nimport { ReportRequest } from \"../../../enums/report.enum\";\r\n\r\n\r\nconst ConfirmationModal = ({ onBack, onConfirm, loading, setActiveTab }) => (\r\n  <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center\">\r\n    <div className=\"bg-white w-[400px] rounded-2xl shadow-xl p-6\">\r\n      <div className=\"flex justify-between items-center mb-2\">\r\n        <h2 className=\"text-xl font-semibold\">Confirm to proceed!</h2>\r\n        <button onClick={onBack} className=\"text-gray-500 hover:text-gray-700\">\r\n          <X size={20} />\r\n        </button>\r\n      </div>\r\n      <p className=\"text-[#2E3A44] mb-6\">Have you uploaded the latest data?</p>\r\n      <div className=\"flex gap-3\">\r\n        \r\n        <Button\r\n          variant=\"outlined\"\r\n          onClick={() => setActiveTab(\"companyInfo\")}\r\n          disabled={loading}\r\n          sx={{\r\n            width: '50%', // Fixed width matching Upload/Sync buttons\r\n            textTransform: 'none', // Keep original text case for \"Go back\"\r\n          }}\r\n        >\r\n          {/* <GrRevert size={20} style={{ marginRight: '8px' }} /> */}\r\n          GO BACK\r\n        </Button>\r\n\r\n        <Button\r\n          variant=\"contained\"\r\n          color=\"primary\"\r\n          onClick={onConfirm}\r\n          disabled={loading}\r\n          sx={{\r\n            width: '50%', // Fixed width matching Upload/Sync buttons\r\n            textTransform: 'none', // Keep original text case for \"Yes, Continue\"\r\n            // Loading state styling\r\n            ...(loading && {\r\n              cursor: \"not-allowed\",\r\n              animation: \"pulse 2s infinite\",\r\n              \"@keyframes pulse\": {\r\n                \"0%\": { opacity: 0.8 },\r\n                \"50%\": { opacity: 1 },\r\n                \"100%\": { opacity: 0.8 },\r\n              },\r\n              // Disable hover effects during loading\r\n              \"&:hover\": {\r\n                transform: \"none\",\r\n              },\r\n            }),\r\n            \"&:disabled\": {\r\n              cursor: \"not-allowed\",\r\n            },\r\n          }}\r\n        >\r\n          {loading ? (\r\n            <CircularProgress size={20} color=\"inherit\" />\r\n          ) : (\r\n            \"YES, CONTINUE\"\r\n          )}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst ReportModal = ({\r\n  onClose,\r\n  companyFiles,\r\n  companyId,\r\n  companyFYEndDate,\r\n  onSubmitSuccess,\r\n  setActiveTab,\r\n}) => {\r\n  const [error, setError] = useState(\"\");\r\n  const [dates, setDates] = useState({\r\n    monthly: null,\r\n    Quarterly: null,\r\n    csfa: null,\r\n    gaap: null,\r\n    weekly: null,\r\n    deepsight: null,\r\n  });\r\n  const [files, setFiles] = useState({\r\n    ar: null,\r\n    ap: null,\r\n    balance_sheet: null,\r\n    income_statement: null,\r\n    previous_flow_cash: null,\r\n    transaction: null,\r\n  });\r\n\r\n  const [showConfirmation, setShowConfirmation] = useState(false);\r\n  const [selectedReport, setSelectedReport] = useState(\"\");\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // Fiscal Year Logic for DeepSight\r\n  const calculateFiscalYearOptions = () => {\r\n    if (!companyFYEndDate) return [];\r\n\r\n    const fyEndDate = new Date(companyFYEndDate);\r\n    const fyEndMonth = fyEndDate.getMonth() + 1; // Convert to 1-based month (1-12)\r\n    const today = new Date();\r\n    const currentMonth = today.getMonth() + 1;\r\n    const currentCalendarYear = today.getFullYear();\r\n\r\n    // Determine current fiscal year\r\n    let currentFY;\r\n    if (currentMonth <= fyEndMonth) {\r\n      currentFY = currentCalendarYear;\r\n    } else {\r\n      currentFY = currentCalendarYear + 1;\r\n    }\r\n\r\n    // Check if current FY should be enabled\r\n    // Current FY is enabled only after the FY end month has completed\r\n    const currentFYEnabled = currentMonth > fyEndMonth;\r\n\r\n    // Generate selectable years (current FY + previous 4 FYs)\r\n    const selectableYears = [];\r\n    const startYear = currentFYEnabled ? currentFY : currentFY - 1;\r\n\r\n    for (let i = 0; i < 5; i++) {\r\n      const year = startYear - i;\r\n      selectableYears.push({\r\n        year,\r\n        enabled: year < currentFY || (year === currentFY && currentFYEnabled)\r\n      });\r\n    }\r\n\r\n    return selectableYears.sort((a, b) => b.year - a.year); // Sort descending\r\n  };\r\n\r\n  const fiscalYearOptions = calculateFiscalYearOptions();\r\n\r\n  // Helper function to get fiscal year period display\r\n  const getFiscalYearPeriod = (fy) => {\r\n    if (!companyFYEndDate) return '';\r\n\r\n    const fyEndDate = new Date(companyFYEndDate);\r\n    const fyEndMonth = fyEndDate.getMonth() + 1;\r\n\r\n    // FY start is fyEndMonth + 1 of previous year\r\n    // FY end is fyEndMonth of current year\r\n    const startMonth = fyEndMonth === 12 ? 1 : fyEndMonth + 1;\r\n    const startYear = fyEndMonth === 12 ? fy : fy - 1;\r\n    const endYear = fy;\r\n\r\n    const monthNames = [\r\n      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\r\n      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'\r\n    ];\r\n\r\n    return `(${monthNames[startMonth - 1]} ${startYear} - ${monthNames[fyEndMonth - 1]} ${endYear})`;\r\n  };\r\n\r\n  const hasChartOfAccounts = companyFiles?.some(\r\n    (file) => file?.type === \"CHART_OF_ACCOUNTS\" && file?.documents?.length == 0\r\n  );\r\n\r\n  const hasTrialBalance = companyFiles?.some(\r\n    (file) => file?.type === \"TRIAL_BALANCE\" && file?.documents?.length == 0\r\n  );\r\n\r\n  const available_report_requests = [\r\n    {\r\n      label: \"ProfitPulse (Monthly)\",\r\n      value: ReportRequest.MONTHLY,\r\n    },\r\n    {\r\n      label: \"KPITrack (Benchmark)\",\r\n      value: ReportRequest.BENCHMARK,\r\n    },\r\n    {\r\n      label: \"GAAP Align\",\r\n      value: ReportRequest.GAAP,\r\n    },\r\n    {\r\n      label: \"FinCheck (Current State)\",\r\n      value: ReportRequest.CSFA,\r\n    },\r\n    {\r\n      label: \"FlowCast (13 Week)\",\r\n      value: ReportRequest.WEEKLY,\r\n    },\r\n    {\r\n      label: \"DeepSight\",\r\n      value: ReportRequest.DEEPSIGHT,\r\n    }\r\n  ];\r\n\r\n  const handleFileUpload = (type, file) => {\r\n    if (type === \"transaction\" && file) {\r\n      setError(\"\");\r\n    }\r\n\r\n    if (type === \"previous_flow_cash\" && file) {\r\n      setError(\"\");\r\n    }\r\n    if (type === \"balance_sheet\" || type === \"income_statement\") {\r\n      setError(\"\");\r\n    }\r\n\r\n    if (!file) {\r\n      setFiles((prev) => ({\r\n        ...prev,\r\n        [type]: null,\r\n      }));\r\n      return;\r\n    }\r\n\r\n    const reader = new FileReader();\r\n\r\n    reader.onload = () => {\r\n      const base64Content = reader.result.split(\",\")[1]; // Extract base64 content\r\n      setFiles((prev) => ({\r\n        ...prev,\r\n        [type]: {\r\n          name: file.name,\r\n          size: file.size,\r\n          base64: base64Content,\r\n        },\r\n      }));\r\n    };\r\n\r\n    reader.onerror = () => {\r\n      console.error(\"File could not be read.\");\r\n    };\r\n\r\n    reader.readAsDataURL(file); // Read file as a data URL to get base64\r\n  };\r\n\r\n  const FileUploadSection = ({ type, label }) => {\r\n    const file = files[type];\r\n    return (\r\n      <div className=\"flex items-center mb-4\">\r\n        <span className=\"text-[#2E3A44] w-40 font-medium\">{label}</span>\r\n        <div className=\"flex-1 flex justify-center ml-[8rem] \">\r\n          {!file ? (\r\n            <button\r\n              className=\"w-24 py-2 text-blue-600 border text-sm border-blue-600 rounded hover:bg-blue-50\"\r\n              onClick={() => document.getElementById(`file-${type}`).click()}\r\n            >\r\n              Upload\r\n            </button>\r\n          ) : (\r\n            <div className=\"flex min-w-[200px] items-center  justify-between border-[1px] border-slate-300 rounded-lg\">\r\n              <div className=\"flex items-center gap-2 px-2 py-[2px] rounded\">\r\n                <InsertDriveFileIcon />\r\n                <div className=\"flex flex-col\">\r\n                  <p className=\"text-sm text-[#344054] \">\r\n                    {file.name.length > 12\r\n                      ? file.name.slice(0, 15) + \"...\"\r\n                      : file.name}\r\n                  </p>\r\n                  <p className=\"text-sm text-[#475467]\">\r\n                    ({Math.round(file.size / 1024)} KB)\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <button onClick={() => handleFileUpload(type, null)}>\r\n                <X className=\"w-5 m-1 h-5 text-gray-400 hover:text-gray-600\" />\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n        <input\r\n          id={`file-${type}`}\r\n          type=\"file\"\r\n          className=\"hidden\"\r\n          onChange={(e) => handleFileUpload(type, e.target.files[0])}\r\n          accept=\".xlsx,.csv\"\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const handleDateChange = (date, reportType) => {\r\n    setDates((prev) => ({\r\n      ...prev,\r\n      [reportType]: date,\r\n    }));\r\n  };\r\n\r\n\r\n\r\n  const formatDate = (date) => {\r\n    if (!date) return null;\r\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n    const day = date.getDate().toString().padStart(2, \"0\");\r\n    const year = date.getFullYear();\r\n    return `${month}/${day}/${year}`;\r\n  };\r\n\r\n  const handleSubmit = () => {\r\n    if (selectedReport === ReportRequest.GAAP) {\r\n      if (!files.balance_sheet && !files.income_statement) {\r\n        setError(\"Both Balance Sheet and Income Statement files are required.\");\r\n        return; // Prevent submission if both are missing\r\n      } else if (!files.balance_sheet) {\r\n        setError(\"Balance Sheet file is required.\");\r\n        return;\r\n      } else if (!files.income_statement) {\r\n        setError(\"Income Statement file is required.\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    if (selectedReport === ReportRequest.WEEKLY && !files.transaction) {\r\n      setError(\"Transaction detail file is required.\");\r\n      return;\r\n    }\r\n\r\n    if (selectedReport !== ReportRequest.WEEKLY) {\r\n      setShowConfirmation(true);\r\n    } else {\r\n      handleConfirm();\r\n    }\r\n  };\r\n\r\n  const handleConfirm = async () => {\r\n    let payload;\r\n    const fileArray = [];\r\n\r\n    if (\r\n      selectedReport === ReportRequest.WEEKLY ||\r\n      selectedReport === ReportRequest.GAAP\r\n    ) {\r\n      Object.entries(files).forEach(([type, file]) => {\r\n        if (file) {\r\n          fileArray.push({\r\n            type,\r\n            name: file.name,\r\n            content: file.base64,\r\n          });\r\n        }\r\n      });\r\n\r\n      payload = {\r\n        request_type:\r\n          selectedReport === ReportRequest.WEEKLY\r\n            ? ReportRequest.WEEKLY\r\n            : selectedReport,\r\n        date: formatDate(dates[selectedReport]),\r\n        files: fileArray,\r\n      };\r\n    } else {\r\n      payload = {\r\n        request_type: selectedReport,\r\n        date: formatDate(dates[selectedReport]),\r\n      };\r\n    }\r\n\r\n    payload[\"companyId\"] = companyId;\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await createRequest(payload);\r\n      if (response?.data?.statusCode === 201) {\r\n        Swal.fire({\r\n          toast: true,\r\n          position: \"top-end\",\r\n          icon: \"success\",\r\n          title: \"Reports are sent successfully!\",\r\n          showConfirmButton: false,\r\n          timer: 1000,\r\n          timerProgressBar: true,\r\n        });\r\n        onSubmitSuccess();\r\n        onClose();\r\n      }\r\n    } catch (err) {\r\n      Swal.fire({\r\n        toast: true,\r\n        position: \"top-end\",\r\n        icon: \"error\",\r\n        title: \"Failed to send reports in email. Please try again.\",\r\n        showConfirmButton: false,\r\n        timer: 3000,\r\n        timerProgressBar: true,\r\n      });\r\n    } finally {\r\n      setLoading(false); // Hide loader after API call ends\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showConfirmation ? (\r\n        <ConfirmationModal\r\n          setActiveTab={setActiveTab}\r\n          onBack={() => setShowConfirmation(false)}\r\n          onConfirm={handleConfirm}\r\n          loading={loading}\r\n        />\r\n      ) : (\r\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center\">\r\n          <div className=\"bg-white w-[600px] rounded-2xl shadow-xl max-h-[700px] overflow-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-200\">\r\n            <div className=\"flex justify-between items-center p-6 border-b\">\r\n              <h2 className=\"text-xl font-semibold\">Request new report</h2>\r\n              <button\r\n                className=\"text-gray-500 hover:text-gray-700\"\r\n                onClick={onClose}\r\n              >\r\n                <X size={20} />\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"p-6 space-y-4\">\r\n              {available_report_requests?.map((report) => (\r\n                <div key={report.value} className=\"flex items-center gap-4\">\r\n                  <div className=\"flex items-center flex-1 gap-3\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"reportType\"\r\n                      id={report.value}\r\n                      checked={selectedReport === report.value}\r\n                      onChange={() => setSelectedReport(report.value)}\r\n                      className=\"w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-600\"\r\n                    />\r\n                    <label\r\n                      htmlFor={report.value}\r\n                      className=\"text-[#344054] font-medium\"\r\n                    >\r\n                      {report.label}\r\n                    </label>\r\n                  </div>\r\n\r\n                  {/* Conditionally render Year Picker for DeepSight or regular DatePicker for others */}\r\n                  {report.value === ReportRequest.DEEPSIGHT ? (\r\n                    <div className=\"relative w-48\">\r\n                      <select\r\n                        value={dates[report.value] ? dates[report.value].getFullYear() : \"\"}\r\n                        onChange={(e) => {\r\n                          const selectedYear = parseInt(e.target.value);\r\n                          if (selectedYear) {\r\n                            // Check if the selected year is enabled\r\n                            const yearOption = fiscalYearOptions.find(opt => opt.year === selectedYear);\r\n                            if (yearOption && yearOption.enabled) {\r\n                              const yearDate = new Date(selectedYear, 0, 1); // January 1st of selected year\r\n                              handleDateChange(yearDate, report.value);\r\n                            }\r\n                          } else {\r\n                            handleDateChange(null, report.value);\r\n                          }\r\n                        }}\r\n                        className=\"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white cursor-pointer\"\r\n                        style={{\r\n                          backgroundImage: `url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\")`,\r\n                          backgroundPosition: 'right 0.5rem center',\r\n                          backgroundRepeat: 'no-repeat',\r\n                          backgroundSize: '1.5em 1.5em'\r\n                        }}\r\n                      >\r\n                        <option value=\"\">Select fiscal year</option>\r\n                        {fiscalYearOptions.map(({ year, enabled }) => (\r\n                          <option\r\n                            key={year}\r\n                            value={year}\r\n                            disabled={!enabled}\r\n                            style={{\r\n                              color: enabled ? 'inherit' : '#ccc',\r\n                              backgroundColor: enabled ? 'inherit' : '#f5f5f5'\r\n                            }}\r\n                          >\r\n                            FY {year} {getFiscalYearPeriod(year)} {!enabled ? '(Not Available)' : ''}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"relative w-48\">\r\n                      <DatePicker\r\n                        selected={dates[report.value]}\r\n                        onChange={(date) => handleDateChange(date, report.value)}\r\n                        placeholderText=\"Select date\"\r\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                      />\r\n                      <Calendar\r\n                        className=\"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400\"\r\n                        size={20}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n\r\n              {/* Show Trial Balance error only when a report type is selected and it requires trial balance */}\r\n              {selectedReport && \r\n               selectedReport !== ReportRequest.DEEPSIGHT &&\r\n               companyFiles?.filter((file) => file?.type === \"TRIAL_BALANCE\") &&\r\n               companyFiles?.filter(\r\n                 (file) => file?.type === \"TRIAL_BALANCE\"\r\n               )[0]?.documents?.length === 0 && (\r\n                 <p className=\"text-red-500 text-sm\">\r\n                   Trial Balance File is required\r\n                 </p>\r\n               )}\r\n\r\n              {/* Show Chart of Accounts error only for GAAP report */}\r\n              {selectedReport === ReportRequest.GAAP && (\r\n                <div>\r\n                  {companyFiles?.filter(\r\n                    (file) => file?.type === \"CHART_OF_ACCOUNTS\"\r\n                  ) &&\r\n                    companyFiles?.filter(\r\n                      (file) => file?.type === \"CHART_OF_ACCOUNTS\"\r\n                    )[0]?.documents?.length === 0 && (\r\n                      <p className=\"text-red-500 text-sm\">\r\n                        Chart Of Account File is required\r\n                      </p>\r\n                    )}\r\n                </div>\r\n              )}\r\n\r\n              {selectedReport === ReportRequest.WEEKLY &&\r\n                companyFiles?.filter(\r\n                  (file) => file?.type === \"TRIAL_BALANCE\"\r\n                ) &&\r\n                companyFiles?.filter(\r\n                  (file) => file?.type === \"TRIAL_BALANCE\"\r\n                )[0]?.documents?.length > 0 && (\r\n                  <div className=\"mt-6 pt-4 border-t\">\r\n                    <p className=\"text-sm text-gray-500 mb-4\">\r\n                      (Please upload the latest data)\r\n                    </p>\r\n                    <FileUploadSection type=\"ar\" label=\"AR aging detail\" />\r\n                    <FileUploadSection type=\"ap\" label=\"AP aging detail\" />\r\n                    <FileUploadSection\r\n                      type=\"previous_flow_cash\"\r\n                      label=\"Previous FlowCast Edited\"\r\n                    />\r\n\r\n                    <FileUploadSection\r\n                      type=\"transaction\"\r\n                      label=\"Transaction detail\"\r\n                    />\r\n                    {error && <p className=\"text-red-500 text-sm\">{error}</p>}\r\n                  </div>\r\n                )}\r\n\r\n              {!hasChartOfAccounts && !hasTrialBalance && (\r\n                <div>\r\n                  {selectedReport === ReportRequest.GAAP &&\r\n                    companyFiles?.some(\r\n                      (file) => file?.type === \"TRIAL_BALANCE\"\r\n                    ) &&\r\n                    companyFiles?.some(\r\n                      (file) => file?.type === \"CHART_OF_ACCOUNTS\"\r\n                    ) && (\r\n                      <div className=\"mt-6 pt-4 border-t\">\r\n                        <FileUploadSection\r\n                          type=\"balance_sheet\"\r\n                          label=\"Balance sheet\"\r\n                        />\r\n                        <FileUploadSection\r\n                          type=\"income_statement\"\r\n                          label=\"Income statement (monthly)\"\r\n                        />\r\n                        {error && (\r\n                          <p className=\"text-red-500 text-sm\">{error}</p>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"p-6 flex gap-3 border-t\">\r\n              <Button\r\n                variant=\"outlined\"\r\n                onClick={onClose}\r\n                disabled={loading}\r\n                sx={{\r\n                  width: '50%', // Fixed width matching Upload/Sync buttons\r\n                  textTransform: 'uppercase',\r\n                }}\r\n              >\r\n                CANCEL\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                onClick={handleSubmit}\r\n                disabled={!dates[selectedReport] || !dates[selectedReport] || loading}\r\n                sx={{\r\n                  width: '50%', // Fixed width matching Upload/Sync buttons\r\n                  textTransform: 'uppercase',\r\n                  // Loading state styling\r\n                  ...(loading && {\r\n                    cursor: \"not-allowed\",\r\n                    animation: \"pulse 2s infinite\",\r\n                    \"@keyframes pulse\": {\r\n                      \"0%\": { opacity: 0.8 },\r\n                      \"50%\": { opacity: 1 },\r\n                      \"100%\": { opacity: 0.8 },\r\n                    },\r\n                    // Disable hover effects during loading\r\n                    \"&:hover\": {\r\n                      transform: \"none\",\r\n                    },\r\n                  }),\r\n                  \"&:disabled\": {\r\n                    cursor: \"not-allowed\",\r\n                  },\r\n                }}\r\n              >\r\n                {loading ? (\r\n                  <CircularProgress size={20} color=\"inherit\" />\r\n                ) : ( \r\n                  \"REQUEST\"\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ReportModal;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,CAAC,QAAQ,cAAc;AAC1C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAO,4CAA4C;AACnD,OAAOC,mBAAmB,MAAM,qCAAqC;AACrE,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,MAAM,EAAEC,gBAAgB,QAAQ,eAAe;AACxD,SAASC,aAAa,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG3D,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAa,CAAC,kBACrEP,OAAA;EAAKQ,SAAS,EAAC,4DAA4D;EAAAC,QAAA,eACzET,OAAA;IAAKQ,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DT,OAAA;MAAKQ,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDT,OAAA;QAAIQ,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9Db,OAAA;QAAQc,OAAO,EAAEV,MAAO;QAACI,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eACpET,OAAA,CAACX,CAAC;UAAC0B,IAAI,EAAE;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNb,OAAA;MAAGQ,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAC;IAAkC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eACzEb,OAAA;MAAKQ,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAEzBT,OAAA,CAACJ,MAAM;QACLoB,OAAO,EAAC,UAAU;QAClBF,OAAO,EAAEA,CAAA,KAAMP,YAAY,CAAC,aAAa,CAAE;QAC3CU,QAAQ,EAAEX,OAAQ;QAClBY,EAAE,EAAE;UACFC,KAAK,EAAE,KAAK;UAAE;UACdC,aAAa,EAAE,MAAM,CAAE;QACzB,CAAE;QAAAX,QAAA,EAE2D;MAE/D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETb,OAAA,CAACJ,MAAM;QACLoB,OAAO,EAAC,WAAW;QACnBK,KAAK,EAAC,SAAS;QACfP,OAAO,EAAET,SAAU;QACnBY,QAAQ,EAAEX,OAAQ;QAClBY,EAAE,EAAE;UACFC,KAAK,EAAE,KAAK;UAAE;UACdC,aAAa,EAAE,MAAM;UAAE;UACvB;UACA,IAAId,OAAO,IAAI;YACbgB,MAAM,EAAE,aAAa;YACrBC,SAAS,EAAE,mBAAmB;YAC9B,kBAAkB,EAAE;cAClB,IAAI,EAAE;gBAAEC,OAAO,EAAE;cAAI,CAAC;cACtB,KAAK,EAAE;gBAAEA,OAAO,EAAE;cAAE,CAAC;cACrB,MAAM,EAAE;gBAAEA,OAAO,EAAE;cAAI;YACzB,CAAC;YACD;YACA,SAAS,EAAE;cACTC,SAAS,EAAE;YACb;UACF,CAAC,CAAC;UACF,YAAY,EAAE;YACZH,MAAM,EAAE;UACV;QACF,CAAE;QAAAb,QAAA,EAEDH,OAAO,gBACNN,OAAA,CAACH,gBAAgB;UAACkB,IAAI,EAAE,EAAG;UAACM,KAAK,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAE9C;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACa,EAAA,GA7DIvB,iBAAiB;AA+DvB,MAAMwB,WAAW,GAAGA,CAAC;EACnBC,OAAO;EACPC,YAAY;EACZC,SAAS;EACTC,gBAAgB;EAChBC,eAAe;EACfzB;AACF,CAAC,KAAK;EAAA0B,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC;IACjCqD,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC;IACjC6D,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAE,IAAI;IACtBC,kBAAkB,EAAE,IAAI;IACxBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACe,OAAO,EAAEwD,UAAU,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAMwE,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAAChC,gBAAgB,EAAE,OAAO,EAAE;IAEhC,MAAMiC,SAAS,GAAG,IAAIC,IAAI,CAAClC,gBAAgB,CAAC;IAC5C,MAAMmC,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,MAAMC,KAAK,GAAG,IAAIH,IAAI,CAAC,CAAC;IACxB,MAAMI,YAAY,GAAGD,KAAK,CAACD,QAAQ,CAAC,CAAC,GAAG,CAAC;IACzC,MAAMG,mBAAmB,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;;IAE/C;IACA,IAAIC,SAAS;IACb,IAAIH,YAAY,IAAIH,UAAU,EAAE;MAC9BM,SAAS,GAAGF,mBAAmB;IACjC,CAAC,MAAM;MACLE,SAAS,GAAGF,mBAAmB,GAAG,CAAC;IACrC;;IAEA;IACA;IACA,MAAMG,gBAAgB,GAAGJ,YAAY,GAAGH,UAAU;;IAElD;IACA,MAAMQ,eAAe,GAAG,EAAE;IAC1B,MAAMC,SAAS,GAAGF,gBAAgB,GAAGD,SAAS,GAAGA,SAAS,GAAG,CAAC;IAE9D,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMC,IAAI,GAAGF,SAAS,GAAGC,CAAC;MAC1BF,eAAe,CAACI,IAAI,CAAC;QACnBD,IAAI;QACJE,OAAO,EAAEF,IAAI,GAAGL,SAAS,IAAKK,IAAI,KAAKL,SAAS,IAAIC;MACtD,CAAC,CAAC;IACJ;IAEA,OAAOC,eAAe,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,IAAI,GAAGI,CAAC,CAACJ,IAAI,CAAC,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMM,iBAAiB,GAAGpB,0BAA0B,CAAC,CAAC;;EAEtD;EACA,MAAMqB,mBAAmB,GAAIC,EAAE,IAAK;IAClC,IAAI,CAACtD,gBAAgB,EAAE,OAAO,EAAE;IAEhC,MAAMiC,SAAS,GAAG,IAAIC,IAAI,CAAClC,gBAAgB,CAAC;IAC5C,MAAMmC,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC;;IAE3C;IACA;IACA,MAAMmB,UAAU,GAAGpB,UAAU,KAAK,EAAE,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC;IACzD,MAAMS,SAAS,GAAGT,UAAU,KAAK,EAAE,GAAGmB,EAAE,GAAGA,EAAE,GAAG,CAAC;IACjD,MAAME,OAAO,GAAGF,EAAE;IAElB,MAAMG,UAAU,GAAG,CACjB,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CACzC;IAED,OAAO,IAAIA,UAAU,CAACF,UAAU,GAAG,CAAC,CAAC,IAAIX,SAAS,MAAMa,UAAU,CAACtB,UAAU,GAAG,CAAC,CAAC,IAAIqB,OAAO,GAAG;EAClG,CAAC;EAED,MAAME,kBAAkB,GAAG5D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6D,IAAI,CAC1CC,IAAI;IAAA,IAAAC,eAAA;IAAA,OAAK,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,mBAAmB,IAAI,CAAAF,IAAI,aAAJA,IAAI,wBAAAC,eAAA,GAAJD,IAAI,CAAEG,SAAS,cAAAF,eAAA,uBAAfA,eAAA,CAAiBG,MAAM,KAAI,CAAC;EAAA,CAC9E,CAAC;EAED,MAAMC,eAAe,GAAGnE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6D,IAAI,CACvCC,IAAI;IAAA,IAAAM,gBAAA;IAAA,OAAK,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,eAAe,IAAI,CAAAF,IAAI,aAAJA,IAAI,wBAAAM,gBAAA,GAAJN,IAAI,CAAEG,SAAS,cAAAG,gBAAA,uBAAfA,gBAAA,CAAiBF,MAAM,KAAI,CAAC;EAAA,CAC1E,CAAC;EAED,MAAMG,yBAAyB,GAAG,CAChC;IACEC,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAEtG,aAAa,CAACuG;EACvB,CAAC,EACD;IACEF,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAEtG,aAAa,CAACwG;EACvB,CAAC,EACD;IACEH,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAEtG,aAAa,CAACyG;EACvB,CAAC,EACD;IACEJ,KAAK,EAAE,0BAA0B;IACjCC,KAAK,EAAEtG,aAAa,CAAC0G;EACvB,CAAC,EACD;IACEL,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAEtG,aAAa,CAAC2G;EACvB,CAAC,EACD;IACEN,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAEtG,aAAa,CAAC4G;EACvB,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAGA,CAACd,IAAI,EAAEF,IAAI,KAAK;IACvC,IAAIE,IAAI,KAAK,aAAa,IAAIF,IAAI,EAAE;MAClClD,QAAQ,CAAC,EAAE,CAAC;IACd;IAEA,IAAIoD,IAAI,KAAK,oBAAoB,IAAIF,IAAI,EAAE;MACzClD,QAAQ,CAAC,EAAE,CAAC;IACd;IACA,IAAIoD,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC3DpD,QAAQ,CAAC,EAAE,CAAC;IACd;IAEA,IAAI,CAACkD,IAAI,EAAE;MACTxC,QAAQ,CAAEyD,IAAI,KAAM;QAClB,GAAGA,IAAI;QACP,CAACf,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;MACH;IACF;IAEA,MAAMgB,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAE/BD,MAAM,CAACE,MAAM,GAAG,MAAM;MACpB,MAAMC,aAAa,GAAGH,MAAM,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD/D,QAAQ,CAAEyD,IAAI,KAAM;QAClB,GAAGA,IAAI;QACP,CAACf,IAAI,GAAG;UACNsB,IAAI,EAAExB,IAAI,CAACwB,IAAI;UACfpG,IAAI,EAAE4E,IAAI,CAAC5E,IAAI;UACfqG,MAAM,EAAEJ;QACV;MACF,CAAC,CAAC,CAAC;IACL,CAAC;IAEDH,MAAM,CAACQ,OAAO,GAAG,MAAM;MACrBC,OAAO,CAAC9E,KAAK,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAEDqE,MAAM,CAACU,aAAa,CAAC5B,IAAI,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAC;IAAE3B,IAAI;IAAEM;EAAM,CAAC,KAAK;IAC7C,MAAMR,IAAI,GAAGzC,KAAK,CAAC2C,IAAI,CAAC;IACxB,oBACE7F,OAAA;MAAKQ,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCT,OAAA;QAAMQ,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAAE0F;MAAK;QAAAzF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChEb,OAAA;QAAKQ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnD,CAACkF,IAAI,gBACJ3F,OAAA;UACEQ,SAAS,EAAC,iFAAiF;UAC3FM,OAAO,EAAEA,CAAA,KAAM2G,QAAQ,CAACC,cAAc,CAAC,QAAQ7B,IAAI,EAAE,CAAC,CAAC8B,KAAK,CAAC,CAAE;UAAAlH,QAAA,EAChE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETb,OAAA;UAAKQ,SAAS,EAAC,2FAA2F;UAAAC,QAAA,gBACxGT,OAAA;YAAKQ,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DT,OAAA,CAACR,mBAAmB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBb,OAAA;cAAKQ,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BT,OAAA;gBAAGQ,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EACnCkF,IAAI,CAACwB,IAAI,CAACpB,MAAM,GAAG,EAAE,GAClBJ,IAAI,CAACwB,IAAI,CAACS,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAC9BjC,IAAI,CAACwB;cAAI;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACJb,OAAA;gBAAGQ,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,GAAC,GACnC,EAACoH,IAAI,CAACC,KAAK,CAACnC,IAAI,CAAC5E,IAAI,GAAG,IAAI,CAAC,EAAC,MACjC;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNb,OAAA;YAAQc,OAAO,EAAEA,CAAA,KAAM6F,gBAAgB,CAACd,IAAI,EAAE,IAAI,CAAE;YAAApF,QAAA,eAClDT,OAAA,CAACX,CAAC;cAACmB,SAAS,EAAC;YAA+C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNb,OAAA;QACE+H,EAAE,EAAE,QAAQlC,IAAI,EAAG;QACnBA,IAAI,EAAC,MAAM;QACXrF,SAAS,EAAC,QAAQ;QAClBwH,QAAQ,EAAGC,CAAC,IAAKtB,gBAAgB,CAACd,IAAI,EAAEoC,CAAC,CAACC,MAAM,CAAChF,KAAK,CAAC,CAAC,CAAC,CAAE;QAC3DiF,MAAM,EAAC;MAAY;QAAAzH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMuH,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,UAAU,KAAK;IAC7C3F,QAAQ,CAAEiE,IAAI,KAAM;MAClB,GAAGA,IAAI;MACP,CAAC0B,UAAU,GAAGD;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAID,MAAME,UAAU,GAAIF,IAAI,IAAK;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IACtB,MAAMG,KAAK,GAAG,CAACH,IAAI,CAAClE,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEsE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/D,MAAMC,GAAG,GAAGN,IAAI,CAACO,OAAO,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAM7D,IAAI,GAAGwD,IAAI,CAAC9D,WAAW,CAAC,CAAC;IAC/B,OAAO,GAAGiE,KAAK,IAAIG,GAAG,IAAI9D,IAAI,EAAE;EAClC,CAAC;EAED,MAAMgE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjF,cAAc,KAAK9D,aAAa,CAACyG,IAAI,EAAE;MACzC,IAAI,CAACrD,KAAK,CAACI,aAAa,IAAI,CAACJ,KAAK,CAACK,gBAAgB,EAAE;QACnDd,QAAQ,CAAC,6DAA6D,CAAC;QACvE,OAAO,CAAC;MACV,CAAC,MAAM,IAAI,CAACS,KAAK,CAACI,aAAa,EAAE;QAC/Bb,QAAQ,CAAC,iCAAiC,CAAC;QAC3C;MACF,CAAC,MAAM,IAAI,CAACS,KAAK,CAACK,gBAAgB,EAAE;QAClCd,QAAQ,CAAC,oCAAoC,CAAC;QAC9C;MACF;IACF;IAEA,IAAImB,cAAc,KAAK9D,aAAa,CAAC2G,MAAM,IAAI,CAACvD,KAAK,CAACO,WAAW,EAAE;MACjEhB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEA,IAAImB,cAAc,KAAK9D,aAAa,CAAC2G,MAAM,EAAE;MAC3C9C,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLmF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAIC,OAAO;IACX,MAAMC,SAAS,GAAG,EAAE;IAEpB,IACEpF,cAAc,KAAK9D,aAAa,CAAC2G,MAAM,IACvC7C,cAAc,KAAK9D,aAAa,CAACyG,IAAI,EACrC;MACA0C,MAAM,CAACC,OAAO,CAAChG,KAAK,CAAC,CAACiG,OAAO,CAAC,CAAC,CAACtD,IAAI,EAAEF,IAAI,CAAC,KAAK;QAC9C,IAAIA,IAAI,EAAE;UACRqD,SAAS,CAAClE,IAAI,CAAC;YACbe,IAAI;YACJsB,IAAI,EAAExB,IAAI,CAACwB,IAAI;YACfiC,OAAO,EAAEzD,IAAI,CAACyB;UAChB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF2B,OAAO,GAAG;QACRM,YAAY,EACVzF,cAAc,KAAK9D,aAAa,CAAC2G,MAAM,GACnC3G,aAAa,CAAC2G,MAAM,GACpB7C,cAAc;QACpByE,IAAI,EAAEE,UAAU,CAAC7F,KAAK,CAACkB,cAAc,CAAC,CAAC;QACvCV,KAAK,EAAE8F;MACT,CAAC;IACH,CAAC,MAAM;MACLD,OAAO,GAAG;QACRM,YAAY,EAAEzF,cAAc;QAC5ByE,IAAI,EAAEE,UAAU,CAAC7F,KAAK,CAACkB,cAAc,CAAC;MACxC,CAAC;IACH;IAEAmF,OAAO,CAAC,WAAW,CAAC,GAAGjH,SAAS;IAEhCgC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAAwF,cAAA;MACF,MAAMC,QAAQ,GAAG,MAAM7J,aAAa,CAACqJ,OAAO,CAAC;MAC7C,IAAI,CAAAQ,QAAQ,aAARA,QAAQ,wBAAAD,cAAA,GAARC,QAAQ,CAAEC,IAAI,cAAAF,cAAA,uBAAdA,cAAA,CAAgBG,UAAU,MAAK,GAAG,EAAE;QACtC9J,IAAI,CAAC+J,IAAI,CAAC;UACRC,KAAK,EAAE,IAAI;UACXC,QAAQ,EAAE,SAAS;UACnBC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,gCAAgC;UACvCC,iBAAiB,EAAE,KAAK;UACxBC,KAAK,EAAE,IAAI;UACXC,gBAAgB,EAAE;QACpB,CAAC,CAAC;QACFjI,eAAe,CAAC,CAAC;QACjBJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,OAAOsI,GAAG,EAAE;MACZvK,IAAI,CAAC+J,IAAI,CAAC;QACRC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,SAAS;QACnBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,oDAAoD;QAC3DC,iBAAiB,EAAE,KAAK;QACxBC,KAAK,EAAE,IAAI;QACXC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRnG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACE9D,OAAA,CAAAE,SAAA;IAAAO,QAAA,EACGiD,gBAAgB,gBACf1D,OAAA,CAACG,iBAAiB;MAChBI,YAAY,EAAEA,YAAa;MAC3BH,MAAM,EAAEA,CAAA,KAAMuD,mBAAmB,CAAC,KAAK,CAAE;MACzCtD,SAAS,EAAEyI,aAAc;MACzBxI,OAAO,EAAEA;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,gBAEFb,OAAA;MAAKQ,SAAS,EAAC,4DAA4D;MAAAC,QAAA,eACzET,OAAA;QAAKQ,SAAS,EAAC,uIAAuI;QAAAC,QAAA,gBACpJT,OAAA;UAAKQ,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DT,OAAA;YAAIQ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7Db,OAAA;YACEQ,SAAS,EAAC,mCAAmC;YAC7CM,OAAO,EAAEc,OAAQ;YAAAnB,QAAA,eAEjBT,OAAA,CAACX,CAAC;cAAC0B,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENb,OAAA;UAAKQ,SAAS,EAAC,eAAe;UAAAC,QAAA,GAC3ByF,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEiE,GAAG,CAAEC,MAAM,iBACrCpK,OAAA;YAAwBQ,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACzDT,OAAA;cAAKQ,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CT,OAAA;gBACE6F,IAAI,EAAC,OAAO;gBACZsB,IAAI,EAAC,YAAY;gBACjBY,EAAE,EAAEqC,MAAM,CAAChE,KAAM;gBACjBiE,OAAO,EAAEzG,cAAc,KAAKwG,MAAM,CAAChE,KAAM;gBACzC4B,QAAQ,EAAEA,CAAA,KAAMnE,iBAAiB,CAACuG,MAAM,CAAChE,KAAK,CAAE;gBAChD5F,SAAS,EAAC;cAA2D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eACFb,OAAA;gBACEsK,OAAO,EAAEF,MAAM,CAAChE,KAAM;gBACtB5F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAErC2J,MAAM,CAACjE;cAAK;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EAGLuJ,MAAM,CAAChE,KAAK,KAAKtG,aAAa,CAAC4G,SAAS,gBACvC1G,OAAA;cAAKQ,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BT,OAAA;gBACEoG,KAAK,EAAE1D,KAAK,CAAC0H,MAAM,CAAChE,KAAK,CAAC,GAAG1D,KAAK,CAAC0H,MAAM,CAAChE,KAAK,CAAC,CAAC7B,WAAW,CAAC,CAAC,GAAG,EAAG;gBACpEyD,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMsC,YAAY,GAAGC,QAAQ,CAACvC,CAAC,CAACC,MAAM,CAAC9B,KAAK,CAAC;kBAC7C,IAAImE,YAAY,EAAE;oBAChB;oBACA,MAAME,UAAU,GAAGtF,iBAAiB,CAACuF,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC9F,IAAI,KAAK0F,YAAY,CAAC;oBAC3E,IAAIE,UAAU,IAAIA,UAAU,CAAC1F,OAAO,EAAE;sBACpC,MAAM6F,QAAQ,GAAG,IAAI3G,IAAI,CAACsG,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;sBAC/CnC,gBAAgB,CAACwC,QAAQ,EAAER,MAAM,CAAChE,KAAK,CAAC;oBAC1C;kBACF,CAAC,MAAM;oBACLgC,gBAAgB,CAAC,IAAI,EAAEgC,MAAM,CAAChE,KAAK,CAAC;kBACtC;gBACF,CAAE;gBACF5F,SAAS,EAAC,oKAAoK;gBAC9KqK,KAAK,EAAE;kBACLC,eAAe,EAAE,mOAAmO;kBACpPC,kBAAkB,EAAE,qBAAqB;kBACzCC,gBAAgB,EAAE,WAAW;kBAC7BC,cAAc,EAAE;gBAClB,CAAE;gBAAAxK,QAAA,gBAEFT,OAAA;kBAAQoG,KAAK,EAAC,EAAE;kBAAA3F,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3CsE,iBAAiB,CAACgF,GAAG,CAAC,CAAC;kBAAEtF,IAAI;kBAAEE;gBAAQ,CAAC,kBACvC/E,OAAA;kBAEEoG,KAAK,EAAEvB,IAAK;kBACZ5D,QAAQ,EAAE,CAAC8D,OAAQ;kBACnB8F,KAAK,EAAE;oBACLxJ,KAAK,EAAE0D,OAAO,GAAG,SAAS,GAAG,MAAM;oBACnCmG,eAAe,EAAEnG,OAAO,GAAG,SAAS,GAAG;kBACzC,CAAE;kBAAAtE,QAAA,GACH,KACI,EAACoE,IAAI,EAAC,GAAC,EAACO,mBAAmB,CAACP,IAAI,CAAC,EAAC,GAAC,EAAC,CAACE,OAAO,GAAG,iBAAiB,GAAG,EAAE;gBAAA,GARnEF,IAAI;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASH,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENb,OAAA;cAAKQ,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BT,OAAA,CAACV,UAAU;gBACT6L,QAAQ,EAAEzI,KAAK,CAAC0H,MAAM,CAAChE,KAAK,CAAE;gBAC9B4B,QAAQ,EAAGK,IAAI,IAAKD,gBAAgB,CAACC,IAAI,EAAE+B,MAAM,CAAChE,KAAK,CAAE;gBACzDgF,eAAe,EAAC,aAAa;gBAC7B5K,SAAS,EAAC;cAAsH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjI,CAAC,eACFb,OAAA,CAACZ,QAAQ;gBACPoB,SAAS,EAAC,yDAAyD;gBACnEO,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,GAzEOuJ,MAAM,CAAChE,KAAK;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0EjB,CACN,CAAC,EAGD+C,cAAc,IACdA,cAAc,KAAK9D,aAAa,CAAC4G,SAAS,KAC1C7E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwJ,MAAM,CAAE1F,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,eAAe,CAAC,KAC9D,CAAAhE,YAAY,aAAZA,YAAY,wBAAAK,qBAAA,GAAZL,YAAY,CAAEwJ,MAAM,CACjB1F,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,eAC3B,CAAC,CAAC,CAAC,CAAC,cAAA3D,qBAAA,wBAAAC,sBAAA,GAFJD,qBAAA,CAEM4D,SAAS,cAAA3D,sBAAA,uBAFfA,sBAAA,CAEiB4D,MAAM,MAAK,CAAC,iBAC3B/F,OAAA;YAAGQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ,EAGD+C,cAAc,KAAK9D,aAAa,CAACyG,IAAI,iBACpCvG,OAAA;YAAAS,QAAA,EACG,CAAAoB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwJ,MAAM,CAClB1F,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,mBAC3B,CAAC,KACC,CAAAhE,YAAY,aAAZA,YAAY,wBAAAO,sBAAA,GAAZP,YAAY,CAAEwJ,MAAM,CACjB1F,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,mBAC3B,CAAC,CAAC,CAAC,CAAC,cAAAzD,sBAAA,wBAAAC,sBAAA,GAFJD,sBAAA,CAEM0D,SAAS,cAAAzD,sBAAA,uBAFfA,sBAAA,CAEiB0D,MAAM,MAAK,CAAC,iBAC3B/F,OAAA;cAAGQ,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACN,EAEA+C,cAAc,KAAK9D,aAAa,CAAC2G,MAAM,KACtC5E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwJ,MAAM,CACjB1F,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,eAC3B,CAAC,KACD,CAAAhE,YAAY,aAAZA,YAAY,wBAAAS,sBAAA,GAAZT,YAAY,CAAEwJ,MAAM,CACjB1F,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,eAC3B,CAAC,CAAC,CAAC,CAAC,cAAAvD,sBAAA,wBAAAC,sBAAA,GAFJD,sBAAA,CAEMwD,SAAS,cAAAvD,sBAAA,uBAFfA,sBAAA,CAEiBwD,MAAM,IAAG,CAAC,iBACzB/F,OAAA;YAAKQ,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCT,OAAA;cAAGQ,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJb,OAAA,CAACwH,iBAAiB;cAAC3B,IAAI,EAAC,IAAI;cAACM,KAAK,EAAC;YAAiB;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDb,OAAA,CAACwH,iBAAiB;cAAC3B,IAAI,EAAC,IAAI;cAACM,KAAK,EAAC;YAAiB;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDb,OAAA,CAACwH,iBAAiB;cAChB3B,IAAI,EAAC,oBAAoB;cACzBM,KAAK,EAAC;YAA0B;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEFb,OAAA,CAACwH,iBAAiB;cAChB3B,IAAI,EAAC,aAAa;cAClBM,KAAK,EAAC;YAAoB;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EACD2B,KAAK,iBAAIxC,OAAA;cAAGQ,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAE+B;YAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACN,EAEF,CAAC4E,kBAAkB,IAAI,CAACO,eAAe,iBACtChG,OAAA;YAAAS,QAAA,EACGmD,cAAc,KAAK9D,aAAa,CAACyG,IAAI,KACpC1E,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6D,IAAI,CACfC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,eAC3B,CAAC,MACDhE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6D,IAAI,CACfC,IAAI,IAAK,CAAAA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,MAAK,mBAC3B,CAAC,kBACC7F,OAAA;cAAKQ,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCT,OAAA,CAACwH,iBAAiB;gBAChB3B,IAAI,EAAC,eAAe;gBACpBM,KAAK,EAAC;cAAe;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACFb,OAAA,CAACwH,iBAAiB;gBAChB3B,IAAI,EAAC,kBAAkB;gBACvBM,KAAK,EAAC;cAA4B;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,EACD2B,KAAK,iBACJxC,OAAA;gBAAGQ,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAE+B;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC/C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENb,OAAA;UAAKQ,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCT,OAAA,CAACJ,MAAM;YACLoB,OAAO,EAAC,UAAU;YAClBF,OAAO,EAAEc,OAAQ;YACjBX,QAAQ,EAAEX,OAAQ;YAClBY,EAAE,EAAE;cACFC,KAAK,EAAE,KAAK;cAAE;cACdC,aAAa,EAAE;YACjB,CAAE;YAAAX,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETb,OAAA,CAACJ,MAAM;YACLoB,OAAO,EAAC,WAAW;YACnBK,KAAK,EAAC,SAAS;YACfP,OAAO,EAAE+H,YAAa;YACtB5H,QAAQ,EAAE,CAACyB,KAAK,CAACkB,cAAc,CAAC,IAAI,CAAClB,KAAK,CAACkB,cAAc,CAAC,IAAItD,OAAQ;YACtEY,EAAE,EAAE;cACFC,KAAK,EAAE,KAAK;cAAE;cACdC,aAAa,EAAE,WAAW;cAC1B;cACA,IAAId,OAAO,IAAI;gBACbgB,MAAM,EAAE,aAAa;gBACrBC,SAAS,EAAE,mBAAmB;gBAC9B,kBAAkB,EAAE;kBAClB,IAAI,EAAE;oBAAEC,OAAO,EAAE;kBAAI,CAAC;kBACtB,KAAK,EAAE;oBAAEA,OAAO,EAAE;kBAAE,CAAC;kBACrB,MAAM,EAAE;oBAAEA,OAAO,EAAE;kBAAI;gBACzB,CAAC;gBACD;gBACA,SAAS,EAAE;kBACTC,SAAS,EAAE;gBACb;cACF,CAAC,CAAC;cACF,YAAY,EAAE;gBACZH,MAAM,EAAE;cACV;YACF,CAAE;YAAAb,QAAA,EAEDH,OAAO,gBACNN,OAAA,CAACH,gBAAgB;cAACkB,IAAI,EAAE,EAAG;cAACM,KAAK,EAAC;YAAS;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAE9C;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EACN,gBACD,CAAC;AAEP,CAAC;AAACoB,EAAA,CA1iBIN,WAAW;AAAA2J,GAAA,GAAX3J,WAAW;AA4iBjB,eAAeA,WAAW;AAAC,IAAAD,EAAA,EAAA4J,GAAA;AAAAC,YAAA,CAAA7J,EAAA;AAAA6J,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}