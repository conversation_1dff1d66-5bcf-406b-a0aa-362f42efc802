import React, { useEffect, useRef } from 'react';
import ApexCharts from 'apexcharts';

const LiquiditySummaryDashboard = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  liquidityData = null
}) => {
  const netChangeRef = useRef(null);
  const quickRatioRef = useRef(null);
  const monthsCashRef = useRef(null);

  // Enhanced data validation function
// Replace your isDataLoaded function with this improved version:

const isDataLoaded = () => {
  if (!liquidityData) {
    console.log('LiquiditySummary - No liquidityData provided');
    return false;
  }

  console.log('LiquiditySummary - liquidityData keys:', Object.keys(liquidityData));
  console.log('LiquiditySummary - Full liquidityData:', liquidityData);

  // Function to check if an array has meaningful data
  const hasValidData = (arr) => {
    return Array.isArray(arr) && 
           arr.length > 0 && 
           arr.some(item => item !== null && item !== undefined);
  };

  // Check if data might be nested under a different structure
  const possibleDataPaths = [
    liquidityData,
    liquidityData.liquiditySummary,
    liquidityData.liquidity,
    liquidityData.data,
    liquidityData.reportData
  ];

  console.log('LiquiditySummary - Checking possible data paths:', possibleDataPaths.map(path => path ? Object.keys(path) : 'null'));

  // Check if at least some required data exists
  const hasNetChangeData = hasValidData(liquidityData.netChangeInCash);
  const hasQuickRatioData = hasValidData(liquidityData.quickRatio);
  const hasMonthsCashData = hasValidData(liquidityData.monthOnCash);

  console.log('LiquiditySummary - Data validation:', {
    hasNetChangeData,
    hasQuickRatioData,
    hasMonthsCashData,
    netChangeLength: liquidityData.netChangeInCash?.length || 0,
    quickRatioLength: liquidityData.quickRatio?.length || 0,
    monthsCashLength: liquidityData.monthOnCash?.length || 0
  });

  // Log sample data for debugging
  if (liquidityData.monthOnCash && liquidityData.monthOnCash.length > 0) {
    console.log('LiquiditySummary - Sample months cash on hand data:', liquidityData.monthOnCash[0]);
  }

  // Return true if we have the basic structure, even if arrays might be empty
  // This allows the component to render and show appropriate "no data" messages
  return liquidityData && (
    liquidityData.hasOwnProperty('netChangeInCash') ||
    liquidityData.hasOwnProperty('quickRatio') ||
    liquidityData.hasOwnProperty('monthOnCash')
  );
};

  useEffect(() => {
    if (isDataLoaded()) {
      initializeCharts();
    }
  }, [liquidityData]);

  const formatMonthYear = (year, month) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;
  };

  const initializeCharts = () => {
    if (!liquidityData) return;

    console.log('LiquiditySummary - Initializing charts with data:', liquidityData);

    // Try to find the correct data structure
    let dataToUse = liquidityData;

    // Check if data is nested under different possible paths
    if (liquidityData.liquiditySummary) {
      dataToUse = liquidityData.liquiditySummary;
      console.log('LiquiditySummary - Using nested liquiditySummary data');
    } else if (liquidityData.liquidity) {
      dataToUse = liquidityData.liquidity;
      console.log('LiquiditySummary - Using nested liquidity data');
    } else if (liquidityData.data) {
      dataToUse = liquidityData.data;
      console.log('LiquiditySummary - Using nested data');
    } else if (liquidityData.reportData) {
      dataToUse = liquidityData.reportData;
      console.log('LiquiditySummary - Using nested reportData');
    }

    console.log('LiquiditySummary - Final data structure to use:', dataToUse);

    // Prepare data from API response
    const categories = dataToUse.netChangeInCash?.map(item =>
      formatMonthYear(item.year, item.month)
    ) || [];

    const netChangeInCashData = dataToUse.netChangeInCash?.map(item =>
      parseFloat(item.net_change_in_cash) / 1000 || 0  // Convert to thousands for better display
    ) || [];

    const quickRatioData = dataToUse.quickRatio?.map(item =>
      parseFloat(item.quickRatio) || 0
    ) || [];

    const monthsCashOnHandData = dataToUse.monthOnCash?.map(item => {
      // Try multiple possible property names for robustness
      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;
      return parseFloat(value) || 0;
    }) || [];

    console.log('LiquiditySummary - Processed data:', {
      categoriesLength: categories.length,
      netChangeLength: netChangeInCashData.length,
      netChangeData: netChangeInCashData,
      quickRatioLength: quickRatioData.length,
      quickRatioData: quickRatioData,
      monthsCashLength: monthsCashOnHandData.length,
      monthsCashData: monthsCashOnHandData
    });

    // 1. Net Change in Cash Chart
const netChangeOptions = {
  series: [{
    name: 'Net Change in Cash',
    data: netChangeInCashData
  }],
  chart: {
    type: 'line',
    height: 300,
    toolbar: { show: false },
    background: 'transparent',
    zoom : {
      enabled : false
    }
  },
  dataLabels: {
    enabled: true,
    formatter: function (val) {
      if (val >= 1) {
        return '$' + val.toFixed(1) + 'k';
      } else if (val >= 0.1) {
        return '$' + val.toFixed(2) + 'k';
      } else if (val <= -0.1) {
        return '-$' + Math.abs(val).toFixed(2) + 'k';
      } else {
        return '$' + val.toFixed(2) + 'k';
      }
    },
    style: {
      fontSize: '14px',
      colors: ['#333'],
      fontWeight: '500'
    },
    offsetY: -15,
    background: {
      enabled: false
    },
    dropShadow: {
      enabled: false
    }
  },
  stroke: {
    curve: 'straight',
    width: 3 // Changed from 2 to 3 to match netProfitMarginOptions
  },
  fill: {
    type: 'solid'
  },
  markers: {
    size: 5,
    strokeColors: '#fff',
    strokeWidth: 2,
    colors: netChangeInCashData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),
    hover: {
      size: 7
    },
    discrete: netChangeInCashData.map((val, index) => ({
      seriesIndex: 0,
      dataPointIndex: index,
      fillColor: val >= 0 ? '#1E7C8C' : '#d70015',
      strokeColor: '#fff',
      size: 5
    }))
  },
  xaxis: {
    categories: categories,
    labels: {
      style: {
        colors: '#666',
        fontSize: '14px'
      }
    },
    axisBorder: { show: false },
    axisTicks: { show: false }
  },
  yaxis: {
    show: false
  },
  colors: ['#1E7C8C'], // Default line color
  plotOptions: {
    line: {
      colors: {
        threshold: 0,
        colorAboveThreshold: '#1E7C8C',
        colorBelowThreshold: '#d70015',
      },
    }
  },
  tooltip: {
    y: {
      formatter: function (val) {
        if (val >= 1) {
          return '$' + val.toFixed(1) + ' thousand';
        } else if (val >= 0.1) {
          return '$' + val.toFixed(2) + ' thousand';
        } else if (val <= -0.1) {
          return '-$' + Math.abs(val).toFixed(2) + ' thousand';
        } else {
          return '$' + val.toFixed(3) + ' thousand';
        }
      }
    }
  },
  grid: {
    show: false,
    padding: {
      left: 25,
      right: 25,
      top: 25,
      bottom: 0
    }
  },
  annotations: {
    yaxis: [{
      y: 0,
      borderColor: '#666',
      borderWidth: 1,
      strokeDashArray: 0,
      opacity: 0.8
    }]
  }
};
    // 2. Quick Ratio Chart
    const quickRatioOptions = {
      series: [{
        name: 'Quick Ratio',
        data: quickRatioData
      }],
      chart: {
        type: 'area',
        height: 300,
        toolbar: { show: false },
        background: 'transparent',
        zoom : {
          enabled : false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val.toFixed(2);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: ['#5457a3']
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: '#5457a3', opacity: 0.4 },
            { offset: 100, color: '#5457a3', opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: ['#5457a3'],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: ['#5457a3'],
      tooltip: {
        y: {
          formatter: function (val) {
            return val.toFixed(2);
          }
        }
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: {
        show: false
      }
    };

    // 3. Months Cash on Hand Chart
    const monthsCashOptions = {
      series: [{
        name: 'Months Cash on Hand',
        data: monthsCashOnHandData
      }],
      chart: {
        type: 'area',
        height: 300,
        toolbar: { show: false },
        background: 'transparent',
        zoom : {
          enabled : false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val.toFixed(2);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: ['#298478']
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: '#298478', opacity: 0.4 },
            { offset: 100, color: '#298478', opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: ['#298478'],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            colors: '#666',
            fontSize: '14px'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: ['#298478'],
      tooltip: {
        y: {
          formatter: function (val) {
            return val.toFixed(2) + ' months';
          }
        }
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: {
        show: false
      }
    };

    // Clear existing charts before rendering new ones
    const clearAndRenderChart = (ref, options, chartName) => {
      if (ref.current) {
        // Clear any existing chart
        ref.current.innerHTML = '';

        // Wait a tick before rendering to ensure DOM is cleared
        setTimeout(() => {
          if (ref.current) {
            try {
              console.log(`LiquiditySummary - Rendering ${chartName} chart`);
              const chart = new ApexCharts(ref.current, options);
              chart.render();

              // Store chart instances globally for export
              if (chartName === "Net Change in Cash") {
                window.netChangeChart = chart;
              } else if (chartName === "Quick Ratio") {
                window.quickRatioChart = chart;
              } else if (chartName === "Months Cash on Hand") {
                window.monthsCashChart = chart;
              }
            } catch (error) {
              console.error(`LiquiditySummary - Error rendering ${chartName} chart:`, error);
            }
          }
        }, 10);
      }
    };

    // Only render charts if we have data for them
    console.log('LiquiditySummary - Chart rendering check:', {
      netChangeDataLength: netChangeInCashData.length,
      netChangeData: netChangeInCashData,
      quickRatioDataLength: quickRatioData.length,
      quickRatioData: quickRatioData,
      monthsCashDataLength: monthsCashOnHandData.length,
      monthsCashData: monthsCashOnHandData
    });

    if (netChangeInCashData.length > 0) {
      clearAndRenderChart(netChangeRef, netChangeOptions, 'Net Change in Cash');
    } else if (netChangeRef.current) {
      netChangeRef.current.innerHTML = '<div class="flex items-center justify-center h-64 text-gray-500">No meaningful net change in cash data available</div>';
    }

    if (quickRatioData.length > 0) {
      clearAndRenderChart(quickRatioRef, quickRatioOptions, 'Quick Ratio');
    } else if (quickRatioRef.current) {
      quickRatioRef.current.innerHTML = '<div class="flex items-center justify-center h-64 text-gray-500">No meaningful quick ratio data available</div>';
    }

    if (monthsCashOnHandData.length > 0) {
      clearAndRenderChart(monthsCashRef, monthsCashOptions, 'Months Cash on Hand');
    } else if (monthsCashRef.current) {
      monthsCashRef.current.innerHTML = '<div class="flex items-center justify-center h-64 text-gray-500">No meaningful months cash on hand data available</div>';
    }
  };

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; // fallback
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

  // Enhanced loading component


  // Show loading if data is not properly loaded

  // Add a fallback if liquidityData exists but has no usable data
  // const hasAnyUsableData = () => {
  //   const netChangeInCashData = liquidityData.netChangeInCash?.map(item =>
  //     parseFloat(item.net_change_in_cash) || 0
  //   ) || [];

  //   const quickRatioData = liquidityData.quickRatio?.map(item =>
  //     parseFloat(item.quick_ratio) || 0
  //   ) || [];

  //   const monthsCashOnHandData = liquidityData.monthOnCash?.map(item => {
  //     // Try multiple possible property names for robustness
  //     const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;
  //     return parseFloat(value) || 0;
  //   }) || [];
  //   console.log('LiquiditySummary - Months Cash on Hand Data:', monthsCashOnHandData);

  //   return (netChangeInCashData.length > 0 || quickRatioData.length > 0 || monthsCashOnHandData.length > 0);
  // };

  // if (!hasAnyUsableData()) {
  //   return (
  //     <div className="p-5">
  //       <div className="max-w-6xl mx-auto bg-white flex flex-col h-[400mm] shadow p-10 mb-2">
  //         <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
  //         <h1
  //           className="text-4xl font-bold text-gray-800 m-0"
  //           style={headerTextStyle}
  //         >
  //           Liquidity Summary
  //         </h1>
  //         <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
  //           {formatHeaderPeriod(liquidityData?.FYStartYear, liquidityData?.FYStartMonth)} | Acme Print
  //         </p>
  //       </div>

  //         <div className="flex items-center justify-center h-64">
  //           <div className="text-center">
  //             <div className="text-xl text-gray-600 mb-2">
  //               No liquidity data available
  //             </div>
  //             <div className="text-sm text-gray-500">
  //               The data structure is available but charts cannot be rendered
  //             </div>
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="min-h-screen p-5">
      {/* Main Container */}
      <div className="max-w-6xl mx-auto bg-white flex flex-col p-10 mb-2">

        {/* Header Section */}
        <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Liquidity Summary
          </h1>
          <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
            {formatHeaderPeriod(liquidityData?.FYStartYear, liquidityData?.FYStartMonth)} | Acme Print
          </p>
        </div>

        {/* Net Change in Cash Chart */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Net Change in Cash
          </div>
          <div ref={netChangeRef}></div>
        </div>

        {/* Quick Ratio Chart */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Quick Ratio
          </div>
          <div ref={quickRatioRef}></div>
        </div>

        {/* Months Cash on Hand Chart */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-2"
            style={subHeadingTextStyle}
          >
            Months Cash on Hand
          </div>
          <div ref={monthsCashRef}></div>
          <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
            <div
              className="text-teal-600 text-2xl"
              style={{ ...subHeadingTextStyle, fontWeight: 'lighter' }}
            >
              Months Cash on Hand
            </div>
            <div style={contentTextStyle}>
              Given the amount of cash available, the number of months that a business can continue to pay for its core and operating expenses. Under 3 months or a downward trend may be cause for concern.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiquiditySummaryDashboard;