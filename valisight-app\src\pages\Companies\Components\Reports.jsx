import React, { useEffect, useState } from "react";
import "react-datepicker/dist/react-datepicker.css";
import EditReport from "./EditReport";
import ReportModal from "./ReportModal";
import { deleteRequest, getAllCompanyReports } from "../../../services/report";
import { formatDate } from "../../../utils/shared";
import Swal from "sweetalert2";
import { CircularProgress } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import Button from '@mui/material/Button';

const Reports = ({ companyFiles, companyId, companyFYEndDate, setActiveTab }) => {
  console.log("FyDate: ", companyFYEndDate);
  const [reportModal, setReportModal] = useState(false);
  const [reportDetail, setReportDetail] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [reports, setReports] = useState([]);
  const [isloading, setLoading] = useState(false);
  const navigate = useNavigate();
  const params = useParams();

  // Get company ID from props or params
  const currentCompanyId = companyId || params.id;

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSubmitSuccess = () => {
    companyReports();
    Swal.fire({
      toast: true,
      position: "top-end",
      icon: "success",
      title: "Report added successfully!",
      showConfirmButton: false,
      timer: 1000,
      timerProgressBar: true,
    });
  };

  const requestTypeLabels = {
    Monthly: "ProfitPulse (Monthly)",
    Benchmark: "KPITrack (Benchmark)",
    "GAAP Analyzer": "GAAP Align",
    CSFA: "FinCheck (Current State)",
    "13weeks": "FlowCast (13 Week)",
  };

  const handleView = (reportId) => {
    // Navigate to the custom template route with the current company ID and report ID
    navigate(`/company/${currentCompanyId}/custom-template/${reportId}`);
  };

  const handleReportNameClick = (row) => {
    // Only allow navigation for Deepsight reports
    if (row.name.toLowerCase().includes('deepsight')) {
      handleView(row.id);
    }
  };

  const handleDelete = async (id) => {
    try {
      setLoading(true);
      const response = await deleteRequest(id);
      if (response?.data?.request && response?.data?.statusCode === 200) {
        Swal.fire({
          toast: true,
          position: "top-end",
          icon: "success",
          title: "Report request deleted successfully!",
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
        });
        await companyReports();
      }
    } catch (err) {
      console.error("Error in deleting request report:", err);
      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: "Failed to delete request. Please try again.",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const getBorderColor = (status) => {
    switch (status) {
      case "download":
        return "green";
      case "edit":
        return "blue";
      case "error":
        return "red";
      case "processing":
        return "orange";
      default:
        return "grey";
    }
  };

  const companyReports = async () => {
    setLoading(true); // Start loading
    try {
      const reports = await getAllCompanyReports(currentCompanyId);
      if (reports.status === 200) {
        setReports(reports?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching reports:", error);
    } finally {
      setLoading(false); // End loading
    }
  };

  useEffect(() => {
    if (currentCompanyId) {
      companyReports();
    }
  }, [currentCompanyId, reportDetail]);

  const handleDownload = (documents) => {
    const signedUrl = documents[0]?.file_key;
    window.open(`${signedUrl}`, "_blank");
  };

  const handleEditReport = (row) => {
    setReportDetail(row);
  };

  return (
    <div className="px-2 py-3">
      {reportDetail ? (
        <EditReport report={reportDetail} setReportDetail={setReportDetail} />
      ) : (
        <>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold">Reports</h1>
            <Button
              variant="contained"
              color="primary"
              onClick={() => setReportModal(true)}
              sx={{
                width: 'auto',
                minWidth: 160,
                px: 2,
                py: 1,
                fontWeight: 500,
                textTransform: 'uppercase',
                fontSize: '0.875rem',
              }}
            >
              REQUEST NEW REPORT
            </Button>
          </div>
          <div className="bg-white rounded-lg shadow">
            {isloading ? (
              <div className="flex justify-center items-center p-6">
                <CircularProgress />
              </div>
            ) : (
              <>
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="text-left p-4 font-semibold text-gray-600 sm:w-[45%] md:w-[55%] lg:w-[65%]">
                        Report Name
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-600  sm:w-[15%] md:w-[20%] lg:w-[15%]">
                        Date Submitted
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-600  sm:w-[15%] md:w-[10%] lg:w-[10%]">
                        Status
                      </th>
                      <th className="text-center p-4 font-semibold text-gray-600 sm:w-[15%] md:w-[15%] lg:w-[10%]">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {reports?.length > 0 ? (
                      reports
                        ?.slice(
                          page * rowsPerPage,
                          page * rowsPerPage + rowsPerPage
                        )
                        .map((row) => (
                          <tr key={row.id} className="border-t">
                            <td
                              className={`p-4 sm:w-[45%] md:w-[55%] lg:w-[65%] ${row.name.toLowerCase().includes('deepsight')
                                  ? 'cursor-pointer'
                                  : ''
                                }`}
                              onClick={() => handleReportNameClick(row)}
                              title={row.name.toLowerCase().includes('deepsight') ? 'Click to view report' : ''}
                            >
                              {row.name}
                            </td>
                            <td className="p-4 sm:w-[15%] md:w-[20%] lg:w-[15%]">
                              {formatDate(row.date_requested)}
                            </td>
                            <td
                              className="p-4 cursor-pointer"
                              onClick={
                                row.status === "download"
                                  ? () => handleDownload(row.documents)
                                  : row.status === "edit"
                                    ? () => handleEditReport(row)
                                    : null
                              }
                            >
                              <span
                                className="px-3 py-1 rounded-full text-sm sm:w-[15%] md:w-[10%] lg:w-[10%]"
                                style={{
                                  border: `2px solid ${getBorderColor(
                                    row.status
                                  )}`,
                                  paddingBottom : '7px',
                                  color: getBorderColor(row.status),
                                }}
                              >
                                {row.status}
                              </span>
                            </td>
                            <td className="p-4 text-center sm:w-[15%] md:w-[15%] lg:w-[10%]">
                              <div className="flex items-center justify-center">
                                <button
                                  onClick={() => handleDelete(row.id)}
                                  className="text-gray-500 hover:text-red-600 transition-colors duration-200"
                                  title="Delete"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                    ) : (
                      <tr>
                        <td
                          colSpan="4"
                          className="p-4 text-center text-gray-600"
                        >
                          No Report requests
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
                <div className="flex items-center justify-between p-4 border-t">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      Rows per page:
                    </span>
                    <select
                      value={rowsPerPage}
                      onChange={handleChangeRowsPerPage}
                      className="border rounded p-1"
                    >
                      {[5, 10, 25].map((n) => (
                        <option key={n} value={n}>
                          {n}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => handleChangePage(e, page - 1)}
                      disabled={page === 0}
                      className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <span className="text-sm text-gray-600">
                      Page {page + 1} of{" "}
                      {Math.ceil(reports.length / rowsPerPage)}
                    </span>
                    <button
                      onClick={(e) => handleChangePage(e, page + 1)}
                      disabled={
                        page >= Math.ceil(reports.length / rowsPerPage) - 1
                      }
                      className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      )}
      {reportModal && (
        <ReportModal
          setActiveTab={setActiveTab}
          companyFiles={companyFiles}
          companyId={currentCompanyId}
          companyFYEndDate={companyFYEndDate}
          onClose={() => setReportModal(false)}
          onSubmitSuccess={handleSubmitSuccess}
        />
      )}
    </div>
  );
};

export default Reports;