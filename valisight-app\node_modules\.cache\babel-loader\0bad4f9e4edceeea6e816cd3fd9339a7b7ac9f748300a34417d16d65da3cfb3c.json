{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\QuickBooksConnection.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Card, Typography, Button, Stack, Chip, CircularProgress, Fade, Slide, Paper, Divider, Alert, AlertTitle, LinearProgress, IconButton, Tooltip } from \"@mui/material\";\nimport { CheckCircle as CheckCircleIcon, Error as ErrorIcon, Refresh as RefreshIcon, Info as InfoIcon, Launch as LaunchIcon, AccountBalance as AccountBalanceIcon, Security as SecurityIcon, Sync as SyncIcon, Close as CloseIcon } from \"@mui/icons-material\";\nimport qboButton from \"../../../assets/C2QB_green_btn_med_default.svg\";\nimport qboButtonHover from \"../../../assets/C2QB_green_btn_med_hover.svg\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuickBooksConnection = ({\n  qboConnected,\n  qboLoading,\n  qboStatus,\n  company,\n  onConnect,\n  onDisconnect,\n  onRefreshStatus,\n  showDetailedView = false\n}) => {\n  _s();\n  const [showDetails, setShowDetails] = useState(showDetailedView);\n  const [connectionProgress, setConnectionProgress] = useState(0);\n  const [showBenefits, setShowBenefits] = useState(!qboConnected);\n  useEffect(() => {\n    if (qboLoading) {\n      const interval = setInterval(() => {\n        setConnectionProgress(prev => {\n          if (prev >= 90) return 90;\n          return prev + Math.random() * 10;\n        });\n      }, 200);\n      return () => clearInterval(interval);\n    } else {\n      setConnectionProgress(qboConnected ? 100 : 0);\n    }\n  }, [qboLoading, qboConnected]);\n  const connectionBenefits = [{\n    icon: /*#__PURE__*/_jsxDEV(SyncIcon, {\n      sx: {\n        color: \"primary.main\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this),\n    title: \"Real-time Data Sync\",\n    description: \"Automatically sync your financial data in real-time\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AccountBalanceIcon, {\n      sx: {\n        color: \"success.main\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this),\n    title: \"Complete Financial Reports\",\n    description: \"Access trial balance, P&L, balance sheet, and aging reports\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n      sx: {\n        color: \"warning.main\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    title: \"Secure Integration\",\n    description: \"Bank-level security with OAuth 2.0 authentication\"\n  }];\n  const renderConnectionStatus = () => {\n    if (qboLoading) {\n      return /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20,\n          thickness: 4\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Connecting to QuickBooks...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 2,\n      alignItems: \"center\",\n      children: qboConnected ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          sx: {\n            color: \"success.main\",\n            fontSize: 20\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Connected\",\n          size: \"small\",\n          sx: {\n            backgroundColor: \"success.light\",\n            color: \"success.dark\",\n            fontWeight: 600\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n          sx: {\n            color: \"error.main\",\n            fontSize: 20\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Not Connected\",\n          size: \"small\",\n          sx: {\n            backgroundColor: \"error.light\",\n            color: \"error.dark\",\n            fontWeight: 600\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this);\n  };\n  const renderDetailedView = () => /*#__PURE__*/_jsxDEV(Slide, {\n    direction: \"up\",\n    in: showDetails,\n    timeout: 400,\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 12,\n      sx: {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"50%\",\n        transform: \"translate(-50%, -50%)\",\n        width: {\n          xs: \"90%\",\n          sm: \"80%\",\n          md: \"600px\"\n        },\n        maxHeight: \"80vh\",\n        overflow: \"auto\",\n        zIndex: 1300,\n        borderRadius: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: qboButton,\n              alt: \"QuickBooks\",\n              sx: {\n                height: 40,\n                width: \"auto\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: 700,\n                children: \"Connect ValiSights Company to\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: 700,\n                color: \"primary.main\",\n                children: \"QuickBooks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => setShowDetails(false),\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Connection Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), renderConnectionStatus(), qboLoading && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: connectionProgress,\n              sx: {\n                height: 8,\n                borderRadius: 4\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1,\n                display: \"block\"\n              },\n              children: \"Establishing secure connection...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), showBenefits && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Why Connect to QuickBooks?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: connectionBenefits.map((benefit, index) => /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 2,\n                backgroundColor: \"grey.50\"\n              },\n              children: /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                spacing: 2,\n                alignItems: \"center\",\n                children: [benefit.icon, /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: 600,\n                    children: benefit.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: benefit.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this), qboConnected && (company === null || company === void 0 ? void 0 : company.qboCompanyName) && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"success\",\n            sx: {\n              borderRadius: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n              children: \"ValiSights Company Successfully Connected to QuickBooks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Connected to: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: company.qboCompanyName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), (qboStatus === null || qboStatus === void 0 ? void 0 : qboStatus.qboRealmID) && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [\"Company ID: \", qboStatus.qboRealmID]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          justifyContent: \"flex-end\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => setShowDetails(false),\n            children: \"Close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), qboConnected ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 30\n              }, this),\n              onClick: onRefreshStatus,\n              disabled: qboLoading,\n              children: \"Refresh Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              onClick: onDisconnect,\n              disabled: qboLoading,\n              children: \"Disconnect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: \"relative\",\n              display: \"inline-block\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: qboButton,\n              alt: \"Connect to QuickBooks\",\n              sx: {\n                cursor: \"pointer\",\n                height: 44,\n                width: \"auto\",\n                transition: \"opacity 0.2s ease\",\n                \"&:hover\": {\n                  opacity: 0\n                },\n                opacity: qboLoading ? 0.5 : 1,\n                pointerEvents: qboLoading ? \"none\" : \"auto\"\n              },\n              onClick: !qboLoading ? onConnect : undefined\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: qboButtonHover,\n              alt: \"Connect to QuickBooks\",\n              sx: {\n                position: \"absolute\",\n                top: 0,\n                left: 0,\n                height: 44,\n                width: \"auto\",\n                opacity: 0,\n                transition: \"opacity 0.2s ease\",\n                \"&:hover\": {\n                  opacity: 1\n                },\n                pointerEvents: qboLoading ? \"none\" : \"auto\"\n              },\n              onClick: !qboLoading ? onConnect : undefined\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), qboLoading && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: \"absolute\",\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"rgba(255, 255, 255, 0.8)\",\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [showDetails && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: \"fixed\",\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: \"rgba(0,0,0,0.5)\",\n        zIndex: 1200\n      },\n      onClick: () => setShowDetails(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 9\n    }, this), showDetails && renderDetailedView()]\n  }, void 0, true);\n};\n_s(QuickBooksConnection, \"bWGsk0PyuYBH+nIGj7TdgInQKt0=\");\n_c = QuickBooksConnection;\nexport default QuickBooksConnection;\nvar _c;\n$RefreshReg$(_c, \"QuickBooksConnection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "CircularProgress", "Fade", "Slide", "Paper", "Divider", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LinearProgress", "IconButton", "<PERSON><PERSON><PERSON>", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Refresh", "RefreshIcon", "Info", "InfoIcon", "Launch", "LaunchIcon", "AccountBalance", "AccountBalanceIcon", "Security", "SecurityIcon", "Sync", "SyncIcon", "Close", "CloseIcon", "qboButton", "qboButtonHover", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuickBooksConnection", "qboConnected", "qboLoading", "qboStatus", "company", "onConnect", "onDisconnect", "onRefreshStatus", "showDetailedView", "_s", "showDetails", "setShowDetails", "connectionProgress", "setConnectionProgress", "showBenefits", "setShowBenefits", "interval", "setInterval", "prev", "Math", "random", "clearInterval", "connectionBenefits", "icon", "sx", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "renderConnectionStatus", "direction", "spacing", "alignItems", "children", "size", "thickness", "variant", "fontSize", "label", "backgroundColor", "fontWeight", "renderDetailedView", "in", "timeout", "elevation", "position", "top", "left", "transform", "width", "xs", "sm", "md", "maxHeight", "overflow", "zIndex", "borderRadius", "p", "justifyContent", "mb", "component", "src", "alt", "height", "onClick", "gutterBottom", "mt", "value", "display", "map", "benefit", "index", "qboCompanyName", "severity", "qboRealmID", "startIcon", "disabled", "cursor", "transition", "opacity", "pointerEvents", "undefined", "right", "bottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/QuickBooksConnection.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON>,\r\n  Card,\r\n  <PERSON>po<PERSON>,\r\n  <PERSON>ton,\r\n  <PERSON>ack,\r\n  Chip,\r\n  CircularProgress,\r\n  Fade,\r\n  Slide,\r\n  Paper,\r\n  Divider,\r\n  Alert,\r\n  AlertTitle,\r\n  LinearProgress,\r\n  IconButton,\r\n  Tooltip,\r\n} from \"@mui/material\";\r\nimport {\r\n  CheckCircle as CheckCircleIcon,\r\n  Error as ErrorIcon,\r\n  Refresh as RefreshIcon,\r\n  Info as InfoIcon,\r\n  Launch as LaunchIcon,\r\n  AccountBalance as AccountBalanceIcon,\r\n  Security as SecurityIcon,\r\n  Sync as SyncIcon,\r\n  Close as CloseIcon,\r\n} from \"@mui/icons-material\";\r\nimport qboButton from \"../../../assets/C2QB_green_btn_med_default.svg\";\r\nimport qboButtonHover from \"../../../assets/C2QB_green_btn_med_hover.svg\";\r\n\r\nconst QuickBooksConnection = ({\r\n  qboConnected,\r\n  qboLoading,\r\n  qboStatus,\r\n  company,\r\n  onConnect,\r\n  onDisconnect,\r\n  onRefreshStatus,\r\n  showDetailedView = false,\r\n}) => {\r\n  const [showDetails, setShowDetails] = useState(showDetailedView);\r\n  const [connectionProgress, setConnectionProgress] = useState(0);\r\n  const [showBenefits, setShowBenefits] = useState(!qboConnected);\r\n\r\n  useEffect(() => {\r\n    if (qboLoading) {\r\n      const interval = setInterval(() => {\r\n        setConnectionProgress((prev) => {\r\n          if (prev >= 90) return 90;\r\n          return prev + Math.random() * 10;\r\n        });\r\n      }, 200);\r\n      return () => clearInterval(interval);\r\n    } else {\r\n      setConnectionProgress(qboConnected ? 100 : 0);\r\n    }\r\n  }, [qboLoading, qboConnected]);\r\n\r\n  const connectionBenefits = [\r\n    {\r\n      icon: <SyncIcon sx={{ color: \"primary.main\" }} />,\r\n      title: \"Real-time Data Sync\",\r\n      description: \"Automatically sync your financial data in real-time\",\r\n    },\r\n    {\r\n      icon: <AccountBalanceIcon sx={{ color: \"success.main\" }} />,\r\n      title: \"Complete Financial Reports\",\r\n      description:\r\n        \"Access trial balance, P&L, balance sheet, and aging reports\",\r\n    },\r\n    {\r\n      icon: <SecurityIcon sx={{ color: \"warning.main\" }} />,\r\n      title: \"Secure Integration\",\r\n      description: \"Bank-level security with OAuth 2.0 authentication\",\r\n    },\r\n  ];\r\n\r\n  const renderConnectionStatus = () => {\r\n    if (qboLoading) {\r\n      return (\r\n        <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n          <CircularProgress size={20} thickness={4} />\r\n          <Typography variant=\"body2\" color=\"text.secondary\">\r\n            Connecting to QuickBooks...\r\n          </Typography>\r\n        </Stack>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n        {qboConnected ? (\r\n          <>\r\n            <CheckCircleIcon sx={{ color: \"success.main\", fontSize: 20 }} />\r\n            <Chip\r\n              label=\"Connected\"\r\n              size=\"small\"\r\n              sx={{\r\n                backgroundColor: \"success.light\",\r\n                color: \"success.dark\",\r\n                fontWeight: 600,\r\n              }}\r\n            />\r\n          </>\r\n        ) : (\r\n          <>\r\n            <ErrorIcon sx={{ color: \"error.main\", fontSize: 20 }} />\r\n            <Chip\r\n              label=\"Not Connected\"\r\n              size=\"small\"\r\n              sx={{\r\n                backgroundColor: \"error.light\",\r\n                color: \"error.dark\",\r\n                fontWeight: 600,\r\n              }}\r\n            />\r\n          </>\r\n        )}\r\n      </Stack>\r\n    );\r\n  };\r\n\r\n  const renderDetailedView = () => (\r\n    <Slide direction=\"up\" in={showDetails} timeout={400}>\r\n      <Paper\r\n        elevation={12}\r\n        sx={{\r\n          position: \"fixed\",\r\n          top: \"50%\",\r\n          left: \"50%\",\r\n          transform: \"translate(-50%, -50%)\",\r\n          width: { xs: \"90%\", sm: \"80%\", md: \"600px\" },\r\n          maxHeight: \"80vh\",\r\n          overflow: \"auto\",\r\n          zIndex: 1300,\r\n          borderRadius: 3,\r\n        }}\r\n      >\r\n        <Box sx={{ p: 4 }}>\r\n          {/* Header */}\r\n          <Stack\r\n            direction=\"row\"\r\n            justifyContent=\"space-between\"\r\n            alignItems=\"center\"\r\n            sx={{ mb: 3 }}\r\n          >\r\n            <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n              <Box\r\n                component=\"img\"\r\n                src={qboButton}\r\n                alt=\"QuickBooks\"\r\n                sx={{ height: 40, width: \"auto\" }}\r\n              />\r\n              <Stack direction=\"row\" spacing={1} alignItems=\"center\">\r\n                <Typography variant=\"h5\" fontWeight={700}>\r\n                  Connect ValiSights Company to\r\n                </Typography>\r\n                <Typography variant=\"h5\" fontWeight={700} color=\"primary.main\">\r\n                  QuickBooks\r\n                </Typography>\r\n              </Stack>\r\n            </Stack>\r\n            <IconButton onClick={() => setShowDetails(false)}>\r\n              <CloseIcon />\r\n            </IconButton>\r\n          </Stack>\r\n\r\n          <Divider sx={{ mb: 3 }} />\r\n\r\n          {/* Connection Status */}\r\n          <Box sx={{ mb: 4 }}>\r\n            <Typography variant=\"h6\" gutterBottom>\r\n              Connection Status\r\n            </Typography>\r\n            {renderConnectionStatus()}\r\n\r\n            {qboLoading && (\r\n              <Box sx={{ mt: 2 }}>\r\n                <LinearProgress\r\n                  variant=\"determinate\"\r\n                  value={connectionProgress}\r\n                  sx={{ height: 8, borderRadius: 4 }}\r\n                />\r\n                <Typography\r\n                  variant=\"caption\"\r\n                  color=\"text.secondary\"\r\n                  sx={{ mt: 1, display: \"block\" }}\r\n                >\r\n                  Establishing secure connection...\r\n                </Typography>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n\r\n          {/* Benefits Section */}\r\n          {showBenefits && (\r\n            <Box sx={{ mb: 4 }}>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Why Connect to QuickBooks?\r\n              </Typography>\r\n              <Stack spacing={2}>\r\n                {connectionBenefits.map((benefit, index) => (\r\n                  <Card key={index} sx={{ p: 2, backgroundColor: \"grey.50\" }}>\r\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n                      {benefit.icon}\r\n                      <Box>\r\n                        <Typography variant=\"subtitle2\" fontWeight={600}>\r\n                          {benefit.title}\r\n                        </Typography>\r\n                        <Typography variant=\"body2\" color=\"text.secondary\">\r\n                          {benefit.description}\r\n                        </Typography>\r\n                      </Box>\r\n                    </Stack>\r\n                  </Card>\r\n                ))}\r\n              </Stack>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Connected Company Info */}\r\n          {qboConnected && company?.qboCompanyName && (\r\n            <Box sx={{ mb: 4 }}>\r\n              <Alert severity=\"success\" sx={{ borderRadius: 2 }}>\r\n                <AlertTitle>\r\n                  ValiSights Company Successfully Connected to QuickBooks\r\n                </AlertTitle>\r\n                <Typography variant=\"body2\">\r\n                  Connected to: <strong>{company.qboCompanyName}</strong>\r\n                </Typography>\r\n                {qboStatus?.qboRealmID && (\r\n                  <Typography variant=\"caption\" color=\"text.secondary\">\r\n                    Company ID: {qboStatus.qboRealmID}\r\n                  </Typography>\r\n                )}\r\n              </Alert>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Action Buttons */}\r\n          <Stack direction=\"row\" spacing={2} justifyContent=\"flex-end\">\r\n            <Button variant=\"outlined\" onClick={() => setShowDetails(false)}>\r\n              Close\r\n            </Button>\r\n\r\n            {qboConnected ? (\r\n              <>\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  startIcon={<RefreshIcon />}\r\n                  onClick={onRefreshStatus}\r\n                  disabled={qboLoading}\r\n                >\r\n                  Refresh Status\r\n                </Button>\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"error\"\r\n                  onClick={onDisconnect}\r\n                  disabled={qboLoading}\r\n                >\r\n                  Disconnect\r\n                </Button>\r\n              </>\r\n            ) : (\r\n              <Box sx={{ position: \"relative\", display: \"inline-block\" }}>\r\n                <Box\r\n                  component=\"img\"\r\n                  src={qboButton}\r\n                  alt=\"Connect to QuickBooks\"\r\n                  sx={{\r\n                    cursor: \"pointer\",\r\n                    height: 44,\r\n                    width: \"auto\",\r\n                    transition: \"opacity 0.2s ease\",\r\n                    \"&:hover\": { opacity: 0 },\r\n                    opacity: qboLoading ? 0.5 : 1,\r\n                    pointerEvents: qboLoading ? \"none\" : \"auto\",\r\n                  }}\r\n                  onClick={!qboLoading ? onConnect : undefined}\r\n                />\r\n                <Box\r\n                  component=\"img\"\r\n                  src={qboButtonHover}\r\n                  alt=\"Connect to QuickBooks\"\r\n                  sx={{\r\n                    position: \"absolute\",\r\n                    top: 0,\r\n                    left: 0,\r\n                    height: 44,\r\n                    width: \"auto\",\r\n                    opacity: 0,\r\n                    transition: \"opacity 0.2s ease\",\r\n                    \"&:hover\": { opacity: 1 },\r\n                    pointerEvents: qboLoading ? \"none\" : \"auto\",\r\n                  }}\r\n                  onClick={!qboLoading ? onConnect : undefined}\r\n                />\r\n                {qboLoading && (\r\n                  <Box\r\n                    sx={{\r\n                      position: \"absolute\",\r\n                      top: 0,\r\n                      left: 0,\r\n                      right: 0,\r\n                      bottom: 0,\r\n                      display: \"flex\",\r\n                      alignItems: \"center\",\r\n                      justifyContent: \"center\",\r\n                      backgroundColor: \"rgba(255, 255, 255, 0.8)\",\r\n                      borderRadius: 1,\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={20} />\r\n                  </Box>\r\n                )}\r\n              </Box>\r\n            )}\r\n          </Stack>\r\n        </Box>\r\n      </Paper>\r\n    </Slide>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {showDetails && (\r\n        <Box\r\n          sx={{\r\n            position: \"fixed\",\r\n            top: 0,\r\n            left: 0,\r\n            right: 0,\r\n            bottom: 0,\r\n            backgroundColor: \"rgba(0,0,0,0.5)\",\r\n            zIndex: 1200,\r\n          }}\r\n          onClick={() => setShowDetails(false)}\r\n        />\r\n      )}\r\n      {showDetails && renderDetailedView()}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default QuickBooksConnection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,cAAc,IAAIC,kBAAkB,EACpCC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,SAAS,MAAM,gDAAgD;AACtE,OAAOC,cAAc,MAAM,8CAA8C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1E,MAAMC,oBAAoB,GAAGA,CAAC;EAC5BC,YAAY;EACZC,UAAU;EACVC,SAAS;EACTC,OAAO;EACPC,SAAS;EACTC,YAAY;EACZC,eAAe;EACfC,gBAAgB,GAAG;AACrB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAACkD,gBAAgB,CAAC;EAChE,MAAM,CAACI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,CAAC2C,YAAY,CAAC;EAE/D1C,SAAS,CAAC,MAAM;IACd,IAAI2C,UAAU,EAAE;MACd,MAAMc,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjCJ,qBAAqB,CAAEK,IAAI,IAAK;UAC9B,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE;UACzB,OAAOA,IAAI,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QAClC,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,MAAMC,aAAa,CAACL,QAAQ,CAAC;IACtC,CAAC,MAAM;MACLH,qBAAqB,CAACZ,YAAY,GAAG,GAAG,GAAG,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACC,UAAU,EAAED,YAAY,CAAC,CAAC;EAE9B,MAAMqB,kBAAkB,GAAG,CACzB;IACEC,IAAI,eAAE1B,OAAA,CAACN,QAAQ;MAACiC,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjDC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,eAAE1B,OAAA,CAACV,kBAAkB;MAACqC,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EACT;EACJ,CAAC,EACD;IACER,IAAI,eAAE1B,OAAA,CAACR,YAAY;MAACmC,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrDC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI9B,UAAU,EAAE;MACd,oBACEL,OAAA,CAACjC,KAAK;QAACqE,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAC,QAAA,gBACpDvC,OAAA,CAAC/B,gBAAgB;UAACuE,IAAI,EAAE,EAAG;UAACC,SAAS,EAAE;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5ChC,OAAA,CAACnC,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAACd,KAAK,EAAC,gBAAgB;UAAAW,QAAA,EAAC;QAEnD;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEZ;IAEA,oBACEhC,OAAA,CAACjC,KAAK;MAACqE,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAC,QAAA,EACnDnC,YAAY,gBACXJ,OAAA,CAAAE,SAAA;QAAAqC,QAAA,gBACEvC,OAAA,CAACpB,eAAe;UAAC+C,EAAE,EAAE;YAAEC,KAAK,EAAE,cAAc;YAAEe,QAAQ,EAAE;UAAG;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEhC,OAAA,CAAChC,IAAI;UACH4E,KAAK,EAAC,WAAW;UACjBJ,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YACFkB,eAAe,EAAE,eAAe;YAChCjB,KAAK,EAAE,cAAc;YACrBkB,UAAU,EAAE;UACd;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACF,CAAC,gBAEHhC,OAAA,CAAAE,SAAA;QAAAqC,QAAA,gBACEvC,OAAA,CAAClB,SAAS;UAAC6C,EAAE,EAAE;YAAEC,KAAK,EAAE,YAAY;YAAEe,QAAQ,EAAE;UAAG;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDhC,OAAA,CAAChC,IAAI;UACH4E,KAAK,EAAC,eAAe;UACrBJ,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YACFkB,eAAe,EAAE,aAAa;YAC9BjB,KAAK,EAAE,YAAY;YACnBkB,UAAU,EAAE;UACd;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACF;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,MAAMe,kBAAkB,GAAGA,CAAA,kBACzB/C,OAAA,CAAC7B,KAAK;IAACiE,SAAS,EAAC,IAAI;IAACY,EAAE,EAAEnC,WAAY;IAACoC,OAAO,EAAE,GAAI;IAAAV,QAAA,eAClDvC,OAAA,CAAC5B,KAAK;MACJ8E,SAAS,EAAE,EAAG;MACdvB,EAAE,EAAE;QACFwB,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXC,SAAS,EAAE,uBAAuB;QAClCC,KAAK,EAAE;UAAEC,EAAE,EAAE,KAAK;UAAEC,EAAE,EAAE,KAAK;UAAEC,EAAE,EAAE;QAAQ,CAAC;QAC5CC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,IAAI;QACZC,YAAY,EAAE;MAChB,CAAE;MAAAvB,QAAA,eAEFvC,OAAA,CAACrC,GAAG;QAACgE,EAAE,EAAE;UAAEoC,CAAC,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBAEhBvC,OAAA,CAACjC,KAAK;UACJqE,SAAS,EAAC,KAAK;UACf4B,cAAc,EAAC,eAAe;UAC9B1B,UAAU,EAAC,QAAQ;UACnBX,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,gBAEdvC,OAAA,CAACjC,KAAK;YAACqE,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACC,UAAU,EAAC,QAAQ;YAAAC,QAAA,gBACpDvC,OAAA,CAACrC,GAAG;cACFuG,SAAS,EAAC,KAAK;cACfC,GAAG,EAAEtE,SAAU;cACfuE,GAAG,EAAC,YAAY;cAChBzC,EAAE,EAAE;gBAAE0C,MAAM,EAAE,EAAE;gBAAEd,KAAK,EAAE;cAAO;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACFhC,OAAA,CAACjC,KAAK;cAACqE,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAACC,UAAU,EAAC,QAAQ;cAAAC,QAAA,gBACpDvC,OAAA,CAACnC,UAAU;gBAAC6E,OAAO,EAAC,IAAI;gBAACI,UAAU,EAAE,GAAI;gBAAAP,QAAA,EAAC;cAE1C;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhC,OAAA,CAACnC,UAAU;gBAAC6E,OAAO,EAAC,IAAI;gBAACI,UAAU,EAAE,GAAI;gBAAClB,KAAK,EAAC,cAAc;gBAAAW,QAAA,EAAC;cAE/D;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRhC,OAAA,CAACvB,UAAU;YAAC6F,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,KAAK,CAAE;YAAAyB,QAAA,eAC/CvC,OAAA,CAACJ,SAAS;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAERhC,OAAA,CAAC3B,OAAO;UAACsD,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BhC,OAAA,CAACrC,GAAG;UAACgE,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,gBACjBvC,OAAA,CAACnC,UAAU;YAAC6E,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAAAhC,QAAA,EAAC;UAEtC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZG,sBAAsB,CAAC,CAAC,EAExB9B,UAAU,iBACTL,OAAA,CAACrC,GAAG;YAACgE,EAAE,EAAE;cAAE6C,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,gBACjBvC,OAAA,CAACxB,cAAc;cACbkE,OAAO,EAAC,aAAa;cACrB+B,KAAK,EAAE1D,kBAAmB;cAC1BY,EAAE,EAAE;gBAAE0C,MAAM,EAAE,CAAC;gBAAEP,YAAY,EAAE;cAAE;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACFhC,OAAA,CAACnC,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBd,KAAK,EAAC,gBAAgB;cACtBD,EAAE,EAAE;gBAAE6C,EAAE,EAAE,CAAC;gBAAEE,OAAO,EAAE;cAAQ,CAAE;cAAAnC,QAAA,EACjC;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLf,YAAY,iBACXjB,OAAA,CAACrC,GAAG;UAACgE,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,gBACjBvC,OAAA,CAACnC,UAAU;YAAC6E,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAAAhC,QAAA,EAAC;UAEtC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhC,OAAA,CAACjC,KAAK;YAACsE,OAAO,EAAE,CAAE;YAAAE,QAAA,EACfd,kBAAkB,CAACkD,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACrC7E,OAAA,CAACpC,IAAI;cAAa+D,EAAE,EAAE;gBAAEoC,CAAC,EAAE,CAAC;gBAAElB,eAAe,EAAE;cAAU,CAAE;cAAAN,QAAA,eACzDvC,OAAA,CAACjC,KAAK;gBAACqE,SAAS,EAAC,KAAK;gBAACC,OAAO,EAAE,CAAE;gBAACC,UAAU,EAAC,QAAQ;gBAAAC,QAAA,GACnDqC,OAAO,CAAClD,IAAI,eACb1B,OAAA,CAACrC,GAAG;kBAAA4E,QAAA,gBACFvC,OAAA,CAACnC,UAAU;oBAAC6E,OAAO,EAAC,WAAW;oBAACI,UAAU,EAAE,GAAI;oBAAAP,QAAA,EAC7CqC,OAAO,CAAC3C;kBAAK;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACbhC,OAAA,CAACnC,UAAU;oBAAC6E,OAAO,EAAC,OAAO;oBAACd,KAAK,EAAC,gBAAgB;oBAAAW,QAAA,EAC/CqC,OAAO,CAAC1C;kBAAW;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GAXC6C,KAAK;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA5B,YAAY,KAAIG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuE,cAAc,kBACtC9E,OAAA,CAACrC,GAAG;UAACgE,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eACjBvC,OAAA,CAAC1B,KAAK;YAACyG,QAAQ,EAAC,SAAS;YAACpD,EAAE,EAAE;cAAEmC,YAAY,EAAE;YAAE,CAAE;YAAAvB,QAAA,gBAChDvC,OAAA,CAACzB,UAAU;cAAAgE,QAAA,EAAC;YAEZ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhC,OAAA,CAACnC,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAAAH,QAAA,GAAC,gBACZ,eAAAvC,OAAA;gBAAAuC,QAAA,EAAShC,OAAO,CAACuE;cAAc;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACZ,CAAA1B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE0E,UAAU,kBACpBhF,OAAA,CAACnC,UAAU;cAAC6E,OAAO,EAAC,SAAS;cAACd,KAAK,EAAC,gBAAgB;cAAAW,QAAA,GAAC,cACvC,EAACjC,SAAS,CAAC0E,UAAU;YAAA;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eAGDhC,OAAA,CAACjC,KAAK;UAACqE,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAAC2B,cAAc,EAAC,UAAU;UAAAzB,QAAA,gBAC1DvC,OAAA,CAAClC,MAAM;YAAC4E,OAAO,EAAC,UAAU;YAAC4B,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,KAAK,CAAE;YAAAyB,QAAA,EAAC;UAEjE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAER5B,YAAY,gBACXJ,OAAA,CAAAE,SAAA;YAAAqC,QAAA,gBACEvC,OAAA,CAAClC,MAAM;cACL4E,OAAO,EAAC,UAAU;cAClBuC,SAAS,eAAEjF,OAAA,CAAChB,WAAW;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BsC,OAAO,EAAE5D,eAAgB;cACzBwE,QAAQ,EAAE7E,UAAW;cAAAkC,QAAA,EACtB;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThC,OAAA,CAAClC,MAAM;cACL4E,OAAO,EAAC,WAAW;cACnBd,KAAK,EAAC,OAAO;cACb0C,OAAO,EAAE7D,YAAa;cACtByE,QAAQ,EAAE7E,UAAW;cAAAkC,QAAA,EACtB;YAED;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHhC,OAAA,CAACrC,GAAG;YAACgE,EAAE,EAAE;cAAEwB,QAAQ,EAAE,UAAU;cAAEuB,OAAO,EAAE;YAAe,CAAE;YAAAnC,QAAA,gBACzDvC,OAAA,CAACrC,GAAG;cACFuG,SAAS,EAAC,KAAK;cACfC,GAAG,EAAEtE,SAAU;cACfuE,GAAG,EAAC,uBAAuB;cAC3BzC,EAAE,EAAE;gBACFwD,MAAM,EAAE,SAAS;gBACjBd,MAAM,EAAE,EAAE;gBACVd,KAAK,EAAE,MAAM;gBACb6B,UAAU,EAAE,mBAAmB;gBAC/B,SAAS,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAC;gBACzBA,OAAO,EAAEhF,UAAU,GAAG,GAAG,GAAG,CAAC;gBAC7BiF,aAAa,EAAEjF,UAAU,GAAG,MAAM,GAAG;cACvC,CAAE;cACFiE,OAAO,EAAE,CAACjE,UAAU,GAAGG,SAAS,GAAG+E;YAAU;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACFhC,OAAA,CAACrC,GAAG;cACFuG,SAAS,EAAC,KAAK;cACfC,GAAG,EAAErE,cAAe;cACpBsE,GAAG,EAAC,uBAAuB;cAC3BzC,EAAE,EAAE;gBACFwB,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPgB,MAAM,EAAE,EAAE;gBACVd,KAAK,EAAE,MAAM;gBACb8B,OAAO,EAAE,CAAC;gBACVD,UAAU,EAAE,mBAAmB;gBAC/B,SAAS,EAAE;kBAAEC,OAAO,EAAE;gBAAE,CAAC;gBACzBC,aAAa,EAAEjF,UAAU,GAAG,MAAM,GAAG;cACvC,CAAE;cACFiE,OAAO,EAAE,CAACjE,UAAU,GAAGG,SAAS,GAAG+E;YAAU;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,EACD3B,UAAU,iBACTL,OAAA,CAACrC,GAAG;cACFgE,EAAE,EAAE;gBACFwB,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPmC,KAAK,EAAE,CAAC;gBACRC,MAAM,EAAE,CAAC;gBACTf,OAAO,EAAE,MAAM;gBACfpC,UAAU,EAAE,QAAQ;gBACpB0B,cAAc,EAAE,QAAQ;gBACxBnB,eAAe,EAAE,0BAA0B;gBAC3CiB,YAAY,EAAE;cAChB,CAAE;cAAAvB,QAAA,eAEFvC,OAAA,CAAC/B,gBAAgB;gBAACuE,IAAI,EAAE;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;EAED,oBACEhC,OAAA,CAAAE,SAAA;IAAAqC,QAAA,GACG1B,WAAW,iBACVb,OAAA,CAACrC,GAAG;MACFgE,EAAE,EAAE;QACFwB,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPmC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT5C,eAAe,EAAE,iBAAiB;QAClCgB,MAAM,EAAE;MACV,CAAE;MACFS,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,KAAK;IAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,EACAnB,WAAW,IAAIkC,kBAAkB,CAAC,CAAC;EAAA,eACpC,CAAC;AAEP,CAAC;AAACnC,EAAA,CAzTIT,oBAAoB;AAAAuF,EAAA,GAApBvF,oBAAoB;AA2T1B,eAAeA,oBAAoB;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}