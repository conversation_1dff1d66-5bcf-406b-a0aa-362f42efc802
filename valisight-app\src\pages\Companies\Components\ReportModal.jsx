import { Calendar, X } from "lucide-react";
import DatePicker from "react-datepicker";
import { useState } from "react";
import "react-datepicker/dist/react-datepicker.css";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { GrRevert } from "react-icons/gr";
import { createRequest } from "../../../services/report";
import Swal from "sweetalert2";
import { Button, CircularProgress } from "@mui/material";
import { ReportRequest } from "../../../enums/report.enum";


const ConfirmationModal = ({ onBack, onConfirm, loading, setActiveTab }) => (
  <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
    <div className="bg-white w-[400px] rounded-2xl shadow-xl p-6">
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-xl font-semibold">Confirm to proceed!</h2>
        <button onClick={onBack} className="text-gray-500 hover:text-gray-700">
          <X size={20} />
        </button>
      </div>
      <p className="text-[#2E3A44] mb-6">Have you uploaded the latest data?</p>
      <div className="flex gap-3">
        
        <Button
          variant="outlined"
          onClick={() => setActiveTab("companyInfo")}
          disabled={loading}
          sx={{
            width: '50%', // Fixed width matching Upload/Sync buttons
            textTransform: 'none', // Keep original text case for "Go back"
          }}
        >
          {/* <GrRevert size={20} style={{ marginRight: '8px' }} /> */}
          GO BACK
        </Button>

        <Button
          variant="contained"
          color="primary"
          onClick={onConfirm}
          disabled={loading}
          sx={{
            width: '50%', // Fixed width matching Upload/Sync buttons
            textTransform: 'none', // Keep original text case for "Yes, Continue"
            // Loading state styling
            ...(loading && {
              cursor: "not-allowed",
              animation: "pulse 2s infinite",
              "@keyframes pulse": {
                "0%": { opacity: 0.8 },
                "50%": { opacity: 1 },
                "100%": { opacity: 0.8 },
              },
              // Disable hover effects during loading
              "&:hover": {
                transform: "none",
              },
            }),
            "&:disabled": {
              cursor: "not-allowed",
            },
          }}
        >
          {loading ? (
            <CircularProgress size={20} color="inherit" />
          ) : (
            "YES, CONTINUE"
          )}
        </Button>
      </div>
    </div>
  </div>
);

const ReportModal = ({
  onClose,
  companyFiles,
  companyId,
  companyFYEndDate,
  onSubmitSuccess,
  setActiveTab,
}) => {
  const [error, setError] = useState("");
  const [dates, setDates] = useState({
    monthly: null,
    Quarterly: null,
    csfa: null,
    gaap: null,
    weekly: null,
    deepsight: null,
  });
  const [files, setFiles] = useState({
    ar: null,
    ap: null,
    balance_sheet: null,
    income_statement: null,
    previous_flow_cash: null,
    transaction: null,
  });

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [selectedReport, setSelectedReport] = useState("");
  const [loading, setLoading] = useState(false);

  // Fiscal Year Logic for DeepSight
  const calculateFiscalYearOptions = () => {
    if (!companyFYEndDate) return [];

    const fyEndDate = new Date(companyFYEndDate);
    const fyEndMonth = fyEndDate.getMonth() + 1; // Convert to 1-based month (1-12)
    const today = new Date();
    const currentMonth = today.getMonth() + 1;
    const currentCalendarYear = today.getFullYear();

    // Determine current fiscal year
    let currentFY;
    if (currentMonth <= fyEndMonth) {
      currentFY = currentCalendarYear;
    } else {
      currentFY = currentCalendarYear + 1;
    }

    // Check if current FY should be enabled
    // Current FY is enabled only after the FY end month has completed
    const currentFYEnabled = currentMonth > fyEndMonth;

    // Generate selectable years (current FY + previous 4 FYs)
    const selectableYears = [];
    const startYear = currentFYEnabled ? currentFY : currentFY - 1;

    for (let i = 0; i < 5; i++) {
      const year = startYear - i;
      selectableYears.push({
        year,
        enabled: year < currentFY || (year === currentFY && currentFYEnabled)
      });
    }

    return selectableYears.sort((a, b) => b.year - a.year); // Sort descending
  };

  const fiscalYearOptions = calculateFiscalYearOptions();

  // Helper function to get fiscal year period display
  const getFiscalYearPeriod = (fy) => {
    if (!companyFYEndDate) return '';

    const fyEndDate = new Date(companyFYEndDate);
    const fyEndMonth = fyEndDate.getMonth() + 1;

    // FY start is fyEndMonth + 1 of previous year
    // FY end is fyEndMonth of current year
    const startMonth = fyEndMonth === 12 ? 1 : fyEndMonth + 1;
    const startYear = fyEndMonth === 12 ? fy : fy - 1;
    const endYear = fy;

    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return `(${monthNames[startMonth - 1]} ${startYear} - ${monthNames[fyEndMonth - 1]} ${endYear})`;
  };

  const hasChartOfAccounts = companyFiles?.some(
    (file) => file?.type === "CHART_OF_ACCOUNTS" && file?.documents?.length == 0
  );

  const hasTrialBalance = companyFiles?.some(
    (file) => file?.type === "TRIAL_BALANCE" && file?.documents?.length == 0
  );

  const available_report_requests = [
    {
      label: "ProfitPulse (Monthly)",
      value: ReportRequest.MONTHLY,
    },
    {
      label: "KPITrack (Benchmark)",
      value: ReportRequest.BENCHMARK,
    },
    {
      label: "GAAP Align",
      value: ReportRequest.GAAP,
    },
    {
      label: "FinCheck (Current State)",
      value: ReportRequest.CSFA,
    },
    {
      label: "FlowCast (13 Week)",
      value: ReportRequest.WEEKLY,
    },
    {
      label: "DeepSight",
      value: ReportRequest.DEEPSIGHT,
    }
  ];

  const handleFileUpload = (type, file) => {
    if (type === "transaction" && file) {
      setError("");
    }

    if (type === "previous_flow_cash" && file) {
      setError("");
    }
    if (type === "balance_sheet" || type === "income_statement") {
      setError("");
    }

    if (!file) {
      setFiles((prev) => ({
        ...prev,
        [type]: null,
      }));
      return;
    }

    const reader = new FileReader();

    reader.onload = () => {
      const base64Content = reader.result.split(",")[1]; // Extract base64 content
      setFiles((prev) => ({
        ...prev,
        [type]: {
          name: file.name,
          size: file.size,
          base64: base64Content,
        },
      }));
    };

    reader.onerror = () => {
      console.error("File could not be read.");
    };

    reader.readAsDataURL(file); // Read file as a data URL to get base64
  };

  const FileUploadSection = ({ type, label }) => {
    const file = files[type];
    return (
      <div className="flex items-center mb-4">
        <span className="text-[#2E3A44] w-40 font-medium">{label}</span>
        <div className="flex-1 flex justify-center ml-[8rem] ">
          {!file ? (
            <button
              className="w-24 py-2 text-blue-600 border text-sm border-blue-600 rounded hover:bg-blue-50"
              onClick={() => document.getElementById(`file-${type}`).click()}
            >
              Upload
            </button>
          ) : (
            <div className="flex min-w-[200px] items-center  justify-between border-[1px] border-slate-300 rounded-lg">
              <div className="flex items-center gap-2 px-2 py-[2px] rounded">
                <InsertDriveFileIcon />
                <div className="flex flex-col">
                  <p className="text-sm text-[#344054] ">
                    {file.name.length > 12
                      ? file.name.slice(0, 15) + "..."
                      : file.name}
                  </p>
                  <p className="text-sm text-[#475467]">
                    ({Math.round(file.size / 1024)} KB)
                  </p>
                </div>
              </div>
              <button onClick={() => handleFileUpload(type, null)}>
                <X className="w-5 m-1 h-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>
          )}
        </div>
        <input
          id={`file-${type}`}
          type="file"
          className="hidden"
          onChange={(e) => handleFileUpload(type, e.target.files[0])}
          accept=".xlsx,.csv"
        />
      </div>
    );
  };

  const handleDateChange = (date, reportType) => {
    setDates((prev) => ({
      ...prev,
      [reportType]: date,
    }));
  };



  const formatDate = (date) => {
    if (!date) return null;
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  };

  const handleSubmit = () => {
    if (selectedReport === ReportRequest.GAAP) {
      if (!files.balance_sheet && !files.income_statement) {
        setError("Both Balance Sheet and Income Statement files are required.");
        return; // Prevent submission if both are missing
      } else if (!files.balance_sheet) {
        setError("Balance Sheet file is required.");
        return;
      } else if (!files.income_statement) {
        setError("Income Statement file is required.");
        return;
      }
    }

    if (selectedReport === ReportRequest.WEEKLY && !files.transaction) {
      setError("Transaction detail file is required.");
      return;
    }

    if (selectedReport !== ReportRequest.WEEKLY) {
      setShowConfirmation(true);
    } else {
      handleConfirm();
    }
  };

  const handleConfirm = async () => {
    let payload;
    const fileArray = [];

    if (
      selectedReport === ReportRequest.WEEKLY ||
      selectedReport === ReportRequest.GAAP
    ) {
      Object.entries(files).forEach(([type, file]) => {
        if (file) {
          fileArray.push({
            type,
            name: file.name,
            content: file.base64,
          });
        }
      });

      payload = {
        request_type:
          selectedReport === ReportRequest.WEEKLY
            ? ReportRequest.WEEKLY
            : selectedReport,
        date: formatDate(dates[selectedReport]),
        files: fileArray,
      };
    } else {
      payload = {
        request_type: selectedReport,
        date: formatDate(dates[selectedReport]),
      };
    }

    payload["companyId"] = companyId;

    setLoading(true);
    try {
      const response = await createRequest(payload);
      if (response?.data?.statusCode === 201) {
        Swal.fire({
          toast: true,
          position: "top-end",
          icon: "success",
          title: "Reports are sent successfully!",
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
        });
        onSubmitSuccess();
        onClose();
      }
    } catch (err) {
      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: "Failed to send reports in email. Please try again.",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });
    } finally {
      setLoading(false); // Hide loader after API call ends
    }
  };

  return (
    <>
      {showConfirmation ? (
        <ConfirmationModal
          setActiveTab={setActiveTab}
          onBack={() => setShowConfirmation(false)}
          onConfirm={handleConfirm}
          loading={loading}
        />
      ) : (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
          <div className="bg-white w-[600px] rounded-2xl shadow-xl max-h-[700px] overflow-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-200">
            <div className="flex justify-between items-center p-6 border-b">
              <h2 className="text-xl font-semibold">Request new report</h2>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={onClose}
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6 space-y-4">
              {available_report_requests?.map((report) => (
                <div key={report.value} className="flex items-center gap-4">
                  <div className="flex items-center flex-1 gap-3">
                    <input
                      type="radio"
                      name="reportType"
                      id={report.value}
                      checked={selectedReport === report.value}
                      onChange={() => setSelectedReport(report.value)}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-600"
                    />
                    <label
                      htmlFor={report.value}
                      className="text-[#344054] font-medium"
                    >
                      {report.label}
                    </label>
                  </div>

                  {/* Conditionally render Year Picker for DeepSight or regular DatePicker for others */}
                  {report.value === ReportRequest.DEEPSIGHT ? (
                    <div className="relative w-48">
                      <select
                        value={dates[report.value] ? dates[report.value].getFullYear() : ""}
                        onChange={(e) => {
                          const selectedYear = parseInt(e.target.value);
                          if (selectedYear) {
                            // Check if the selected year is enabled
                            const yearOption = fiscalYearOptions.find(opt => opt.year === selectedYear);
                            if (yearOption && yearOption.enabled) {
                              const yearDate = new Date(selectedYear, 0, 1); // January 1st of selected year
                              handleDateChange(yearDate, report.value);
                            }
                          } else {
                            handleDateChange(null, report.value);
                          }
                        }}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none bg-white cursor-pointer"
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                          backgroundPosition: 'right 0.5rem center',
                          backgroundRepeat: 'no-repeat',
                          backgroundSize: '1.5em 1.5em'
                        }}
                      >
                        <option value="">Select fiscal year</option>
                        {fiscalYearOptions.map(({ year, enabled }) => (
                          <option
                            key={year}
                            value={year}
                            disabled={!enabled}
                            style={{
                              color: enabled ? 'inherit' : '#ccc',
                              backgroundColor: enabled ? 'inherit' : '#f5f5f5'
                            }}
                          >
                            FY {year} {getFiscalYearPeriod(year)} {!enabled ? '(Not Available)' : ''}
                          </option>
                        ))}
                      </select>
                    </div>
                  ) : (
                    <div className="relative w-48">
                      <DatePicker
                        selected={dates[report.value]}
                        onChange={(date) => handleDateChange(date, report.value)}
                        placeholderText="Select date"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <Calendar
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                        size={20}
                      />
                    </div>
                  )}
                </div>
              ))}

              {/* Show Trial Balance error only when a report type is selected and it requires trial balance */}
              {selectedReport && 
               selectedReport !== ReportRequest.DEEPSIGHT &&
               companyFiles?.filter((file) => file?.type === "TRIAL_BALANCE") &&
               companyFiles?.filter(
                 (file) => file?.type === "TRIAL_BALANCE"
               )[0]?.documents?.length === 0 && (
                 <p className="text-red-500 text-sm">
                   Trial Balance File is required
                 </p>
               )}

              {/* Show Chart of Accounts error only for GAAP report */}
              {selectedReport === ReportRequest.GAAP && (
                <div>
                  {companyFiles?.filter(
                    (file) => file?.type === "CHART_OF_ACCOUNTS"
                  ) &&
                    companyFiles?.filter(
                      (file) => file?.type === "CHART_OF_ACCOUNTS"
                    )[0]?.documents?.length === 0 && (
                      <p className="text-red-500 text-sm">
                        Chart Of Account File is required
                      </p>
                    )}
                </div>
              )}

              {selectedReport === ReportRequest.WEEKLY &&
                companyFiles?.filter(
                  (file) => file?.type === "TRIAL_BALANCE"
                ) &&
                companyFiles?.filter(
                  (file) => file?.type === "TRIAL_BALANCE"
                )[0]?.documents?.length > 0 && (
                  <div className="mt-6 pt-4 border-t">
                    <p className="text-sm text-gray-500 mb-4">
                      (Please upload the latest data)
                    </p>
                    <FileUploadSection type="ar" label="AR aging detail" />
                    <FileUploadSection type="ap" label="AP aging detail" />
                    <FileUploadSection
                      type="previous_flow_cash"
                      label="Previous FlowCast Edited"
                    />

                    <FileUploadSection
                      type="transaction"
                      label="Transaction detail"
                    />
                    {error && <p className="text-red-500 text-sm">{error}</p>}
                  </div>
                )}

              {!hasChartOfAccounts && !hasTrialBalance && (
                <div>
                  {selectedReport === ReportRequest.GAAP &&
                    companyFiles?.some(
                      (file) => file?.type === "TRIAL_BALANCE"
                    ) &&
                    companyFiles?.some(
                      (file) => file?.type === "CHART_OF_ACCOUNTS"
                    ) && (
                      <div className="mt-6 pt-4 border-t">
                        <FileUploadSection
                          type="balance_sheet"
                          label="Balance sheet"
                        />
                        <FileUploadSection
                          type="income_statement"
                          label="Income statement (monthly)"
                        />
                        {error && (
                          <p className="text-red-500 text-sm">{error}</p>
                        )}
                      </div>
                    )}
                </div>
              )}
            </div>

            <div className="p-6 flex gap-3 border-t">
              <Button
                variant="outlined"
                onClick={onClose}
                disabled={loading}
                sx={{
                  width: '50%', // Fixed width matching Upload/Sync buttons
                  textTransform: 'uppercase',
                }}
              >
                CANCEL
              </Button>

              <Button
                variant="contained"
                color="primary"
                onClick={handleSubmit}
                disabled={!dates[selectedReport] || !dates[selectedReport] || loading}
                sx={{
                  width: '50%', // Fixed width matching Upload/Sync buttons
                  textTransform: 'uppercase',
                  // Loading state styling
                  ...(loading && {
                    cursor: "not-allowed",
                    animation: "pulse 2s infinite",
                    "@keyframes pulse": {
                      "0%": { opacity: 0.8 },
                      "50%": { opacity: 1 },
                      "100%": { opacity: 0.8 },
                    },
                    // Disable hover effects during loading
                    "&:hover": {
                      transform: "none",
                    },
                  }),
                  "&:disabled": {
                    cursor: "not-allowed",
                  },
                }}
              >
                {loading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : ( 
                  "REQUEST"
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ReportModal;