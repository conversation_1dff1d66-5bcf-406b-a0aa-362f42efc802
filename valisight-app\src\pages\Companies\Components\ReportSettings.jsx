import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Switch,
  Card,
  Stack,
  Divider,
  TextField,
  Chip,
  IconButton,
  Tooltip,
  Button,
  useTheme,
  alpha,
  Snackbar,
  Alert,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  CircularProgress,
} from '@mui/material';

import {
  TuneOutlined as SettingsIcon,
  InfoOutlined as InfoIcon,
  CheckCircleOutlined as CheckIcon,
  SaveOutlined as SaveIcon,
  ScheduleOutlined as ComingSoonIcon,
} from '@mui/icons-material';

import {
  getContentSettingsByReportType,
  updateContentSettings
} from '../../../services/contentSettings';

// Fallback component for unsupported report types
const ComingSoonFallback = ({ reportType, reportTypeOptions }) => {
  const theme = useTheme();
  
  // Find the label for the selected report type
  const selectedReportLabel = reportTypeOptions.find(
    option => option.value === reportType
  )?.label || reportType;

  return (
    <Box sx={{ width: '100%' }}>
      {/* Coming Soon Banner */}
      <Card
        elevation={0}
        sx={{
          mb: 3,
          p: 4,
          backgroundColor: alpha('#1976d2', 0.05),
          border: `1px solid ${alpha('#1976d2', 0.2)}`,
          borderRadius: 2,
          textAlign: 'center',
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <ComingSoonIcon sx={{ color: '#1976d2', fontSize: '3rem' }} />
          <Box>
            <Typography
              variant="h5"
              sx={{
                color: '#1976d2',
                fontWeight: 600,
                fontSize: '1.3rem',
                mb: 1,
              }}
            >
              {selectedReportLabel} - Coming Soon!
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: 'text.secondary',
                fontSize: '1rem',
                maxWidth: '500px',
                margin: '0 auto',
              }}
            >
              This report type will be implemented soon.
            </Typography>
          </Box>
        </Box>
      </Card>
    </Box>
  );
};

const ReportSettings = ({ companyId }) => {
  const theme = useTheme();
  const [settings, setSettings] = useState({
    reportType: 'DEEPSIGHT',
    incomeStatement: {
      incomeSummary: true,
      netIncome: true,
      grossProfitMargin: true,
      netProfitMargin: true,
      roaAndRoe: true,
    },
    prompt: "",
  });

  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Report type options
  const reportTypeOptions = [
    {
      value: 'DEEPSIGHT',
      label: 'Deepsight'
    },
    {
      value: "profitpulse",
      label: 'ProfitPulse (Monthly)'
    },
    {
      value: "kpitrack",
      label: 'KPITrack (Benchmark)'
    },
    {
      value: "gaap",
      label: 'GAAP Align'
    },
    {
      value: "fincheck",
      label: 'FinCheck (Current State)'
    },
    {
      value: "flowcast",
      label: 'FlowCast (13 Week)'
    }
  ];

  // Check if the current report type is supported
  const isCurrentReportTypeSupported = settings.reportType === 'DEEPSIGHT';

  // Load existing settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      if (!companyId) return;

      try {
        setIsLoading(true);
        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');

        if (response.data.success && response.data.data) {
          const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;
          
          if (data) {
            setSettings(prev => ({
              ...prev,
              incomeStatement: data.chartSettings || prev.incomeStatement,
              prompt: data.promptDescription || prev.prompt,
            }));
          }
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        setErrorMessage('Failed to load settings');
        setShowError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [companyId]);

  const handleSwitchChange = (category, field) => (event) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [field]: event.target.checked
      }
    }));
    setHasChanges(true);
  };

  const handlePromptChange = (event) => {
    setSettings(prev => ({
      ...prev,
      prompt: event.target.value
    }));
    setHasChanges(true);
  };

  // Updated handler for report type change
  const handleReportTypeChange = async (event) => {
    const newReportType = event.target.value;
    setSettings(prev => ({
      ...prev,
      reportType: newReportType
    }));

    // If it's not DEEPSIGHT, don't try to load data - just show fallback
    if (newReportType !== 'DEEPSIGHT') {
      setHasChanges(false);
      return;
    }

    // Load settings for DEEPSIGHT
    if (!companyId) return;

    try {
      setIsLoading(true);
      const response = await getContentSettingsByReportType(companyId, newReportType);

      if (response.data.success && response.data.data) {
        const data = Array.isArray(response.data.data) ? response.data.data[0] : response.data.data;
        
        if (data) {
          setSettings(prev => ({
            ...prev,
            incomeStatement: data.chartSettings || {
              incomeSummary: true,
              netIncome: true,
              grossProfitMargin: true,
              netProfitMargin: true,
              roaAndRoe: true,
            },
            prompt: data.promptDescription || '',
          }));
        } else {
          setSettings(prev => ({
            ...prev,
            incomeStatement: {
              incomeSummary: true,
              netIncome: true,
              grossProfitMargin: true,
              netProfitMargin: true,
              roaAndRoe: true,
            },
            prompt: '',
          }));
        }
      }
      setHasChanges(false);
    } catch (error) {
      console.error('Error loading settings for report type:', error);
      setErrorMessage('Failed to load settings');
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!companyId || !isCurrentReportTypeSupported) return;

    setIsSaving(true);

    try {
      const payload = {
        chartSettings: settings.incomeStatement,
        promptDescription: settings.prompt,
      };

      await updateContentSettings(companyId, settings.reportType, payload);

      setHasChanges(false);
      setShowSuccess(true);
    } catch (error) {
      console.error('Error saving settings:', error);
      setErrorMessage(error.response?.data?.message || 'Failed to save settings');
      setShowError(true);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  const handleCloseError = () => {
    setShowError(false);
    setErrorMessage('');
  };

  const settingsOptions = [
    {
      key: 'incomeSummary',
      label: 'Income Summary',
      tooltip: 'Provides a high-level overview of total income for the selected period, showing revenue sources and major income categories.'
    },
    {
      key: 'netIncome',
      label: 'Net Income',
      tooltip: 'Shows the company\’s bottom-line profit after deducting all expenses, taxes, and interest. Indicates overall profitability for the period.'
    },
    {
      key: 'grossProfitMargin',
      label: 'Gross Profit Margin',
      tooltip: 'Displays the percentage of revenue remaining after accounting for the cost of goods sold (COGS). Useful for measuring core business profitability.'
    },
    {
      key: 'netProfitMargin',
      label: 'Net Profit Margin',
      tooltip: 'Shows the percentage of revenue converted into net profit after all expenses. Reflects overall efficiency in turning sales into profit.'
    },
    {
      key: 'roaAndRoe',
      label: 'ROA and ROE',
      tooltip: 'Return on Assets (ROA) measures how efficiently assets generate profit. Return on Equity (ROE) shows how effectively shareholder equity is used to deliver returns.'
    },
  ];

  if (isLoading) {
    return (
      <Box sx={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header Section with Report Type Dropdown */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ maxWidth: '300px', mb: 3 }}>
          <FormControl fullWidth size="small">
            <InputLabel 
              id="report-type-label"
              sx={{
                fontSize: '1rem',
                fontWeight: 500,
                color: 'text.secondary',
              }}
            >
              Report Type
            </InputLabel>
            <Select
              labelId="report-type-label"
              id="report-type-select"
              value={settings.reportType}
              label="Report Type"
              onChange={handleReportTypeChange}
              sx={{
                '& .MuiSelect-select': {
                  fontSize: '0.875rem',
                  padding: '15px 10px',
                  fontWeight: 500,
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'grey.400',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1976d2',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1976d2',
                },
              }}
            >
              {reportTypeOptions.map((option) => (
                <MenuItem 
                  key={option.value} 
                  value={option.value}
                  sx={{
                    fontSize: '0.875rem',
                    fontWeight: 500,
                  }}
                >
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Conditional Rendering: Show fallback for unsupported report types */}
      {!isCurrentReportTypeSupported ? (
        <ComingSoonFallback 
          reportType={settings.reportType} 
          reportTypeOptions={reportTypeOptions} 
        />
      ) : (
        <>
          {/* Report Components Section - Only for supported types */}
          <Card elevation={0}>
            <Box sx={{ mb: 3, maxWidth: '300px' }}>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  color: 'text.primary',
                  fontSize: '1rem',
                }}
              >
                Report Components
              </Typography>
              <Typography
                variant="body2"
                sx={{ color: 'text.secondary' }}
              >
                Select which financial metrics to include in your analysis
              </Typography>

              <Box sx={{ maxWidth: '600px' }}>
                {settingsOptions.map((option, index) => (
                  <Box 
                    key={option.key}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      py: 1,
                      borderBottom: index < settingsOptions.length - 1 ? '1px solid' : 'none',
                      borderBottomColor: 'grey.200',
                    }}
                  >
                    <Tooltip 
                      title={option.tooltip}
                      arrow
                      placement="top-start"
                      sx={{
                        '& .MuiTooltip-tooltip': {
                          fontSize: '0.875rem',
                          maxWidth: '350px',
                          lineHeight: 1.4,
                        }
                      }}
                    >
                      <Box sx={{ 
                        flex: 1,
                        '&:hover': {
                          backgroundColor: alpha('#1976d2', 0.04),
                          borderRadius: 1,
                          padding: '4px 8px',
                          margin: '-4px -8px',
                        }
                      }}>
                        <Typography
                          variant="body1"
                          sx={{
                            fontWeight: 500,
                            color: 'text.primary',
                            fontSize: '0.95rem',
                            lineHeight: 1.3,
                          }}
                        >
                          {option.label}
                        </Typography>
                      </Box>
                    </Tooltip>

                    <Switch
                      checked={settings.incomeStatement[option.key]}
                      onChange={handleSwitchChange('incomeStatement', option.key)}
                      inputProps={{ 'aria-label': 'controlled' }}
                      sx={{ ml: 2 }}
                    />
                  </Box>
                ))}
              </Box>
            </Box>
          </Card>

          {/* Custom Analysis Prompt Section - Only for supported types */}
          <Card
            elevation={0}
            sx={{
              mb: 3,
              width: 600,
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                borderColor: '#1976d2',
                backgroundColor: alpha('#1976d2', 0.02),
              },
            }}
          >
            <Box
              component="fieldset"
              sx={{
                border: '2px solid',
                borderColor: 'grey.400',
                borderRadius: 1,
                margin: 0,
                position: 'relative',
                backgroundColor: 'white',
                transition: 'border-color 0.2s ease-in-out',
                '&:focus-within': {
                  borderColor: '#1976d2',
                },
                '&:focus-within legend': {
                  color: '#1976d2',
                },
              }}
            >
              <Box
                component="legend"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  fontFamily: '"Roboto","Helvetica","Arial",sans-serif',
                  color: 'text.secondary',
                  padding: '0 2px',
                  marginLeft: 1,
                  transition: 'color 0.2s ease-in-out',
                }}
              >
                Prompt
              </Box>
              
              <TextField
                multiline
                rows={5}
                value={settings.prompt}
                onChange={handlePromptChange}
                placeholder="Sample: Summarize financial performance for the current fiscal year, covering expenses, operational efficiency, liquidity, and profit & loss (monthly, YTD, and 13-month trailing). Include a balance sheet review, cost and COGS analysis, and conclude with executive insights, key focus areas, and recommendations for 2025
"
                fullWidth
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                      border: 'none',
                    },
                    '&:hover fieldset': {
                      border: 'none',
                    },
                    '&.Mui-focused fieldset': {
                      border: 'none',
                    },
                  },
                  '& .MuiInputBase-input': {
                    fontSize: '0.875rem',
                    lineHeight: 1.6,
                    '&::placeholder': {
                      color: 'text.secondary',
                      opacity: 0.7,
                    },
                  },
                }}
              />
            </Box>
          </Card>

          {/* Save Button - Only for supported types */}
          <Box sx={{ mt: 4, width: '10%', justifySelf: 'flex-start' }}>
            <Button
              variant="contained"
              size="medium"
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              fullWidth
              sx={{
                textTransform: 'none',
              }}
            >
              {isSaving ? 'SAVING...' : 'SAVE'}
            </Button>
          </Box>
        </>
      )}

      {/* Success Snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={4000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSuccess}
          severity="success"
          variant="filled"
          sx={{
            backgroundColor: '#1976d2',
            '& .MuiAlert-icon': {
              color: 'white',
            },
          }}
        >
          Settings saved successfully!
        </Alert>
      </Snackbar>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseError}
          severity="error"
          variant="filled"
        >
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ReportSettings;