{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\Companies\\\\Components\\\\View.jsx\",\n  _s = $RefreshSig$();\nimport CircleIcon from \"@mui/icons-material/Circle\";\nimport MoreHorizIcon from \"@mui/icons-material/MoreHoriz\";\nimport { Avatar, Box, Button, Card, Chip, CircularProgress, Grid2 as Grid, Skeleton, Stack, Typography } from \"@mui/material\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { Link, useLocation, useParams } from \"react-router-dom\";\nimport Swal from \"sweetalert2\";\nimport { getOneById } from \"../../../services/company\";\nimport { connectQBO, disconnectQBO, getQBOStatus, syncAccounts, syncAPAging, syncARAging, syncBalanceSheet, syncProfitLoss, syncTrialBalance } from \"../../../services/qbo\";\nimport Breadcrumb from \"../../../shared-components/BreadCrumbs\";\nimport { getCookie, setCookie } from \"../../../utils/cookies\";\nimport { formatDate, getAvatarInitials } from \"../../../utils/shared\";\nimport qboButton from \"./../../../assets/C2QB_green_btn_med_default.svg\";\nimport qboButtonHover from \"./../../../assets/C2QB_green_btn_med_hover.svg\";\nimport CSVUploadModal from \"./CSVModal\";\nimport \"./Detail.css\";\nimport EditCompany from \"./Edit\";\nimport Reports from \"./Reports\";\nimport UploadableSection from \"./UploadableSection\";\nimport QuickBooksConnection from \"./QuickBooksConnection\";\nimport ReportSettings from \"./ReportSettings\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SYNC_TYPES = {\n  CHART_OF_ACCOUNTS: \"CHART_OF_ACCOUNTS\",\n  TRIAL_BALANCE: \"TRIAL_BALANCE\",\n  PROFIT_AND_LOSS: \"PROFIT_AND_LOSS\",\n  BALANCE_SHEET: \"BALANCE_SHEET\",\n  AP_AGING: \"AP_AGING\",\n  AR_AGING: \"AR_AGING\"\n};\nfunction CompanyDetail() {\n  _s();\n  var _location$state, _location$state2, _location$state4;\n  const location = useLocation();\n  const params = useParams();\n  const id = (location === null || location === void 0 ? void 0 : (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.id) || (params === null || params === void 0 ? void 0 : params.id);\n  const [activeTab, setActiveTab] = useState((location === null || location === void 0 ? void 0 : (_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.activeTab) || \"companyInfo\");\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [company, setCompany] = useState(null);\n  const [fileTypeSection, setFileTypeSection] = useState(null);\n  const [showModal, setShowModal] = useState(false);\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\n  const [qboConnected, setQboConnected] = useState(false);\n  const [qboLoading, setQboLoading] = useState(false);\n  const [qboStatus, setQboStatus] = useState(null);\n  const [initialLoading, setInitialLoading] = useState(true);\n  const [syncLoading, setSyncLoading] = useState({\n    [SYNC_TYPES.CHART_OF_ACCOUNTS]: false,\n    [SYNC_TYPES.TRIAL_BALANCE]: false,\n    [SYNC_TYPES.PROFIT_AND_LOSS]: false,\n    [SYNC_TYPES.BALANCE_SHEET]: false,\n    [SYNC_TYPES.AP_AGING]: false,\n    [SYNC_TYPES.AR_AGING]: false\n  });\n  const handleOpenModal = type => {\n    setShowModal(true);\n    setFileTypeSection(type);\n  };\n  const handleUpload = () => {\n    if (selectedFile) {}\n  };\n  function capitalizeFirstLetter(str) {\n    if (!str) return \"\"; // handle empty string\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  const handleSync = async type => {\n    setSyncLoading(prev => ({\n      ...prev,\n      [type]: true\n    }));\n    try {\n      var _response$data;\n      const payload = {\n        userId: company === null || company === void 0 ? void 0 : company.userId,\n        companyId: company === null || company === void 0 ? void 0 : company.id,\n        dumpToDatabase: true\n      };\n      const syncMap = {\n        [SYNC_TYPES.CHART_OF_ACCOUNTS]: {\n          fn: syncAccounts,\n          message: \"Chart of Accounts synced successfully\"\n        },\n        [SYNC_TYPES.TRIAL_BALANCE]: {\n          fn: syncTrialBalance,\n          message: \"Trial Balance synced successfully\"\n        },\n        [SYNC_TYPES.PROFIT_AND_LOSS]: {\n          fn: syncProfitLoss,\n          message: \"Profit and Loss synced successfully\"\n        },\n        [SYNC_TYPES.BALANCE_SHEET]: {\n          fn: syncBalanceSheet,\n          message: \"Balance Sheet synced successfully\"\n        },\n        [SYNC_TYPES.AP_AGING]: {\n          fn: syncAPAging,\n          message: \"AP Aging synced successfully\"\n        },\n        [SYNC_TYPES.AR_AGING]: {\n          fn: syncARAging,\n          message: \"AR Aging synced successfully\"\n        }\n      };\n      const syncItem = syncMap[type];\n      if (!syncItem) throw new Error(`Unknown sync type: ${type}`);\n      const response = await syncItem.fn(payload);\n      if ((_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        Swal.fire({\n          icon: \"success\",\n          title: \"Sync Successful\",\n          text: capitalizeFirstLetter(response.data.data.message || response.data.message || syncItem.message),\n          confirmButtonColor: \"#033BD7\"\n        });\n        await fetchData();\n      } else {\n        var _response$data2;\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Sync failed\");\n      }\n    } catch (error) {\n      var _error$response$data;\n      Swal.fire({\n        icon: \"error\",\n        title: \"Sync Error\",\n        text: ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"An error occurred during sync.\",\n        confirmButtonColor: \"#D33\"\n      });\n    } finally {\n      setSyncLoading(prev => ({\n        ...prev,\n        [type]: false\n      }));\n    }\n  };\n  const fetchData = useCallback(async () => {\n    try {\n      const response = await getOneById(id);\n      if (response.data.success) {\n        setCompany(response.data.company);\n        setQboConnected(response.data.company.qboConnectionStatus === \"CONNECTED\");\n        setQboStatus(response.data.company);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data2;\n      console.error(\"Error fetching company details:\", error);\n      Swal.fire({\n        icon: \"error\",\n        title: \"Failed to Fetch Company\",\n        text: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data2 = _error$response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || \"Failed to fetch Company Details. Please try again.\",\n        confirmButtonColor: \"#033BD7\"\n      });\n    } finally {}\n  }, [id]);\n  const fetchQBOStatus = useCallback(async () => {\n    try {\n      setQboLoading(true);\n      const response = await getQBOStatus(id);\n      if (response.data) {\n        setQboConnected(response.data.qboConnectionStatus === \"CONNECTED\");\n        setQboStatus(response.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching QBO status:\", error);\n      // Swal.fire({\n      //   icon: \"error\",\n      //   title: \"Connection Failed\",\n      //   text:\n      //     error.response?.data?.message ||\n      //     \"Failed to fetch QBO status. Please try again.\",\n      //   confirmButtonColor: \"#033BD7\",\n      // });\n      setQboConnected(false);\n    } finally {\n      setQboLoading(false);\n    }\n  }, [id]);\n  const handleQBOConnect = async () => {\n    try {\n      var _response$data3, _response$data3$data;\n      setQboLoading(true);\n      const response = await connectQBO(id);\n      if (response.data.success && (_response$data3 = response.data) !== null && _response$data3 !== void 0 && (_response$data3$data = _response$data3.data) !== null && _response$data3$data !== void 0 && _response$data3$data.url) {\n        var _response$data4, _response$data4$data;\n        window.location.href = (_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : (_response$data4$data = _response$data4.data) === null || _response$data4$data === void 0 ? void 0 : _response$data4$data.url;\n      } else {\n        throw new Error(\"Failed to get authorization URL\");\n      }\n    } catch (error) {\n      console.error(\"Error connecting to QBO:\", error);\n      setQboLoading(false);\n    }\n  };\n  const handleQBODisconnect = async () => {\n    try {\n      const result = await Swal.fire({\n        title: \"Disconnect ValiSights from QuickBooks?\",\n        text: \"This will stop syncing your financial data. You can reconnect anytime.\",\n        icon: \"warning\",\n        showCancelButton: true,\n        confirmButtonColor: \"#ef4444\",\n        //Rd for destructive action\n        cancelButtonColor: \"transparent\",\n        // Remove fill color\n        confirmButtonText: \"DISCONNECT\",\n        cancelButtonText: \"CANCEL\",\n        width: 500,\n        customClass: {\n          cancelButton: \"custom-cancel-button\"\n        },\n        didOpen: () => {\n          const style = document.createElement(\"style\");\n          style.textContent = `\n      .custom-cancel-button {\n        border: 2px solid #1c66bd !important;\n        background-color: transparent !important;\n        color: #1c66bd !important;\n      }\n      .custom-cancel-button:hover {\n        background-color: #1c66bd !important;\n        color: white !important;\n      }\n    `;\n          document.head.appendChild(style);\n        }\n      });\n      if (result.isConfirmed) {\n        setQboLoading(true);\n        await disconnectQBO(id);\n        await fetchQBOStatus();\n        Swal.fire({\n          icon: \"success\",\n          title: \"Successfully Disconnected\",\n          text: \"ValiSights has been disconnected from QuickBooks. You can reconnect anytime.\",\n          confirmButtonColor: \"#033BD7\",\n          confirmButtonText: \"Got it\"\n        });\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Error disconnecting QBO:\", error);\n      Swal.fire({\n        icon: \"error\",\n        title: \"Disconnect Failed\",\n        text: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Unable to disconnect from QuickBooks. Please check your connection and try again.\",\n        confirmButtonColor: \"#033BD7\",\n        confirmButtonText: \"Try again\",\n        showCancelButton: true,\n        cancelButtonText: \"Cancel\",\n        cancelButtonColor: \"#64748b\"\n      });\n    } finally {\n      setQboLoading(false);\n    }\n  };\n\n  // const checkQBOMessages = () => {\n  //   const qboStatus = getCookie(\"qbo_status\");\n  //   if (qboStatus) {\n  //     const decodedMessage = decodeURIComponent(qboStatus);\n  //     Swal.fire({\n  //       icon: \"success\",\n  //       title: \"QuickBooks Connected\",\n  //       text: decodedMessage,\n  //       confirmButtonColor: \"#033BD7\",\n  //     });\n\n  //     setCookie(\"qbo_status\", \"\", -1);\n  //   }\n\n  //   // const qboError = getCookie(\"qbo_error\");\n  //   // if (qboError) {\n  //   //   const decodedMessage = decodeURIComponent(qboError);\n  //   //   Swal.fire({\n  //   //     icon: \"error\",\n  //   //     title: \"QuickBooks Connection Failed\",\n  //   //     text: decodedMessage,\n  //   //     confirmButtonColor: \"#033BD7\",\n  //   //   });\n\n  //   //   setCookie(\"qbo_error\", \"\", -1);\n  //   // }\n  // };\n\n  useEffect(() => {\n    var _location$state3;\n    if (location !== null && location !== void 0 && (_location$state3 = location.state) !== null && _location$state3 !== void 0 && _location$state3.activeTab) {\n      setActiveTab(location.state.activeTab);\n    }\n  }, [location === null || location === void 0 ? void 0 : (_location$state4 = location.state) === null || _location$state4 === void 0 ? void 0 : _location$state4.activeTab]);\n  useEffect(() => {\n    if (!id) return;\n    const fetchAll = async () => {\n      setInitialLoading(true);\n      await fetchData();\n      setInitialLoading(false);\n    };\n    fetchAll();\n\n    // Check if user returned from QuickBooks connect page without connecting\n    // (e.g., look for a specific cookie or status in the URL)\n    // const qboStatus = getCookie(\"qbo_status\");\n    // const qboError = getCookie(\"qbo_error\");\n    // if (qboStatus) {\n    //   const decodedMessage = decodeURIComponent(qboStatus);\n    //   Swal.fire({\n    //     icon: \"success\",\n    //     title: \"QuickBooks Connected\",\n    //     text: decodedMessage,\n    //     confirmButtonColor: \"#033BD7\",\n    //   });\n    //   setCookie(\"qbo_status\", \"\", -1);\n    // } else if (qboError) {\n    //   const decodedMessage = decodeURIComponent(qboError);\n    //   Swal.fire({\n    //     icon: \"error\",\n    //     title: \"QuickBooks Connection Failed\",\n    //     text: decodedMessage,\n    //     confirmButtonColor: \"#033BD7\",\n    //   });\n    //   setCookie(\"qbo_error\", \"\", -1);\n    // }\n  }, [id, fetchData, location.pathname]);\n\n  // useEffect(() => {\n  //   checkQBOMessages();\n  // }, [location.pathname]);\n\n  const handleEdit = () => {\n    if (qboLoading || initialLoading) return;\n    if (company) {\n      setIsEditModalOpen(true);\n    }\n  };\n  const handleSaveCompany = updatedCompany => {\n    setCompany(updatedCompany);\n    fetchData();\n  };\n  const CompanyHeaderSkeleton = () => /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      p: 3,\n      mb: 2,\n      border: \"1px solid #e5e7eb\",\n      borderRadius: 2,\n      boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n      backgroundColor: \"#fff\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      justifyContent: \"space-between\",\n      alignItems: \"flex-start\",\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 3,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"rounded\",\n          width: 64,\n          height: 64,\n          sx: {\n            borderRadius: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 200,\n            height: 32,\n            sx: {\n              mb: 0.5,\n              fontSize: \"1.5rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 280,\n            height: 22,\n            sx: {\n              mb: 1,\n              fontSize: \"0.95rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"center\",\n            children: /*#__PURE__*/_jsxDEV(Skeleton, {\n              variant: \"rounded\",\n              width: 90,\n              height: 22,\n              sx: {\n                borderRadius: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"rounded\",\n          width: 60,\n          height: 36\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"rounded\",\n          width: 220,\n          height: 36\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"rounded\",\n          width: 180,\n          height: 36\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        pt: 2,\n        borderTop: 1,\n        borderColor: \"divider\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 6,\n            lg: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 60,\n            height: 14,\n            sx: {\n              mb: 0.5,\n              fontSize: \"0.75rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"100%\",\n            height: 18,\n            sx: {\n              fontSize: \"0.875rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 6,\n            lg: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 90,\n            height: 14,\n            sx: {\n              mb: 0.5,\n              fontSize: \"0.75rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"100%\",\n            height: 18,\n            sx: {\n              fontSize: \"0.875rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 6,\n            lg: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 40,\n            height: 14,\n            sx: {\n              mb: 0.5,\n              fontSize: \"0.75rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"100%\",\n            height: 18,\n            sx: {\n              fontSize: \"0.875rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          size: {\n            xs: 6,\n            lg: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 50,\n            height: 14,\n            sx: {\n              mb: 0.5,\n              fontSize: \"0.75rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"100%\",\n            height: 18,\n            sx: {\n              fontSize: \"0.875rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        pt: 2,\n        borderTop: 1,\n        borderColor: \"divider\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: 130,\n        height: 14,\n        sx: {\n          mb: 0.5,\n          fontSize: \"0.75rem\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: \"85%\",\n        height: 18,\n        sx: {\n          fontSize: \"0.875rem\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: \"60%\",\n        height: 18,\n        sx: {\n          fontSize: \"0.875rem\",\n          mt: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 5\n  }, this);\n  const TabsSkeleton = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      px: 3,\n      py: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      sx: {\n        borderBottom: 1,\n        borderColor: \"divider\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: 100,\n        height: 20,\n        sx: {\n          pb: 1,\n          fontSize: 14\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: 70,\n        height: 20,\n        sx: {\n          pb: 1,\n          fontSize: 14\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: 110,\n        height: 20,\n        sx: {\n          pb: 1,\n          fontSize: 14\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 541,\n    columnNumber: 5\n  }, this);\n  const UploadableSectionSkeleton = () => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 1.5,\n    children: [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(Grid, {\n      size: {\n        xs: 12,\n        sm: 6,\n        lg: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          p: 1.5,\n          border: \"1px solid #e5e7eb\",\n          borderRadius: 1,\n          boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n          backgroundColor: \"#fff\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          justifyContent: \"space-between\",\n          alignItems: \"flex-start\",\n          sx: {\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: 140,\n            height: 18,\n            sx: {\n              fontSize: \"0.875rem\",\n              fontWeight: 600\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 0.5,\n            children: /*#__PURE__*/_jsxDEV(Skeleton, {\n              variant: \"rounded\",\n              width: 50,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"text\",\n          width: \"75%\",\n          height: 14,\n          sx: {\n            fontSize: \"0.75rem\",\n            marginBottom: \"20px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 0.5,\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"rounded\",\n            width: 50,\n            height: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"rounded\",\n            width: 50,\n            height: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this)\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 583,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        my: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Breadcrumb, {\n      companyName: company === null || company === void 0 ? void 0 : company.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"px-4\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            px: 3,\n            py: 2\n          },\n          children: initialLoading ? /*#__PURE__*/_jsxDEV(TabsSkeleton, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 4,\n            sx: {\n              borderBottom: 1,\n              borderColor: \"divider\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setActiveTab(\"companyInfo\"),\n              sx: {\n                pb: 1,\n                fontSize: 14,\n                fontWeight: 600,\n                textTransform: \"none\",\n                color: activeTab === \"companyInfo\" ? \"primary.main\" : \"text.secondary\",\n                borderBottom: 2,\n                borderColor: activeTab === \"companyInfo\" ? \"primary.main\" : \"transparent\",\n                borderRadius: 0,\n                \"&:hover\": {\n                  backgroundColor: \"transparent\",\n                  color: activeTab === \"companyInfo\" ? \"primary.main\" : \"text.primary\"\n                }\n              },\n              children: \"Company info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setActiveTab(\"reports\"),\n              sx: {\n                pb: 1,\n                fontSize: 14,\n                fontWeight: 600,\n                textTransform: \"none\",\n                color: activeTab === \"reports\" ? \"primary.main\" : \"text.secondary\",\n                borderBottom: 2,\n                borderColor: activeTab === \"reports\" ? \"primary.main\" : \"transparent\",\n                borderRadius: 0,\n                \"&:hover\": {\n                  backgroundColor: \"transparent\",\n                  color: activeTab === \"reports\" ? \"primary.main\" : \"text.primary\"\n                }\n              },\n              children: \"Reports\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setActiveTab(\"report-settings\"),\n              sx: {\n                pb: 1,\n                fontSize: 14,\n                fontWeight: 600,\n                textTransform: \"none\",\n                color: activeTab === \"report-settings\" ? \"primary.main\" : \"text.secondary\",\n                borderBottom: 2,\n                borderColor: activeTab === \"report-settings\" ? \"primary.main\" : \"transparent\",\n                borderRadius: 0,\n                \"&:hover\": {\n                  backgroundColor: \"transparent\",\n                  color: activeTab === \"report-settings\" ? \"primary.main\" : \"text.primary\"\n                }\n              },\n              children: \"Report Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 1.5\n          },\n          children: initialLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(CompanyHeaderSkeleton, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(UploadableSectionSkeleton, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 15\n          }, this) : activeTab === \"companyInfo\" ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 3,\n                mb: 2,\n                border: \"1px solid #e5e7eb\",\n                borderRadius: 1,\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                \"&:hover\": {\n                  boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n                },\n                transition: \"all 0.2s ease-in-out\",\n                backgroundColor: \"#fff\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                justifyContent: \"space-between\",\n                alignItems: \"flex-start\",\n                sx: {\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 3,\n                  alignItems: \"center\",\n                  children: [company !== null && company !== void 0 && company.logo ? /*#__PURE__*/_jsxDEV(Avatar, {\n                    src: company.logo,\n                    alt: \"Company Logo\",\n                    sx: {\n                      height: 64,\n                      width: 64,\n                      borderRadius: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      width: 64,\n                      height: 64,\n                      background: \"#3b82f6\",\n                      borderRadius: 2,\n                      fontSize: 20,\n                      fontWeight: \"bold\",\n                      color: \"#fff\"\n                    },\n                    children: getAvatarInitials(company === null || company === void 0 ? void 0 : company.name)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      sx: {\n                        fontWeight: 600,\n                        mb: 0.5,\n                        fontSize: \"1.5rem\",\n                        color: \"#111827\"\n                      },\n                      children: company === null || company === void 0 ? void 0 : company.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 800,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      sx: {\n                        color: \"#6b7280\",\n                        mb: 1,\n                        fontSize: \"0.95rem\"\n                      },\n                      children: company !== null && company !== void 0 && company.qboCompanyName ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [\"QuickBooks Company:\\xA0\", /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: \"#1976d2\",\n                            fontWeight: \"500\",\n                            textDecoration: \"none\"\n                          },\n                          children: company === null || company === void 0 ? void 0 : company.qboCompanyName\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 825,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true) : null\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      spacing: 2,\n                      alignItems: \"center\",\n                      children: /*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        alignItems: \"center\",\n                        children: qboConnected ? /*#__PURE__*/_jsxDEV(Chip, {\n                          icon: /*#__PURE__*/_jsxDEV(CircleIcon, {\n                            sx: {\n                              fontSize: \"8px !important\",\n                              color: \"#10b981 !important\"\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 848,\n                            columnNumber: 35\n                          }, this),\n                          label: \"Connected\",\n                          size: \"small\",\n                          sx: {\n                            backgroundColor: \"#d1fae5\",\n                            color: \"#065f46\",\n                            border: \"none\",\n                            height: 24,\n                            fontSize: 12,\n                            fontWeight: 500,\n                            borderRadius: \"12px\",\n                            \"& .MuiChip-icon\": {\n                              color: \"#10b981\",\n                              marginLeft: \"6px\"\n                            },\n                            \"& .MuiChip-label\": {\n                              paddingLeft: \"9px\",\n                              paddingRight: \"8px\"\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 846,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                          icon: /*#__PURE__*/_jsxDEV(CircleIcon, {\n                            sx: {\n                              fontSize: \"8px !important\",\n                              color: \"#ef4444 !important\"\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 878,\n                            columnNumber: 35\n                          }, this),\n                          label: \"Not Connected\",\n                          size: \"small\",\n                          sx: {\n                            backgroundColor: \"#fee2e2\",\n                            color: \"#991b1b\",\n                            border: \"none\",\n                            height: 24,\n                            fontSize: 12,\n                            fontWeight: 500,\n                            borderRadius: \"12px\",\n                            \"& .MuiChip-icon\": {\n                              color: \"#ef4444\",\n                              marginLeft: \"6px\"\n                            },\n                            \"& .MuiChip-label\": {\n                              paddingLeft: \"9px\",\n                              paddingRight: \"8px\"\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 876,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 840,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 2,\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    size: \"small\",\n                    sx: {\n                      minWidth: 60,\n                      height: 36,\n                      padding: 0,\n                      backgroundColor: \"#fff\",\n                      color: \"#374151\",\n                      border: \"1px solid #e5e7eb\",\n                      boxShadow: \"none\",\n                      \"&:hover\": {\n                        backgroundColor: \"#f3f4f6\",\n                        boxShadow: \"none\"\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(MoreHorizIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 936,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 23\n                  }, this), qboConnected ? /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    size: \"small\",\n                    sx: {\n                      fontSize: \"14px\",\n                      fontWeight: 540,\n                      px: 12,\n                      width: 280,\n                      height: 36,\n                      textTransform: \"none\",\n                      borderRadius: \"2\",\n                      backgroundColor: \"#dc2626\",\n                      color: \"#fff\",\n                      whiteSpace: \"nowrap\",\n                      overflow: \"hidden\",\n                      textOverflow: \"ellipsis\",\n                      \"&:hover\": {\n                        backgroundColor: \"#b91c1c\"\n                      },\n                      \"&:disabled\": {\n                        backgroundColor: \"#e5e7eb\",\n                        color: \"#9ca3af\"\n                      }\n                    },\n                    onClick: handleQBODisconnect,\n                    disabled: qboLoading || initialLoading,\n                    children: qboLoading ? \"Disconnecting...\" : \"DISCONNECT FROM QUICKBOOKS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: \"relative\",\n                      minWidth: 220,\n                      height: 36\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      component: \"img\",\n                      src: qboButton,\n                      alt: \"Connect to QuickBooks\",\n                      sx: {\n                        cursor: \"pointer\",\n                        height: 36,\n                        width: \"100%\",\n                        transition: \"opacity 0.2s ease-in-out\",\n                        opacity: qboLoading || initialLoading ? 0.5 : 1,\n                        pointerEvents: qboLoading || initialLoading ? \"none\" : \"auto\",\n                        \"&:hover\": {\n                          opacity: 0\n                        }\n                      },\n                      onClick: !qboLoading && !initialLoading ? handleQBOConnect : undefined\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 979,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      component: \"img\",\n                      src: qboButtonHover,\n                      alt: \"Connect to QuickBooks\",\n                      sx: {\n                        position: \"absolute\",\n                        top: 0,\n                        left: 0,\n                        height: 36,\n                        width: \"100%\",\n                        opacity: 0,\n                        transition: \"opacity 0.2s ease-in-out\",\n                        \"&:hover\": {\n                          opacity: 1\n                        },\n                        pointerEvents: qboLoading || initialLoading ? \"none\" : \"auto\"\n                      },\n                      onClick: !qboLoading && !initialLoading ? handleQBOConnect : undefined\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 999,\n                      columnNumber: 27\n                    }, this), (qboLoading || initialLoading) && /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        position: \"absolute\",\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\",\n                        backgroundColor: \"rgba(255, 255, 255, 0.8)\",\n                        borderRadius: 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1036,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1022,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    sx: {\n                      fontSize: 14,\n                      px: 3,\n                      width: 200,\n                      height: 36,\n                      textTransform: \"none\",\n                      borderColor: \"#d1d5db\",\n                      color: \"#374151\",\n                      fontWeight: 500,\n                      borderRadius: \"2\",\n                      whiteSpace: \"nowrap\",\n                      overflow: \"hidden\",\n                      textOverflow: \"ellipsis\",\n                      \"&:hover\": {\n                        backgroundColor: \"#f9fafb\",\n                        borderColor: \"#9ca3af\"\n                      },\n                      \"&:disabled\": {\n                        backgroundColor: \"#f3f4f6\",\n                        color: \"#9ca3af\",\n                        borderColor: \"#e5e7eb\"\n                      }\n                    },\n                    onClick: () => handleEdit(),\n                    disabled: qboLoading || initialLoading,\n                    fullWidth: true,\n                    children: \"EDIT COMPANY INFO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1042,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  pt: 2,\n                  borderTop: 1,\n                  borderColor: \"divider\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [(company === null || company === void 0 ? void 0 : company.country) && /*#__PURE__*/_jsxDEV(Grid, {\n                    size: {\n                      xs: 6,\n                      lg: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: \"text.secondary\",\n                        fontWeight: 500,\n                        display: \"block\",\n                        mb: 0.5,\n                        textTransform: \"none\"\n                      },\n                      children: \"Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1084,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\"\n                      },\n                      children: [company === null || company === void 0 ? void 0 : company.country, company === null || company === void 0 ? void 0 : company.state, company === null || company === void 0 ? void 0 : company.city].filter(Boolean).join(\", \")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1096,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    size: {\n                      xs: 6,\n                      lg: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: \"text.secondary\",\n                        fontWeight: 500,\n                        display: \"block\",\n                        mb: 0.5,\n                        textTransform: \"none\"\n                      },\n                      children: \"Fiscal year end\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1109,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\"\n                      },\n                      children: formatDate(company === null || company === void 0 ? void 0 : company.fiscal_year_end)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1121,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    size: {\n                      xs: 6,\n                      lg: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: \"text.secondary\",\n                        fontWeight: 500,\n                        display: \"block\",\n                        mb: 0.5,\n                        textTransform: \"none\"\n                      },\n                      children: \"Naics\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1130,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\"\n                      },\n                      children: company === null || company === void 0 ? void 0 : company.naics\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1142,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 23\n                  }, this), (company === null || company === void 0 ? void 0 : company.market) && /*#__PURE__*/_jsxDEV(Grid, {\n                    size: {\n                      xs: 6,\n                      lg: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: \"text.secondary\",\n                        fontWeight: 500,\n                        display: \"block\",\n                        mb: 0.5,\n                        textTransform: \"none\"\n                      },\n                      children: \"Market\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1153,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\"\n                      },\n                      children: company === null || company === void 0 ? void 0 : company.market\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1165,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1152,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 19\n              }, this), (company === null || company === void 0 ? void 0 : company.description) && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  pt: 2,\n                  borderTop: 1,\n                  borderColor: \"divider\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: \"text.secondary\",\n                    fontWeight: 500,\n                    display: \"block\",\n                    mb: 0.5,\n                    textTransform: \"none\"\n                  },\n                  children: \"Critical company info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 23\n                }, this), company.description.length > 60 ? /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"details\",\n                  sx: {\n                    cursor: \"pointer\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"summary\",\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 600,\n                      color: \"text.primary\",\n                      \"&:hover\": {\n                        color: \"primary.main\"\n                      },\n                      transition: \"color 0.2s ease-in-out\"\n                    },\n                    children: [company.description.slice(0, 50), \"...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1199,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mt: 1,\n                      color: \"text.secondary\"\n                    },\n                    children: company.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1211,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1198,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600,\n                    color: \"text.primary\"\n                  },\n                  children: company === null || company === void 0 ? void 0 : company.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1177,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(QuickBooksConnection, {\n              qboConnected: qboConnected,\n              qboLoading: qboLoading,\n              qboStatus: qboStatus,\n              company: company,\n              onConnect: handleQBOConnect,\n              onDisconnect: handleQBODisconnect,\n              onRefreshStatus: fetchQBOStatus\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(UploadableSection, {\n              companyFiles: company === null || company === void 0 ? void 0 : company.companyFiles,\n              qboConnected: qboConnected,\n              qboLoading: qboLoading,\n              initialLoading: initialLoading,\n              onUpload: handleOpenModal,\n              onSync: handleSync,\n              syncLoading: syncLoading,\n              companyData: company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 15\n          }, this) : activeTab === \"reports\" ? /*#__PURE__*/_jsxDEV(Reports, {\n            setActiveTab: setActiveTab,\n            companyId: id,\n            companyFiles: company === null || company === void 0 ? void 0 : company.companyFiles\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1254,\n            columnNumber: 15\n          }, this) : activeTab === \"report-settings\" ? /*#__PURE__*/_jsxDEV(ReportSettings, {\n            companyId: id\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1260,\n            columnNumber: 15\n          }, this) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(CSVUploadModal, {\n      onClose: () => setShowModal(false),\n      onUpload: handleUpload,\n      companyId: id,\n      type: fileTypeSection,\n      onSuccess: fetchData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1278,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(EditCompany, {\n      isOpen: isEditModalOpen,\n      onClose: () => setIsEditModalOpen(false),\n      companyData: company,\n      onSave: handleSaveCompany\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1287,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(CompanyDetail, \"ZHP8sWIXOnEkesRA1tbjqZYhzq0=\", false, function () {\n  return [useLocation, useParams];\n});\n_c = CompanyDetail;\nexport default CompanyDetail;\nvar _c;\n$RefreshReg$(_c, \"CompanyDetail\");", "map": {"version": 3, "names": ["CircleIcon", "MoreHorizIcon", "Avatar", "Box", "<PERSON><PERSON>", "Card", "Chip", "CircularProgress", "Grid2", "Grid", "Skeleton", "<PERSON><PERSON>", "Typography", "React", "useCallback", "useEffect", "useState", "Link", "useLocation", "useParams", "<PERSON><PERSON>", "getOneById", "connectQBO", "disconnectQBO", "getQBOStatus", "syncAccounts", "syncAPAging", "syncARAging", "syncBalanceSheet", "syncProfitLoss", "syncTrialBalance", "Breadcrumb", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "formatDate", "getAvatarInitials", "qboButton", "qboButtonHover", "CSVUploadModal", "EditCompany", "Reports", "UploadableSection", "QuickBooksConnection", "ReportSettings", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SYNC_TYPES", "CHART_OF_ACCOUNTS", "TRIAL_BALANCE", "PROFIT_AND_LOSS", "BALANCE_SHEET", "AP_AGING", "AR_AGING", "CompanyDetail", "_s", "_location$state", "_location$state2", "_location$state4", "location", "params", "id", "state", "activeTab", "setActiveTab", "selectedFile", "setSelectedFile", "company", "setCompany", "fileTypeSection", "setFileTypeSection", "showModal", "setShowModal", "isEditModalOpen", "setIsEditModalOpen", "qboConnected", "setQboConnected", "qboLoading", "setQboLoading", "qboStatus", "setQboStatus", "initialLoading", "setInitialLoading", "syncLoading", "setSyncLoading", "handleOpenModal", "type", "handleUpload", "capitalizeFirstLetter", "str", "char<PERSON>t", "toUpperCase", "slice", "handleSync", "prev", "_response$data", "payload", "userId", "companyId", "dumpToDatabase", "syncMap", "fn", "message", "syncItem", "Error", "response", "data", "success", "fire", "icon", "title", "text", "confirmButtonColor", "fetchData", "_response$data2", "error", "_error$response$data", "qboConnectionStatus", "_error$response", "_error$response$data2", "console", "fetchQBOStatus", "handleQBOConnect", "_response$data3", "_response$data3$data", "url", "_response$data4", "_response$data4$data", "window", "href", "handleQBODisconnect", "result", "showCancelButton", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "width", "customClass", "cancelButton", "did<PERSON><PERSON>", "style", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "isConfirmed", "_error$response2", "_error$response2$data", "_location$state3", "fetchAll", "pathname", "handleEdit", "handleSaveCompany", "updatedCompany", "CompanyHeaderSkeleton", "sx", "p", "mb", "border", "borderRadius", "boxShadow", "backgroundColor", "children", "direction", "justifyContent", "alignItems", "spacing", "variant", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "pt", "borderTop", "borderColor", "container", "size", "xs", "lg", "mt", "TabsSkeleton", "px", "py", "borderBottom", "pb", "UploadableSectionSkeleton", "Array", "map", "_", "i", "sm", "fontWeight", "marginBottom", "my", "companyName", "name", "className", "onClick", "textTransform", "color", "transition", "logo", "src", "alt", "background", "qboCompanyName", "textDecoration", "label", "marginLeft", "paddingLeft", "paddingRight", "min<PERSON><PERSON><PERSON>", "padding", "whiteSpace", "overflow", "textOverflow", "disabled", "position", "component", "cursor", "opacity", "pointerEvents", "undefined", "top", "left", "right", "bottom", "display", "fullWidth", "country", "city", "filter", "Boolean", "join", "fiscal_year_end", "naics", "market", "description", "length", "onConnect", "onDisconnect", "onRefreshStatus", "companyFiles", "onUpload", "onSync", "companyData", "onClose", "onSuccess", "isOpen", "onSave", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/Companies/Components/View.jsx"], "sourcesContent": ["import CircleIcon from \"@mui/icons-material/Circle\";\r\nimport MoreHorizIcon from \"@mui/icons-material/MoreHoriz\";\r\nimport {\r\n  Avatar,\r\n  Box,\r\n  Button,\r\n  Card,\r\n  Chip,\r\n  CircularProgress,\r\n  Grid2 as Grid,\r\n  Skeleton,\r\n  Stack,\r\n  Typography,\r\n} from \"@mui/material\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { Link, useLocation, useParams } from \"react-router-dom\";\r\nimport Swal from \"sweetalert2\";\r\nimport { getOneById } from \"../../../services/company\";\r\nimport {\r\n  connectQBO,\r\n  disconnectQBO,\r\n  getQBOStatus,\r\n  syncAccounts,\r\n  syncAPAging,\r\n  syncARAging,\r\n  syncBalanceSheet,\r\n  syncProfitLoss,\r\n  syncTrialBalance,\r\n} from \"../../../services/qbo\";\r\nimport Breadcrumb from \"../../../shared-components/BreadCrumbs\";\r\nimport { getCookie, setCookie } from \"../../../utils/cookies\";\r\nimport { formatDate, getAvatarInitials } from \"../../../utils/shared\";\r\nimport qboButton from \"./../../../assets/C2QB_green_btn_med_default.svg\";\r\nimport qboButtonHover from \"./../../../assets/C2QB_green_btn_med_hover.svg\";\r\nimport CSVUploadModal from \"./CSVModal\";\r\nimport \"./Detail.css\";\r\nimport EditCompany from \"./Edit\";\r\nimport Reports from \"./Reports\";\r\nimport UploadableSection from \"./UploadableSection\";\r\nimport QuickBooksConnection from \"./QuickBooksConnection\";\r\nimport ReportSettings from \"./ReportSettings\";\r\n\r\nconst SYNC_TYPES = {\r\n  CHART_OF_ACCOUNTS: \"CHART_OF_ACCOUNTS\",\r\n  TRIAL_BALANCE: \"TRIAL_BALANCE\",\r\n  PROFIT_AND_LOSS: \"PROFIT_AND_LOSS\",\r\n  BALANCE_SHEET: \"BALANCE_SHEET\",\r\n  AP_AGING: \"AP_AGING\",\r\n  AR_AGING: \"AR_AGING\",\r\n};\r\n\r\nfunction CompanyDetail() {\r\n  const location = useLocation();\r\n  const params = useParams();\r\n  const id = location?.state?.id || params?.id;\r\n  const [activeTab, setActiveTab] = useState(\r\n    location?.state?.activeTab || \"companyInfo\"\r\n  );\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [company, setCompany] = useState(null);\r\n  const [fileTypeSection, setFileTypeSection] = useState(null);\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [isEditModalOpen, setIsEditModalOpen] = useState(false);\r\n  const [qboConnected, setQboConnected] = useState(false);\r\n  const [qboLoading, setQboLoading] = useState(false);\r\n  const [qboStatus, setQboStatus] = useState(null);\r\n  const [initialLoading, setInitialLoading] = useState(true);\r\n  const [syncLoading, setSyncLoading] = useState({\r\n    [SYNC_TYPES.CHART_OF_ACCOUNTS]: false,\r\n    [SYNC_TYPES.TRIAL_BALANCE]: false,\r\n    [SYNC_TYPES.PROFIT_AND_LOSS]: false,\r\n    [SYNC_TYPES.BALANCE_SHEET]: false,\r\n    [SYNC_TYPES.AP_AGING]: false,\r\n    [SYNC_TYPES.AR_AGING]: false,\r\n  });\r\n\r\n  const handleOpenModal = (type) => {\r\n    setShowModal(true);\r\n    setFileTypeSection(type);\r\n  };\r\n\r\n  const handleUpload = () => {\r\n    if (selectedFile) {\r\n    }\r\n  };\r\n  function capitalizeFirstLetter(str) {\r\n    if (!str) return \"\"; // handle empty string\r\n    return str.charAt(0).toUpperCase() + str.slice(1);\r\n  }\r\n\r\n  const handleSync = async (type) => {\r\n    setSyncLoading((prev) => ({ ...prev, [type]: true }));\r\n\r\n    try {\r\n      const payload = {\r\n        userId: company?.userId,\r\n        companyId: company?.id,\r\n        dumpToDatabase: true,\r\n      };\r\n\r\n      const syncMap = {\r\n        [SYNC_TYPES.CHART_OF_ACCOUNTS]: {\r\n          fn: syncAccounts,\r\n          message: \"Chart of Accounts synced successfully\",\r\n        },\r\n        [SYNC_TYPES.TRIAL_BALANCE]: {\r\n          fn: syncTrialBalance,\r\n          message: \"Trial Balance synced successfully\",\r\n        },\r\n        [SYNC_TYPES.PROFIT_AND_LOSS]: {\r\n          fn: syncProfitLoss,\r\n          message: \"Profit and Loss synced successfully\",\r\n        },\r\n        [SYNC_TYPES.BALANCE_SHEET]: {\r\n          fn: syncBalanceSheet,\r\n          message: \"Balance Sheet synced successfully\",\r\n        },\r\n        [SYNC_TYPES.AP_AGING]: {\r\n          fn: syncAPAging,\r\n          message: \"AP Aging synced successfully\",\r\n        },\r\n        [SYNC_TYPES.AR_AGING]: {\r\n          fn: syncARAging,\r\n          message: \"AR Aging synced successfully\",\r\n        },\r\n      };\r\n\r\n      const syncItem = syncMap[type];\r\n      if (!syncItem) throw new Error(`Unknown sync type: ${type}`);\r\n\r\n      const response = await syncItem.fn(payload);\r\n\r\n      if (response.data?.success) {\r\n        Swal.fire({\r\n          icon: \"success\",\r\n          title: \"Sync Successful\",\r\n          text: capitalizeFirstLetter(\r\n            response.data.data.message ||\r\n              response.data.message ||\r\n              syncItem.message\r\n          ),\r\n          confirmButtonColor: \"#033BD7\",\r\n        });\r\n        await fetchData();\r\n      } else {\r\n        throw new Error(response.data?.message || \"Sync failed\");\r\n      }\r\n    } catch (error) {\r\n      Swal.fire({\r\n        icon: \"error\",\r\n        title: \"Sync Error\",\r\n        text: error.response.data?.message || \"An error occurred during sync.\",\r\n        confirmButtonColor: \"#D33\",\r\n      });\r\n    } finally {\r\n      setSyncLoading((prev) => ({ ...prev, [type]: false }));\r\n    }\r\n  };\r\n\r\n  const fetchData = useCallback(async () => {\r\n    try {\r\n      const response = await getOneById(id);\r\n\r\n      if (response.data.success) {\r\n        setCompany(response.data.company);\r\n        setQboConnected(\r\n          response.data.company.qboConnectionStatus === \"CONNECTED\"\r\n        );\r\n        setQboStatus(response.data.company);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching company details:\", error);\r\n      Swal.fire({\r\n        icon: \"error\",\r\n        title: \"Failed to Fetch Company\",\r\n        text:\r\n          error.response?.data?.message ||\r\n          \"Failed to fetch Company Details. Please try again.\",\r\n        confirmButtonColor: \"#033BD7\",\r\n      });\r\n    } finally {\r\n    }\r\n  }, [id]);\r\n\r\n  const fetchQBOStatus = useCallback(async () => {\r\n    try {\r\n      setQboLoading(true);\r\n      const response = await getQBOStatus(id);\r\n      if (response.data) {\r\n        setQboConnected(response.data.qboConnectionStatus === \"CONNECTED\");\r\n        setQboStatus(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching QBO status:\", error);\r\n      // Swal.fire({\r\n      //   icon: \"error\",\r\n      //   title: \"Connection Failed\",\r\n      //   text:\r\n      //     error.response?.data?.message ||\r\n      //     \"Failed to fetch QBO status. Please try again.\",\r\n      //   confirmButtonColor: \"#033BD7\",\r\n      // });\r\n      setQboConnected(false);\r\n    } finally {\r\n      setQboLoading(false);\r\n    }\r\n  }, [id]);\r\n\r\n  const handleQBOConnect = async () => {\r\n    try {\r\n      setQboLoading(true);\r\n\r\n      const response = await connectQBO(id);\r\n\r\n      if (response.data.success && response.data?.data?.url) {\r\n        window.location.href = response.data?.data?.url;\r\n      } else {\r\n        throw new Error(\"Failed to get authorization URL\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error connecting to QBO:\", error);\r\n      setQboLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleQBODisconnect = async () => {\r\n    try {\r\n      const result = await Swal.fire({\r\n        title: \"Disconnect ValiSights from QuickBooks?\",\r\n        text: \"This will stop syncing your financial data. You can reconnect anytime.\",\r\n        icon: \"warning\",\r\n        showCancelButton: true,\r\n        confirmButtonColor: \"#ef4444\", //Rd for destructive action\r\n        cancelButtonColor: \"transparent\", // Remove fill color\r\n        confirmButtonText: \"DISCONNECT\",\r\n        cancelButtonText: \"CANCEL\",\r\n        width: 500,\r\n        customClass: {\r\n          cancelButton: \"custom-cancel-button\",\r\n        },\r\n        didOpen: () => {\r\n          const style = document.createElement(\"style\");\r\n          style.textContent = `\r\n      .custom-cancel-button {\r\n        border: 2px solid #1c66bd !important;\r\n        background-color: transparent !important;\r\n        color: #1c66bd !important;\r\n      }\r\n      .custom-cancel-button:hover {\r\n        background-color: #1c66bd !important;\r\n        color: white !important;\r\n      }\r\n    `;\r\n          document.head.appendChild(style);\r\n        },\r\n      });\r\n\r\n      if (result.isConfirmed) {\r\n        setQboLoading(true);\r\n        await disconnectQBO(id);\r\n        await fetchQBOStatus();\r\n\r\n        Swal.fire({\r\n          icon: \"success\",\r\n          title: \"Successfully Disconnected\",\r\n          text: \"ValiSights has been disconnected from QuickBooks. You can reconnect anytime.\",\r\n          confirmButtonColor: \"#033BD7\",\r\n          confirmButtonText: \"Got it\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error disconnecting QBO:\", error);\r\n\r\n      Swal.fire({\r\n        icon: \"error\",\r\n        title: \"Disconnect Failed\",\r\n        text:\r\n          error.response?.data?.message ||\r\n          \"Unable to disconnect from QuickBooks. Please check your connection and try again.\",\r\n        confirmButtonColor: \"#033BD7\",\r\n        confirmButtonText: \"Try again\",\r\n        showCancelButton: true,\r\n        cancelButtonText: \"Cancel\",\r\n        cancelButtonColor: \"#64748b\",\r\n      });\r\n    } finally {\r\n      setQboLoading(false);\r\n    }\r\n  };\r\n\r\n  // const checkQBOMessages = () => {\r\n  //   const qboStatus = getCookie(\"qbo_status\");\r\n  //   if (qboStatus) {\r\n  //     const decodedMessage = decodeURIComponent(qboStatus);\r\n  //     Swal.fire({\r\n  //       icon: \"success\",\r\n  //       title: \"QuickBooks Connected\",\r\n  //       text: decodedMessage,\r\n  //       confirmButtonColor: \"#033BD7\",\r\n  //     });\r\n\r\n  //     setCookie(\"qbo_status\", \"\", -1);\r\n  //   }\r\n\r\n  //   // const qboError = getCookie(\"qbo_error\");\r\n  //   // if (qboError) {\r\n  //   //   const decodedMessage = decodeURIComponent(qboError);\r\n  //   //   Swal.fire({\r\n  //   //     icon: \"error\",\r\n  //   //     title: \"QuickBooks Connection Failed\",\r\n  //   //     text: decodedMessage,\r\n  //   //     confirmButtonColor: \"#033BD7\",\r\n  //   //   });\r\n\r\n  //   //   setCookie(\"qbo_error\", \"\", -1);\r\n  //   // }\r\n  // };\r\n\r\n  useEffect(() => {\r\n    if (location?.state?.activeTab) {\r\n      setActiveTab(location.state.activeTab);\r\n    }\r\n  }, [location?.state?.activeTab]);\r\n\r\n  useEffect(() => {\r\n    if (!id) return;\r\n\r\n    const fetchAll = async () => {\r\n      setInitialLoading(true);\r\n      await fetchData();\r\n      setInitialLoading(false);\r\n    };\r\n\r\n    fetchAll();\r\n\r\n    // Check if user returned from QuickBooks connect page without connecting\r\n    // (e.g., look for a specific cookie or status in the URL)\r\n    // const qboStatus = getCookie(\"qbo_status\");\r\n    // const qboError = getCookie(\"qbo_error\");\r\n    // if (qboStatus) {\r\n    //   const decodedMessage = decodeURIComponent(qboStatus);\r\n    //   Swal.fire({\r\n    //     icon: \"success\",\r\n    //     title: \"QuickBooks Connected\",\r\n    //     text: decodedMessage,\r\n    //     confirmButtonColor: \"#033BD7\",\r\n    //   });\r\n    //   setCookie(\"qbo_status\", \"\", -1);\r\n    // } else if (qboError) {\r\n    //   const decodedMessage = decodeURIComponent(qboError);\r\n    //   Swal.fire({\r\n    //     icon: \"error\",\r\n    //     title: \"QuickBooks Connection Failed\",\r\n    //     text: decodedMessage,\r\n    //     confirmButtonColor: \"#033BD7\",\r\n    //   });\r\n    //   setCookie(\"qbo_error\", \"\", -1);\r\n    // }\r\n  }, [id, fetchData, location.pathname]);\r\n\r\n  // useEffect(() => {\r\n  //   checkQBOMessages();\r\n  // }, [location.pathname]);\r\n\r\n  const handleEdit = () => {\r\n    if (qboLoading || initialLoading) return;\r\n\r\n    if (company) {\r\n      setIsEditModalOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleSaveCompany = (updatedCompany) => {\r\n    setCompany(updatedCompany);\r\n    fetchData();\r\n  };\r\n  const CompanyHeaderSkeleton = () => (\r\n    <Card\r\n      sx={{\r\n        p: 3,\r\n        mb: 2,\r\n        border: \"1px solid #e5e7eb\",\r\n        borderRadius: 2,\r\n        boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\r\n        backgroundColor: \"#fff\",\r\n      }}\r\n    >\r\n      {/* Header Section - matches Stack direction=\"row\" justifyContent=\"space-between\" */}\r\n      <Stack\r\n        direction=\"row\"\r\n        justifyContent=\"space-between\"\r\n        alignItems=\"flex-start\"\r\n        sx={{ mb: 3 }}\r\n      >\r\n        {/* Left side - Avatar and company info */}\r\n        <Stack direction=\"row\" spacing={3} alignItems=\"center\">\r\n          {/* Avatar - matches 64x64 size */}\r\n          <Skeleton\r\n            variant=\"rounded\"\r\n            width={64}\r\n            height={64}\r\n            sx={{ borderRadius: 2 }}\r\n          />\r\n\r\n          <Box>\r\n            {/* Company name - matches Typography variant=\"h5\" fontSize=\"1.5rem\" */}\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width={200}\r\n              height={32}\r\n              sx={{ mb: 0.5, fontSize: \"1.5rem\" }}\r\n            />\r\n\r\n            {/* QuickBooks Company line - matches Typography variant=\"body1\" fontSize=\"0.95rem\" */}\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width={280}\r\n              height={22}\r\n              sx={{ mb: 1, fontSize: \"0.95rem\" }}\r\n            />\r\n\r\n            {/* Connection Status line - matches Stack with chip */}\r\n            <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n              <Skeleton\r\n                variant=\"rounded\"\r\n                width={90}\r\n                height={22}\r\n                sx={{ borderRadius: 3 }}\r\n              />\r\n            </Stack>\r\n          </Box>\r\n        </Stack>\r\n\r\n        {/* Right side - Action buttons */}\r\n        <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n          {/* More options button - matches 60x36 */}\r\n          <Skeleton variant=\"rounded\" width={60} height={36} />\r\n\r\n          {/* QBO connect/disconnect button - matches 220x36 */}\r\n          <Skeleton variant=\"rounded\" width={220} height={36} />\r\n\r\n          {/* Edit button - matches 180x36 */}\r\n          <Skeleton variant=\"rounded\" width={180} height={36} />\r\n        </Stack>\r\n      </Stack>\r\n\r\n      {/* Company Details Grid - matches pt: 2, borderTop */}\r\n      <Box sx={{ pt: 2, borderTop: 1, borderColor: \"divider\" }}>\r\n        <Grid container spacing={2}>\r\n          {/* Location - matches Grid size={{ xs: 6, lg: 3 }} */}\r\n          <Grid size={{ xs: 6, lg: 3 }}>\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width={60}\r\n              height={14}\r\n              sx={{ mb: 0.5, fontSize: \"0.75rem\" }}\r\n            />\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width=\"100%\"\r\n              height={18}\r\n              sx={{ fontSize: \"0.875rem\" }}\r\n            />\r\n          </Grid>\r\n\r\n          {/* Fiscal Year End */}\r\n          <Grid size={{ xs: 6, lg: 3 }}>\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width={90}\r\n              height={14}\r\n              sx={{ mb: 0.5, fontSize: \"0.75rem\" }}\r\n            />\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width=\"100%\"\r\n              height={18}\r\n              sx={{ fontSize: \"0.875rem\" }}\r\n            />\r\n          </Grid>\r\n\r\n          {/* NAICS */}\r\n          <Grid size={{ xs: 6, lg: 3 }}>\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width={40}\r\n              height={14}\r\n              sx={{ mb: 0.5, fontSize: \"0.75rem\" }}\r\n            />\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width=\"100%\"\r\n              height={18}\r\n              sx={{ fontSize: \"0.875rem\" }}\r\n            />\r\n          </Grid>\r\n\r\n          {/* Market */}\r\n          <Grid size={{ xs: 6, lg: 3 }}>\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width={50}\r\n              height={14}\r\n              sx={{ mb: 0.5, fontSize: \"0.75rem\" }}\r\n            />\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width=\"100%\"\r\n              height={18}\r\n              sx={{ fontSize: \"0.875rem\" }}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n      </Box>\r\n\r\n      {/* Critical Company Info - matches mt: 2, pt: 2, borderTop */}\r\n      <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: \"divider\" }}>\r\n        <Skeleton\r\n          variant=\"text\"\r\n          width={130}\r\n          height={14}\r\n          sx={{ mb: 0.5, fontSize: \"0.75rem\" }}\r\n        />\r\n        <Skeleton\r\n          variant=\"text\"\r\n          width=\"85%\"\r\n          height={18}\r\n          sx={{ fontSize: \"0.875rem\" }}\r\n        />\r\n        <Skeleton\r\n          variant=\"text\"\r\n          width=\"60%\"\r\n          height={18}\r\n          sx={{ fontSize: \"0.875rem\", mt: 0.5 }}\r\n        />\r\n      </Box>\r\n    </Card>\r\n  );\r\n\r\n  const TabsSkeleton = () => (\r\n    <Box sx={{ px: 3, py: 2 }}>\r\n      {/* Matches Stack direction=\"row\" spacing={4} borderBottom */}\r\n      <Stack\r\n        direction=\"row\"\r\n        spacing={4}\r\n        sx={{ borderBottom: 1, borderColor: \"divider\" }}\r\n      >\r\n        {/* Company info tab - matches fontSize: 14, fontWeight: 600, pb: 1 */}\r\n        <Skeleton\r\n          variant=\"text\"\r\n          width={100}\r\n          height={20}\r\n          sx={{\r\n            pb: 1,\r\n            fontSize: 14,\r\n          }}\r\n        />\r\n        {/* Reports tab */}\r\n        <Skeleton\r\n          variant=\"text\"\r\n          width={70}\r\n          height={20}\r\n          sx={{\r\n            pb: 1,\r\n            fontSize: 14,\r\n          }}\r\n        />\r\n        {/* Report Summary tab */}\r\n        <Skeleton\r\n          variant=\"text\"\r\n          width={110}\r\n          height={20}\r\n          sx={{\r\n            pb: 1,\r\n            fontSize: 14,\r\n          }}\r\n        />\r\n      </Stack>\r\n    </Box>\r\n  );\r\n\r\n  const UploadableSectionSkeleton = () => (\r\n    <Grid container spacing={1.5}>\r\n      {[...Array(6)].map((_, i) => (\r\n        <Grid size={{ xs: 12, sm: 6, lg: 4 }} key={i}>\r\n          <Card\r\n            sx={{\r\n              p: 1.5,\r\n              border: \"1px solid #e5e7eb\",\r\n              borderRadius: 1,\r\n              boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\r\n              backgroundColor: \"#fff\",\r\n            }}\r\n          >\r\n            {/* Header with title and buttons */}\r\n            <Stack\r\n              direction=\"row\"\r\n              justifyContent=\"space-between\"\r\n              alignItems=\"flex-start\"\r\n              sx={{ mb: 1 }}\r\n            >\r\n              {/* Section title */}\r\n              <Skeleton\r\n                variant=\"text\"\r\n                width={140}\r\n                height={18}\r\n                sx={{ fontSize: \"0.875rem\", fontWeight: 600 }}\r\n              />\r\n\r\n              {/* Buttons stack */}\r\n              <Stack direction=\"row\" spacing={0.5}>\r\n                <Skeleton variant=\"rounded\" width={50} height={20} />\r\n              </Stack>\r\n            </Stack>\r\n\r\n            {/* Description lines */}\r\n\r\n            <Skeleton\r\n              variant=\"text\"\r\n              width=\"75%\"\r\n              height={14}\r\n              sx={{ fontSize: \"0.75rem\", marginBottom: \"20px\" }}\r\n            />\r\n\r\n            {/* Status or additional info */}\r\n            <Stack direction=\"row\" spacing={0.5}>\r\n              <Skeleton variant=\"rounded\" width={50} height={24} />\r\n              <Skeleton variant=\"rounded\" width={50} height={24} />\r\n            </Stack>\r\n          </Card>\r\n        </Grid>\r\n      ))}\r\n    </Grid>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Header */}\r\n      <Box sx={{ my: 2 }} />\r\n      <Breadcrumb companyName={company?.name} />\r\n\r\n      <Box className=\"px-4\">\r\n        {/* Main Content */}\r\n        <Box sx={{ px: 2 }}>\r\n          {/* Tabs */}\r\n          <Box sx={{ px: 3, py: 2 }}>\r\n            {initialLoading ? (\r\n              <TabsSkeleton />\r\n            ) : (\r\n              <Stack\r\n                direction=\"row\"\r\n                spacing={4}\r\n                sx={{ borderBottom: 1, borderColor: \"divider\" }}\r\n              >\r\n                <Button\r\n                  onClick={() => setActiveTab(\"companyInfo\")}\r\n                  sx={{\r\n                    pb: 1,\r\n                    fontSize: 14,\r\n                    fontWeight: 600,\r\n                    textTransform: \"none\",\r\n                    color:\r\n                      activeTab === \"companyInfo\"\r\n                        ? \"primary.main\"\r\n                        : \"text.secondary\",\r\n                    borderBottom: 2,\r\n                    borderColor:\r\n                      activeTab === \"companyInfo\"\r\n                        ? \"primary.main\"\r\n                        : \"transparent\",\r\n                    borderRadius: 0,\r\n                    \"&:hover\": {\r\n                      backgroundColor: \"transparent\",\r\n                      color:\r\n                        activeTab === \"companyInfo\"\r\n                          ? \"primary.main\"\r\n                          : \"text.primary\",\r\n                    },\r\n                  }}\r\n                >\r\n                  Company info\r\n                </Button>\r\n                <Button\r\n                  onClick={() => setActiveTab(\"reports\")}\r\n                  sx={{\r\n                    pb: 1,\r\n                    fontSize: 14,\r\n                    fontWeight: 600,\r\n                    textTransform: \"none\",\r\n                    color:\r\n                      activeTab === \"reports\"\r\n                        ? \"primary.main\"\r\n                        : \"text.secondary\",\r\n                    borderBottom: 2,\r\n                    borderColor:\r\n                      activeTab === \"reports\" ? \"primary.main\" : \"transparent\",\r\n                    borderRadius: 0,\r\n                    \"&:hover\": {\r\n                      backgroundColor: \"transparent\",\r\n                      color:\r\n                        activeTab === \"reports\"\r\n                          ? \"primary.main\"\r\n                          : \"text.primary\",\r\n                    },\r\n                  }}\r\n                >\r\n                  Reports\r\n                </Button>\r\n                <Button\r\n                  onClick={() => setActiveTab(\"report-settings\")}\r\n                  sx={{\r\n                    pb: 1,\r\n                    fontSize: 14,\r\n                    fontWeight: 600,\r\n                    textTransform: \"none\",\r\n                    color:\r\n                      activeTab === \"report-settings\"\r\n                        ? \"primary.main\"\r\n                        : \"text.secondary\",\r\n                    borderBottom: 2,\r\n                    borderColor:\r\n                      activeTab === \"report-settings\"\r\n                        ? \"primary.main\"\r\n                        : \"transparent\",\r\n                    borderRadius: 0,\r\n                    \"&:hover\": {\r\n                      backgroundColor: \"transparent\",\r\n                      color:\r\n                        activeTab === \"report-settings\"\r\n                          ? \"primary.main\"\r\n                          : \"text.primary\",\r\n                    },\r\n                  }}\r\n                >\r\n                  Report Settings\r\n                </Button>\r\n              </Stack>\r\n            )}\r\n          </Box>\r\n\r\n          {/* Conditional Rendering of Content based on Active Tab */}\r\n          <Box sx={{ p: 1.5 }}>\r\n            {initialLoading ? (\r\n              <Box>\r\n                <CompanyHeaderSkeleton />\r\n                <UploadableSectionSkeleton />\r\n              </Box>\r\n            ) : activeTab === \"companyInfo\" ? (\r\n              <Box>\r\n                {/* Company Info Card */}\r\n                <Card\r\n                  sx={{\r\n                    p: 3,\r\n                    mb: 2,\r\n                    border: \"1px solid #e5e7eb\",\r\n                    borderRadius: 1,\r\n                    boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\r\n                    \"&:hover\": {\r\n                      boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\",\r\n                    },\r\n                    transition: \"all 0.2s ease-in-out\",\r\n                    backgroundColor: \"#fff\",\r\n                  }}\r\n                >\r\n                  {/* Header Section */}\r\n                  <Stack\r\n                    direction=\"row\"\r\n                    justifyContent=\"space-between\"\r\n                    alignItems=\"flex-start\"\r\n                    sx={{ mb: 3 }}\r\n                  >\r\n                    <Stack direction=\"row\" spacing={3} alignItems=\"center\">\r\n                      {company?.logo ? (\r\n                        <Avatar\r\n                          src={company.logo}\r\n                          alt=\"Company Logo\"\r\n                          sx={{\r\n                            height: 64,\r\n                            width: 64,\r\n                            borderRadius: 2,\r\n                          }}\r\n                        />\r\n                      ) : (\r\n                        <Avatar\r\n                          sx={{\r\n                            width: 64,\r\n                            height: 64,\r\n                            background: \"#3b82f6\",\r\n                            borderRadius: 2,\r\n                            fontSize: 20,\r\n                            fontWeight: \"bold\",\r\n                            color: \"#fff\",\r\n                          }}\r\n                        >\r\n                          {getAvatarInitials(company?.name)}\r\n                        </Avatar>\r\n                      )}\r\n\r\n                      <Box>\r\n                        <Typography\r\n                          variant=\"h5\"\r\n                          sx={{\r\n                            fontWeight: 600,\r\n                            mb: 0.5,\r\n                            fontSize: \"1.5rem\",\r\n                            color: \"#111827\",\r\n                          }}\r\n                        >\r\n                          {company?.name}\r\n                        </Typography>\r\n\r\n                        {/* QBO Company Name */}\r\n\r\n                        <Typography\r\n                          variant=\"body1\"\r\n                          sx={{\r\n                            color: \"#6b7280\",\r\n                            mb: 1,\r\n                            fontSize: \"0.95rem\",\r\n                          }}\r\n                        >\r\n                          {company?.qboCompanyName ? (\r\n                            <>\r\n                              QuickBooks Company:&nbsp;\r\n                              <span\r\n                                style={{\r\n                                  color: \"#1976d2\",\r\n                                  fontWeight: \"500\",\r\n                                  textDecoration: \"none\",\r\n                                }}\r\n                              >\r\n                                {company?.qboCompanyName}\r\n                              </span>\r\n                            </>\r\n                          ) : null}\r\n                        </Typography>\r\n\r\n                        {/* Connection Status */}\r\n                        <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n                          <Stack\r\n                            direction=\"row\"\r\n                            spacing={1}\r\n                            alignItems=\"center\"\r\n                          >\r\n                            {qboConnected ? (\r\n                              <Chip\r\n                                icon={\r\n                                  <CircleIcon\r\n                                    sx={{\r\n                                      fontSize: \"8px !important\",\r\n                                      color: \"#10b981 !important\",\r\n                                    }}\r\n                                  />\r\n                                }\r\n                                label=\"Connected\"\r\n                                size=\"small\"\r\n                                sx={{\r\n                                  backgroundColor: \"#d1fae5\",\r\n                                  color: \"#065f46\",\r\n                                  border: \"none\",\r\n                                  height: 24,\r\n                                  fontSize: 12,\r\n                                  fontWeight: 500,\r\n                                  borderRadius: \"12px\",\r\n                                  \"& .MuiChip-icon\": {\r\n                                    color: \"#10b981\",\r\n                                    marginLeft: \"6px\",\r\n                                  },\r\n                                  \"& .MuiChip-label\": {\r\n                                    paddingLeft: \"9px\",\r\n                                    paddingRight: \"8px\",\r\n                                  },\r\n                                }}\r\n                              />\r\n                            ) : (\r\n                              <Chip\r\n                                icon={\r\n                                  <CircleIcon\r\n                                    sx={{\r\n                                      fontSize: \"8px !important\",\r\n                                      color: \"#ef4444 !important\",\r\n                                    }}\r\n                                  />\r\n                                }\r\n                                label=\"Not Connected\"\r\n                                size=\"small\"\r\n                                sx={{\r\n                                  backgroundColor: \"#fee2e2\",\r\n                                  color: \"#991b1b\",\r\n                                  border: \"none\",\r\n                                  height: 24,\r\n                                  fontSize: 12,\r\n                                  fontWeight: 500,\r\n                                  borderRadius: \"12px\",\r\n                                  \"& .MuiChip-icon\": {\r\n                                    color: \"#ef4444\",\r\n                                    marginLeft: \"6px\",\r\n                                  },\r\n                                  \"& .MuiChip-label\": {\r\n                                    paddingLeft: \"9px\",\r\n                                    paddingRight: \"8px\",\r\n                                  },\r\n                                }}\r\n                              />\r\n                            )}\r\n                          </Stack>\r\n\r\n                          {/* {qboStatus?.qboRealmID && (\r\n    <Typography variant=\"body2\" sx={{ color: \"#6b7280\", fontSize: \"0.875rem\" }}>\r\n      (ID: {qboStatus.qboRealmID})\r\n    </Typography>\r\n  )} */}\r\n                        </Stack>\r\n                      </Box>\r\n                    </Stack>\r\n\r\n                    {/* Action Buttons */}\r\n                    <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n                      <Button\r\n                        variant=\"contained\"\r\n                        size=\"small\"\r\n                        sx={{\r\n                          minWidth: 60,\r\n                          height: 36,\r\n                          padding: 0,\r\n                          backgroundColor: \"#fff\",\r\n                          color: \"#374151\",\r\n                          border: \"1px solid #e5e7eb\",\r\n                          boxShadow: \"none\",\r\n                          \"&:hover\": {\r\n                            backgroundColor: \"#f3f4f6\",\r\n                            boxShadow: \"none\",\r\n                          },\r\n                        }}\r\n                      >\r\n                        <MoreHorizIcon />\r\n                      </Button>\r\n\r\n                      {qboConnected ? (\r\n                        <Button\r\n                          variant=\"contained\"\r\n                          size=\"small\"\r\n                          sx={{\r\n                            fontSize: \"14px\",\r\n                            fontWeight: 540,\r\n                            px: 12,\r\n                            width: 280,\r\n                            height: 36,\r\n                            textTransform: \"none\",\r\n                            borderRadius: \"2\",\r\n                            backgroundColor: \"#dc2626\",\r\n                            color: \"#fff\",\r\n                            whiteSpace: \"nowrap\",\r\n                            overflow: \"hidden\",\r\n                            textOverflow: \"ellipsis\",\r\n                            \"&:hover\": {\r\n                              backgroundColor: \"#b91c1c\",\r\n                            },\r\n                            \"&:disabled\": {\r\n                              backgroundColor: \"#e5e7eb\",\r\n                              color: \"#9ca3af\",\r\n                            },\r\n                          }}\r\n                          onClick={handleQBODisconnect}\r\n                          disabled={qboLoading || initialLoading}\r\n                        >\r\n                          {qboLoading\r\n                            ? \"Disconnecting...\"\r\n                            : \"DISCONNECT FROM QUICKBOOKS\"}\r\n                        </Button>\r\n                      ) : (\r\n                        <Box\r\n                          sx={{\r\n                            position: \"relative\",\r\n                            minWidth: 220,\r\n                            height: 36,\r\n                          }}\r\n                        >\r\n                          <Box\r\n                            component=\"img\"\r\n                            src={qboButton}\r\n                            alt=\"Connect to QuickBooks\"\r\n                            sx={{\r\n                              cursor: \"pointer\",\r\n                              height: 36,\r\n                              width: \"100%\",\r\n                              transition: \"opacity 0.2s ease-in-out\",\r\n                              opacity: qboLoading || initialLoading ? 0.5 : 1,\r\n                              pointerEvents:\r\n                                qboLoading || initialLoading ? \"none\" : \"auto\",\r\n                              \"&:hover\": { opacity: 0 },\r\n                            }}\r\n                            onClick={\r\n                              !qboLoading && !initialLoading\r\n                                ? handleQBOConnect\r\n                                : undefined\r\n                            }\r\n                          />\r\n                          <Box\r\n                            component=\"img\"\r\n                            src={qboButtonHover}\r\n                            alt=\"Connect to QuickBooks\"\r\n                            sx={{\r\n                              position: \"absolute\",\r\n                              top: 0,\r\n                              left: 0,\r\n                              height: 36,\r\n                              width: \"100%\",\r\n                              opacity: 0,\r\n                              transition: \"opacity 0.2s ease-in-out\",\r\n                              \"&:hover\": { opacity: 1 },\r\n                              pointerEvents:\r\n                                qboLoading || initialLoading ? \"none\" : \"auto\",\r\n                            }}\r\n                            onClick={\r\n                              !qboLoading && !initialLoading\r\n                                ? handleQBOConnect\r\n                                : undefined\r\n                            }\r\n                          />\r\n                          {(qboLoading || initialLoading) && (\r\n                            <Box\r\n                              sx={{\r\n                                position: \"absolute\",\r\n                                top: 0,\r\n                                left: 0,\r\n                                right: 0,\r\n                                bottom: 0,\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                justifyContent: \"center\",\r\n                                backgroundColor: \"rgba(255, 255, 255, 0.8)\",\r\n                                borderRadius: 1,\r\n                              }}\r\n                            >\r\n                              <CircularProgress size={18} />\r\n                            </Box>\r\n                          )}\r\n                        </Box>\r\n                      )}\r\n\r\n                      <Button\r\n                        variant=\"outlined\"\r\n                        size=\"small\"\r\n                        sx={{\r\n                          fontSize: 14,\r\n                          px: 3,\r\n                          width: 200,\r\n                          height: 36,\r\n                          textTransform: \"none\",\r\n                          borderColor: \"#d1d5db\",\r\n                          color: \"#374151\",\r\n                          fontWeight: 500,\r\n                          borderRadius: \"2\",\r\n                          whiteSpace: \"nowrap\",\r\n                          overflow: \"hidden\",\r\n                          textOverflow: \"ellipsis\",\r\n                          \"&:hover\": {\r\n                            backgroundColor: \"#f9fafb\",\r\n                            borderColor: \"#9ca3af\",\r\n                          },\r\n                          \"&:disabled\": {\r\n                            backgroundColor: \"#f3f4f6\",\r\n                            color: \"#9ca3af\",\r\n                            borderColor: \"#e5e7eb\",\r\n                          },\r\n                        }}\r\n                        onClick={() => handleEdit()}\r\n                        disabled={qboLoading || initialLoading}\r\n                        fullWidth={true}\r\n                      >\r\n                        EDIT COMPANY INFO\r\n                      </Button>\r\n                    </Stack>\r\n                  </Stack>\r\n\r\n                  {/* Company Details Grid */}\r\n\r\n                  <Box sx={{ pt: 2, borderTop: 1, borderColor: \"divider\" }}>\r\n                    <Grid container spacing={2}>\r\n                      {/* Location */}\r\n                      {company?.country && (\r\n                        <Grid size={{ xs: 6, lg: 3 }}>\r\n                          <Typography\r\n                            variant=\"caption\"\r\n                            sx={{\r\n                              color: \"text.secondary\",\r\n                              fontWeight: 500,\r\n                              display: \"block\",\r\n                              mb: 0.5,\r\n                              textTransform: \"none\",\r\n                            }}\r\n                          >\r\n                            Location\r\n                          </Typography>\r\n                          <Typography\r\n                            variant=\"body2\"\r\n                            sx={{ fontWeight: 600, color: \"text.primary\" }}\r\n                          >\r\n                            {[company?.country, company?.state, company?.city]\r\n                              .filter(Boolean)\r\n                              .join(\", \")}\r\n                          </Typography>\r\n                        </Grid>\r\n                      )}\r\n\r\n                      {/* Fiscal Year End */}\r\n                      <Grid size={{ xs: 6, lg: 3 }}>\r\n                        <Typography\r\n                          variant=\"caption\"\r\n                          sx={{\r\n                            color: \"text.secondary\",\r\n                            fontWeight: 500,\r\n                            display: \"block\",\r\n                            mb: 0.5,\r\n                            textTransform: \"none\",\r\n                          }}\r\n                        >\r\n                          Fiscal year end\r\n                        </Typography>\r\n                        <Typography\r\n                          variant=\"body2\"\r\n                          sx={{ fontWeight: 600, color: \"text.primary\" }}\r\n                        >\r\n                          {formatDate(company?.fiscal_year_end)}\r\n                        </Typography>\r\n                      </Grid>\r\n\r\n                      <Grid size={{ xs: 6, lg: 3 }}>\r\n                        <Typography\r\n                          variant=\"caption\"\r\n                          sx={{\r\n                            color: \"text.secondary\",\r\n                            fontWeight: 500,\r\n                            display: \"block\",\r\n                            mb: 0.5,\r\n                            textTransform: \"none\",\r\n                          }}\r\n                        >\r\n                          Naics\r\n                        </Typography>\r\n                        <Typography\r\n                          variant=\"body2\"\r\n                          sx={{ fontWeight: 600, color: \"text.primary\" }}\r\n                        >\r\n                          {company?.naics}\r\n                        </Typography>\r\n                      </Grid>\r\n\r\n                      {/* Market */}\r\n                      {company?.market && (\r\n                        <Grid size={{ xs: 6, lg: 3 }}>\r\n                          <Typography\r\n                            variant=\"caption\"\r\n                            sx={{\r\n                              color: \"text.secondary\",\r\n                              fontWeight: 500,\r\n                              display: \"block\",\r\n                              mb: 0.5,\r\n                              textTransform: \"none\",\r\n                            }}\r\n                          >\r\n                            Market\r\n                          </Typography>\r\n                          <Typography\r\n                            variant=\"body2\"\r\n                            sx={{ fontWeight: 600, color: \"text.primary\" }}\r\n                          >\r\n                            {company?.market}\r\n                          </Typography>\r\n                        </Grid>\r\n                      )}\r\n                    </Grid>\r\n                  </Box>\r\n                  {/* Critical Company Info */}\r\n                  {company?.description && (\r\n                    <Box\r\n                      sx={{\r\n                        mt: 2,\r\n                        pt: 2,\r\n                        borderTop: 1,\r\n                        borderColor: \"divider\",\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"caption\"\r\n                        sx={{\r\n                          color: \"text.secondary\",\r\n                          fontWeight: 500,\r\n                          display: \"block\",\r\n                          mb: 0.5,\r\n                          textTransform: \"none\",\r\n                        }}\r\n                      >\r\n                        Critical company info\r\n                      </Typography>\r\n                      {company.description.length > 60 ? (\r\n                        <Box component=\"details\" sx={{ cursor: \"pointer\" }}>\r\n                          <Typography\r\n                            component=\"summary\"\r\n                            variant=\"body2\"\r\n                            sx={{\r\n                              fontWeight: 600,\r\n                              color: \"text.primary\",\r\n                              \"&:hover\": { color: \"primary.main\" },\r\n                              transition: \"color 0.2s ease-in-out\",\r\n                            }}\r\n                          >\r\n                            {company.description.slice(0, 50)}...\r\n                          </Typography>\r\n                          <Typography\r\n                            variant=\"body2\"\r\n                            sx={{ mt: 1, color: \"text.secondary\" }}\r\n                          >\r\n                            {company.description}\r\n                          </Typography>\r\n                        </Box>\r\n                      ) : (\r\n                        <Typography\r\n                          variant=\"body2\"\r\n                          sx={{ fontWeight: 600, color: \"text.primary\" }}\r\n                        >\r\n                          {company?.description}\r\n                        </Typography>\r\n                      )}\r\n                    </Box>\r\n                  )}\r\n                </Card>\r\n\r\n                {/* Enhanced QuickBooks Connection Component */}\r\n                <QuickBooksConnection\r\n                  qboConnected={qboConnected}\r\n                  qboLoading={qboLoading}\r\n                  qboStatus={qboStatus}\r\n                  company={company}\r\n                  onConnect={handleQBOConnect}\r\n                  onDisconnect={handleQBODisconnect}\r\n                  onRefreshStatus={fetchQBOStatus}\r\n                />\r\n\r\n                {/* Uploadable Sections Component */}\r\n                <UploadableSection\r\n                  companyFiles={company?.companyFiles}\r\n                  qboConnected={qboConnected}\r\n                  qboLoading={qboLoading}\r\n                  initialLoading={initialLoading}\r\n                  onUpload={handleOpenModal}\r\n                  onSync={handleSync}\r\n                  syncLoading={syncLoading}\r\n                  companyData={company}\r\n                />\r\n              </Box>\r\n            ) : activeTab === \"reports\" ? (\r\n              <Reports\r\n                setActiveTab={setActiveTab}\r\n                companyId={id}\r\n                companyFiles={company?.companyFiles}\r\n              />\r\n            ) : activeTab === \"report-settings\" ? (\r\n              <ReportSettings companyId={id} />\r\n            ) : null}\r\n          </Box>\r\n\r\n          {/* Render Reports Tab Content */}\r\n          {/* {activeTab === \"reports\" && (\r\n            <Reports\r\n              setActiveTab={setActiveTab}\r\n              companyId={id}\r\n              companyFiles={company?.companyFiles}\r\n            />\r\n\r\n            \r\n          )} */}\r\n        </Box>\r\n      </Box>\r\n\r\n      {showModal && (\r\n        <CSVUploadModal\r\n          onClose={() => setShowModal(false)}\r\n          onUpload={handleUpload}\r\n          companyId={id}\r\n          type={fileTypeSection}\r\n          onSuccess={fetchData}\r\n        />\r\n      )}\r\n\r\n      <EditCompany\r\n        isOpen={isEditModalOpen}\r\n        onClose={() => setIsEditModalOpen(false)}\r\n        companyData={company}\r\n        onSave={handleSaveCompany}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\nexport default CompanyDetail;\r\n"], "mappings": ";;AAAA,OAAOA,UAAU,MAAM,4BAA4B;AACnD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SACEC,MAAM,EACNC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,gBAAgB,EAChBC,KAAK,IAAIC,IAAI,EACbC,QAAQ,EACRC,KAAK,EACLC,UAAU,QACL,eAAe;AACtB,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAC/D,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SACEC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,QACX,uBAAuB;AAC9B,OAAOC,UAAU,MAAM,wCAAwC;AAC/D,SAASC,SAAS,EAAEC,SAAS,QAAQ,wBAAwB;AAC7D,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,uBAAuB;AACrE,OAAOC,SAAS,MAAM,kDAAkD;AACxE,OAAOC,cAAc,MAAM,gDAAgD;AAC3E,OAAOC,cAAc,MAAM,YAAY;AACvC,OAAO,cAAc;AACrB,OAAOC,WAAW,MAAM,QAAQ;AAChC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,UAAU,GAAG;EACjBC,iBAAiB,EAAE,mBAAmB;EACtCC,aAAa,EAAE,eAAe;EAC9BC,eAAe,EAAE,iBAAiB;EAClCC,aAAa,EAAE,eAAe;EAC9BC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACZ,CAAC;AAED,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACvB,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,MAAM,GAAG1C,SAAS,CAAC,CAAC;EAC1B,MAAM2C,EAAE,GAAG,CAAAF,QAAQ,aAARA,QAAQ,wBAAAH,eAAA,GAARG,QAAQ,CAAEG,KAAK,cAAAN,eAAA,uBAAfA,eAAA,CAAiBK,EAAE,MAAID,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,EAAE;EAC5C,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CACxC,CAAA4C,QAAQ,aAARA,QAAQ,wBAAAF,gBAAA,GAARE,QAAQ,CAAEG,KAAK,cAAAL,gBAAA,uBAAfA,gBAAA,CAAiBM,SAAS,KAAI,aAChC,CAAC;EACD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC;IAC7C,CAACgC,UAAU,CAACC,iBAAiB,GAAG,KAAK;IACrC,CAACD,UAAU,CAACE,aAAa,GAAG,KAAK;IACjC,CAACF,UAAU,CAACG,eAAe,GAAG,KAAK;IACnC,CAACH,UAAU,CAACI,aAAa,GAAG,KAAK;IACjC,CAACJ,UAAU,CAACK,QAAQ,GAAG,KAAK;IAC5B,CAACL,UAAU,CAACM,QAAQ,GAAG;EACzB,CAAC,CAAC;EAEF,MAAMgC,eAAe,GAAIC,IAAI,IAAK;IAChCd,YAAY,CAAC,IAAI,CAAC;IAClBF,kBAAkB,CAACgB,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAItB,YAAY,EAAE,CAClB;EACF,CAAC;EACD,SAASuB,qBAAqBA,CAACC,GAAG,EAAE;IAClC,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;IACrB,OAAOA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;EACnD;EAEA,MAAMC,UAAU,GAAG,MAAOP,IAAI,IAAK;IACjCF,cAAc,CAAEU,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACR,IAAI,GAAG;IAAK,CAAC,CAAC,CAAC;IAErD,IAAI;MAAA,IAAAS,cAAA;MACF,MAAMC,OAAO,GAAG;QACdC,MAAM,EAAE9B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8B,MAAM;QACvBC,SAAS,EAAE/B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEN,EAAE;QACtBsC,cAAc,EAAE;MAClB,CAAC;MAED,MAAMC,OAAO,GAAG;QACd,CAACrD,UAAU,CAACC,iBAAiB,GAAG;UAC9BqD,EAAE,EAAE7E,YAAY;UAChB8E,OAAO,EAAE;QACX,CAAC;QACD,CAACvD,UAAU,CAACE,aAAa,GAAG;UAC1BoD,EAAE,EAAExE,gBAAgB;UACpByE,OAAO,EAAE;QACX,CAAC;QACD,CAACvD,UAAU,CAACG,eAAe,GAAG;UAC5BmD,EAAE,EAAEzE,cAAc;UAClB0E,OAAO,EAAE;QACX,CAAC;QACD,CAACvD,UAAU,CAACI,aAAa,GAAG;UAC1BkD,EAAE,EAAE1E,gBAAgB;UACpB2E,OAAO,EAAE;QACX,CAAC;QACD,CAACvD,UAAU,CAACK,QAAQ,GAAG;UACrBiD,EAAE,EAAE5E,WAAW;UACf6E,OAAO,EAAE;QACX,CAAC;QACD,CAACvD,UAAU,CAACM,QAAQ,GAAG;UACrBgD,EAAE,EAAE3E,WAAW;UACf4E,OAAO,EAAE;QACX;MACF,CAAC;MAED,MAAMC,QAAQ,GAAGH,OAAO,CAACd,IAAI,CAAC;MAC9B,IAAI,CAACiB,QAAQ,EAAE,MAAM,IAAIC,KAAK,CAAC,sBAAsBlB,IAAI,EAAE,CAAC;MAE5D,MAAMmB,QAAQ,GAAG,MAAMF,QAAQ,CAACF,EAAE,CAACL,OAAO,CAAC;MAE3C,KAAAD,cAAA,GAAIU,QAAQ,CAACC,IAAI,cAAAX,cAAA,eAAbA,cAAA,CAAeY,OAAO,EAAE;QAC1BxF,IAAI,CAACyF,IAAI,CAAC;UACRC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAEvB,qBAAqB,CACzBiB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACJ,OAAO,IACxBG,QAAQ,CAACC,IAAI,CAACJ,OAAO,IACrBC,QAAQ,CAACD,OACb,CAAC;UACDU,kBAAkB,EAAE;QACtB,CAAC,CAAC;QACF,MAAMC,SAAS,CAAC,CAAC;MACnB,CAAC,MAAM;QAAA,IAAAC,eAAA;QACL,MAAM,IAAIV,KAAK,CAAC,EAAAU,eAAA,GAAAT,QAAQ,CAACC,IAAI,cAAAQ,eAAA,uBAAbA,eAAA,CAAeZ,OAAO,KAAI,aAAa,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MAAA,IAAAC,oBAAA;MACdjG,IAAI,CAACyF,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,EAAAK,oBAAA,GAAAD,KAAK,CAACV,QAAQ,CAACC,IAAI,cAAAU,oBAAA,uBAAnBA,oBAAA,CAAqBd,OAAO,KAAI,gCAAgC;QACtEU,kBAAkB,EAAE;MACtB,CAAC,CAAC;IACJ,CAAC,SAAS;MACR5B,cAAc,CAAEU,IAAI,KAAM;QAAE,GAAGA,IAAI;QAAE,CAACR,IAAI,GAAG;MAAM,CAAC,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAM2B,SAAS,GAAGpG,WAAW,CAAC,YAAY;IACxC,IAAI;MACF,MAAM4F,QAAQ,GAAG,MAAMrF,UAAU,CAACyC,EAAE,CAAC;MAErC,IAAI4C,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzBvC,UAAU,CAACqC,QAAQ,CAACC,IAAI,CAACvC,OAAO,CAAC;QACjCS,eAAe,CACb6B,QAAQ,CAACC,IAAI,CAACvC,OAAO,CAACkD,mBAAmB,KAAK,WAChD,CAAC;QACDrC,YAAY,CAACyB,QAAQ,CAACC,IAAI,CAACvC,OAAO,CAAC;MACrC;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MAAA,IAAAG,eAAA,EAAAC,qBAAA;MACdC,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDhG,IAAI,CAACyF,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EACF,EAAAO,eAAA,GAAAH,KAAK,CAACV,QAAQ,cAAAa,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBjB,OAAO,KAC7B,oDAAoD;QACtDU,kBAAkB,EAAE;MACtB,CAAC,CAAC;IACJ,CAAC,SAAS,CACV;EACF,CAAC,EAAE,CAACnD,EAAE,CAAC,CAAC;EAER,MAAM4D,cAAc,GAAG5G,WAAW,CAAC,YAAY;IAC7C,IAAI;MACFiE,aAAa,CAAC,IAAI,CAAC;MACnB,MAAM2B,QAAQ,GAAG,MAAMlF,YAAY,CAACsC,EAAE,CAAC;MACvC,IAAI4C,QAAQ,CAACC,IAAI,EAAE;QACjB9B,eAAe,CAAC6B,QAAQ,CAACC,IAAI,CAACW,mBAAmB,KAAK,WAAW,CAAC;QAClErC,YAAY,CAACyB,QAAQ,CAACC,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAvC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,SAAS;MACRE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACjB,EAAE,CAAC,CAAC;EAER,MAAM6D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACF9C,aAAa,CAAC,IAAI,CAAC;MAEnB,MAAM2B,QAAQ,GAAG,MAAMpF,UAAU,CAACwC,EAAE,CAAC;MAErC,IAAI4C,QAAQ,CAACC,IAAI,CAACC,OAAO,KAAAgB,eAAA,GAAIlB,QAAQ,CAACC,IAAI,cAAAiB,eAAA,gBAAAC,oBAAA,GAAbD,eAAA,CAAejB,IAAI,cAAAkB,oBAAA,eAAnBA,oBAAA,CAAqBC,GAAG,EAAE;QAAA,IAAAC,eAAA,EAAAC,oBAAA;QACrDC,MAAM,CAACrE,QAAQ,CAACsE,IAAI,IAAAH,eAAA,GAAGrB,QAAQ,CAACC,IAAI,cAAAoB,eAAA,wBAAAC,oBAAA,GAAbD,eAAA,CAAepB,IAAI,cAAAqB,oBAAA,uBAAnBA,oBAAA,CAAqBF,GAAG;MACjD,CAAC,MAAM;QACL,MAAM,IAAIrB,KAAK,CAAC,iCAAiC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDrC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMoD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMhH,IAAI,CAACyF,IAAI,CAAC;QAC7BE,KAAK,EAAE,wCAAwC;QAC/CC,IAAI,EAAE,wEAAwE;QAC9EF,IAAI,EAAE,SAAS;QACfuB,gBAAgB,EAAE,IAAI;QACtBpB,kBAAkB,EAAE,SAAS;QAAE;QAC/BqB,iBAAiB,EAAE,aAAa;QAAE;QAClCC,iBAAiB,EAAE,YAAY;QAC/BC,gBAAgB,EAAE,QAAQ;QAC1BC,KAAK,EAAE,GAAG;QACVC,WAAW,EAAE;UACXC,YAAY,EAAE;QAChB,CAAC;QACDC,OAAO,EAAEA,CAAA,KAAM;UACb,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;UAC7CF,KAAK,CAACG,WAAW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;UACKF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;QAClC;MACF,CAAC,CAAC;MAEF,IAAIT,MAAM,CAACe,WAAW,EAAE;QACtBpE,aAAa,CAAC,IAAI,CAAC;QACnB,MAAMxD,aAAa,CAACuC,EAAE,CAAC;QACvB,MAAM4D,cAAc,CAAC,CAAC;QAEtBtG,IAAI,CAACyF,IAAI,CAAC;UACRC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,2BAA2B;UAClCC,IAAI,EAAE,8EAA8E;UACpFC,kBAAkB,EAAE,SAAS;UAC7BsB,iBAAiB,EAAE;QACrB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOnB,KAAK,EAAE;MAAA,IAAAgC,gBAAA,EAAAC,qBAAA;MACd5B,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhDhG,IAAI,CAACyF,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EACF,EAAAoC,gBAAA,GAAAhC,KAAK,CAACV,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzC,IAAI,cAAA0C,qBAAA,uBAApBA,qBAAA,CAAsB9C,OAAO,KAC7B,mFAAmF;QACrFU,kBAAkB,EAAE,SAAS;QAC7BsB,iBAAiB,EAAE,WAAW;QAC9BF,gBAAgB,EAAE,IAAI;QACtBG,gBAAgB,EAAE,QAAQ;QAC1BF,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRvD,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEAhE,SAAS,CAAC,MAAM;IAAA,IAAAuI,gBAAA;IACd,IAAI1F,QAAQ,aAARA,QAAQ,gBAAA0F,gBAAA,GAAR1F,QAAQ,CAAEG,KAAK,cAAAuF,gBAAA,eAAfA,gBAAA,CAAiBtF,SAAS,EAAE;MAC9BC,YAAY,CAACL,QAAQ,CAACG,KAAK,CAACC,SAAS,CAAC;IACxC;EACF,CAAC,EAAE,CAACJ,QAAQ,aAARA,QAAQ,wBAAAD,gBAAA,GAARC,QAAQ,CAAEG,KAAK,cAAAJ,gBAAA,uBAAfA,gBAAA,CAAiBK,SAAS,CAAC,CAAC;EAEhCjD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+C,EAAE,EAAE;IAET,MAAMyF,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3BpE,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAM+B,SAAS,CAAC,CAAC;MACjB/B,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC;IAEDoE,QAAQ,CAAC,CAAC;;IAEV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC,EAAE,CAACzF,EAAE,EAAEoD,SAAS,EAAEtD,QAAQ,CAAC4F,QAAQ,CAAC,CAAC;;EAEtC;EACA;EACA;;EAEA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI3E,UAAU,IAAII,cAAc,EAAE;IAElC,IAAId,OAAO,EAAE;MACXO,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM+E,iBAAiB,GAAIC,cAAc,IAAK;IAC5CtF,UAAU,CAACsF,cAAc,CAAC;IAC1BzC,SAAS,CAAC,CAAC;EACb,CAAC;EACD,MAAM0C,qBAAqB,GAAGA,CAAA,kBAC5B/G,OAAA,CAACxC,IAAI;IACHwJ,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACLC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,gCAAgC;MAC3CC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,gBAGFvH,OAAA,CAAClC,KAAK;MACJ0J,SAAS,EAAC,KAAK;MACfC,cAAc,EAAC,eAAe;MAC9BC,UAAU,EAAC,YAAY;MACvBV,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAK,QAAA,gBAGdvH,OAAA,CAAClC,KAAK;QAAC0J,SAAS,EAAC,KAAK;QAACG,OAAO,EAAE,CAAE;QAACD,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBAEpDvH,OAAA,CAACnC,QAAQ;UACP+J,OAAO,EAAC,SAAS;UACjBhC,KAAK,EAAE,EAAG;UACViC,MAAM,EAAE,EAAG;UACXb,EAAE,EAAE;YAAEI,YAAY,EAAE;UAAE;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEFjI,OAAA,CAAC1C,GAAG;UAAAiK,QAAA,gBAEFvH,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,GAAI;YACXiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEE,EAAE,EAAE,GAAG;cAAEgB,QAAQ,EAAE;YAAS;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eAGFjI,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,GAAI;YACXiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEE,EAAE,EAAE,CAAC;cAAEgB,QAAQ,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAGFjI,OAAA,CAAClC,KAAK;YAAC0J,SAAS,EAAC,KAAK;YAACG,OAAO,EAAE,CAAE;YAACD,UAAU,EAAC,QAAQ;YAAAH,QAAA,eACpDvH,OAAA,CAACnC,QAAQ;cACP+J,OAAO,EAAC,SAAS;cACjBhC,KAAK,EAAE,EAAG;cACViC,MAAM,EAAE,EAAG;cACXb,EAAE,EAAE;gBAAEI,YAAY,EAAE;cAAE;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGRjI,OAAA,CAAClC,KAAK;QAAC0J,SAAS,EAAC,KAAK;QAACG,OAAO,EAAE,CAAE;QAACD,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBAEpDvH,OAAA,CAACnC,QAAQ;UAAC+J,OAAO,EAAC,SAAS;UAAChC,KAAK,EAAE,EAAG;UAACiC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGrDjI,OAAA,CAACnC,QAAQ;UAAC+J,OAAO,EAAC,SAAS;UAAChC,KAAK,EAAE,GAAI;UAACiC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtDjI,OAAA,CAACnC,QAAQ;UAAC+J,OAAO,EAAC,SAAS;UAAChC,KAAK,EAAE,GAAI;UAACiC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGRjI,OAAA,CAAC1C,GAAG;MAAC0J,EAAE,EAAE;QAAEmB,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAU,CAAE;MAAAd,QAAA,eACvDvH,OAAA,CAACpC,IAAI;QAAC0K,SAAS;QAACX,OAAO,EAAE,CAAE;QAAAJ,QAAA,gBAEzBvH,OAAA,CAACpC,IAAI;UAAC2K,IAAI,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBAC3BvH,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,EAAG;YACViC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEE,EAAE,EAAE,GAAG;cAAEgB,QAAQ,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFjI,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAC,MAAM;YACZiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEkB,QAAQ,EAAE;YAAW;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGPjI,OAAA,CAACpC,IAAI;UAAC2K,IAAI,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBAC3BvH,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,EAAG;YACViC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEE,EAAE,EAAE,GAAG;cAAEgB,QAAQ,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFjI,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAC,MAAM;YACZiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEkB,QAAQ,EAAE;YAAW;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGPjI,OAAA,CAACpC,IAAI;UAAC2K,IAAI,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBAC3BvH,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,EAAG;YACViC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEE,EAAE,EAAE,GAAG;cAAEgB,QAAQ,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFjI,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAC,MAAM;YACZiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEkB,QAAQ,EAAE;YAAW;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGPjI,OAAA,CAACpC,IAAI;UAAC2K,IAAI,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBAC3BvH,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,EAAG;YACViC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEE,EAAE,EAAE,GAAG;cAAEgB,QAAQ,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFjI,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAC,MAAM;YACZiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEkB,QAAQ,EAAE;YAAW;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNjI,OAAA,CAAC1C,GAAG;MAAC0J,EAAE,EAAE;QAAE0B,EAAE,EAAE,CAAC;QAAEP,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,WAAW,EAAE;MAAU,CAAE;MAAAd,QAAA,gBAC9DvH,OAAA,CAACnC,QAAQ;QACP+J,OAAO,EAAC,MAAM;QACdhC,KAAK,EAAE,GAAI;QACXiC,MAAM,EAAE,EAAG;QACXb,EAAE,EAAE;UAAEE,EAAE,EAAE,GAAG;UAAEgB,QAAQ,EAAE;QAAU;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACFjI,OAAA,CAACnC,QAAQ;QACP+J,OAAO,EAAC,MAAM;QACdhC,KAAK,EAAC,KAAK;QACXiC,MAAM,EAAE,EAAG;QACXb,EAAE,EAAE;UAAEkB,QAAQ,EAAE;QAAW;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFjI,OAAA,CAACnC,QAAQ;QACP+J,OAAO,EAAC,MAAM;QACdhC,KAAK,EAAC,KAAK;QACXiC,MAAM,EAAE,EAAG;QACXb,EAAE,EAAE;UAAEkB,QAAQ,EAAE,UAAU;UAAEQ,EAAE,EAAE;QAAI;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACP;EAED,MAAMU,YAAY,GAAGA,CAAA,kBACnB3I,OAAA,CAAC1C,GAAG;IAAC0J,EAAE,EAAE;MAAE4B,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAtB,QAAA,eAExBvH,OAAA,CAAClC,KAAK;MACJ0J,SAAS,EAAC,KAAK;MACfG,OAAO,EAAE,CAAE;MACXX,EAAE,EAAE;QAAE8B,YAAY,EAAE,CAAC;QAAET,WAAW,EAAE;MAAU,CAAE;MAAAd,QAAA,gBAGhDvH,OAAA,CAACnC,QAAQ;QACP+J,OAAO,EAAC,MAAM;QACdhC,KAAK,EAAE,GAAI;QACXiC,MAAM,EAAE,EAAG;QACXb,EAAE,EAAE;UACF+B,EAAE,EAAE,CAAC;UACLb,QAAQ,EAAE;QACZ;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFjI,OAAA,CAACnC,QAAQ;QACP+J,OAAO,EAAC,MAAM;QACdhC,KAAK,EAAE,EAAG;QACViC,MAAM,EAAE,EAAG;QACXb,EAAE,EAAE;UACF+B,EAAE,EAAE,CAAC;UACLb,QAAQ,EAAE;QACZ;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFjI,OAAA,CAACnC,QAAQ;QACP+J,OAAO,EAAC,MAAM;QACdhC,KAAK,EAAE,GAAI;QACXiC,MAAM,EAAE,EAAG;QACXb,EAAE,EAAE;UACF+B,EAAE,EAAE,CAAC;UACLb,QAAQ,EAAE;QACZ;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAMe,yBAAyB,GAAGA,CAAA,kBAChChJ,OAAA,CAACpC,IAAI;IAAC0K,SAAS;IAACX,OAAO,EAAE,GAAI;IAAAJ,QAAA,EAC1B,CAAC,GAAG0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBpJ,OAAA,CAACpC,IAAI;MAAC2K,IAAI,EAAE;QAAEC,EAAE,EAAE,EAAE;QAAEa,EAAE,EAAE,CAAC;QAAEZ,EAAE,EAAE;MAAE,CAAE;MAAAlB,QAAA,eACnCvH,OAAA,CAACxC,IAAI;QACHwJ,EAAE,EAAE;UACFC,CAAC,EAAE,GAAG;UACNE,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,gCAAgC;UAC3CC,eAAe,EAAE;QACnB,CAAE;QAAAC,QAAA,gBAGFvH,OAAA,CAAClC,KAAK;UACJ0J,SAAS,EAAC,KAAK;UACfC,cAAc,EAAC,eAAe;UAC9BC,UAAU,EAAC,YAAY;UACvBV,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAK,QAAA,gBAGdvH,OAAA,CAACnC,QAAQ;YACP+J,OAAO,EAAC,MAAM;YACdhC,KAAK,EAAE,GAAI;YACXiC,MAAM,EAAE,EAAG;YACXb,EAAE,EAAE;cAAEkB,QAAQ,EAAE,UAAU;cAAEoB,UAAU,EAAE;YAAI;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAGFjI,OAAA,CAAClC,KAAK;YAAC0J,SAAS,EAAC,KAAK;YAACG,OAAO,EAAE,GAAI;YAAAJ,QAAA,eAClCvH,OAAA,CAACnC,QAAQ;cAAC+J,OAAO,EAAC,SAAS;cAAChC,KAAK,EAAE,EAAG;cAACiC,MAAM,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAIRjI,OAAA,CAACnC,QAAQ;UACP+J,OAAO,EAAC,MAAM;UACdhC,KAAK,EAAC,KAAK;UACXiC,MAAM,EAAE,EAAG;UACXb,EAAE,EAAE;YAAEkB,QAAQ,EAAE,SAAS;YAAEqB,YAAY,EAAE;UAAO;QAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAGFjI,OAAA,CAAClC,KAAK;UAAC0J,SAAS,EAAC,KAAK;UAACG,OAAO,EAAE,GAAI;UAAAJ,QAAA,gBAClCvH,OAAA,CAACnC,QAAQ;YAAC+J,OAAO,EAAC,SAAS;YAAChC,KAAK,EAAE,EAAG;YAACiC,MAAM,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDjI,OAAA,CAACnC,QAAQ;YAAC+J,OAAO,EAAC,SAAS;YAAChC,KAAK,EAAE,EAAG;YAACiC,MAAM,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC,GA7CkCmB,CAAC;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA8CtC,CACP;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACP;EAED,oBACEjI,OAAA,CAAAE,SAAA;IAAAqH,QAAA,gBAEEvH,OAAA,CAAC1C,GAAG;MAAC0J,EAAE,EAAE;QAAEwC,EAAE,EAAE;MAAE;IAAE;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBjI,OAAA,CAACd,UAAU;MAACuK,WAAW,EAAElI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmI;IAAK;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1CjI,OAAA,CAAC1C,GAAG;MAACqM,SAAS,EAAC,MAAM;MAAApC,QAAA,eAEnBvH,OAAA,CAAC1C,GAAG;QAAC0J,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAArB,QAAA,gBAEjBvH,OAAA,CAAC1C,GAAG;UAAC0J,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAtB,QAAA,EACvBlF,cAAc,gBACbrC,OAAA,CAAC2I,YAAY;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhBjI,OAAA,CAAClC,KAAK;YACJ0J,SAAS,EAAC,KAAK;YACfG,OAAO,EAAE,CAAE;YACXX,EAAE,EAAE;cAAE8B,YAAY,EAAE,CAAC;cAAET,WAAW,EAAE;YAAU,CAAE;YAAAd,QAAA,gBAEhDvH,OAAA,CAACzC,MAAM;cACLqM,OAAO,EAAEA,CAAA,KAAMxI,YAAY,CAAC,aAAa,CAAE;cAC3C4F,EAAE,EAAE;gBACF+B,EAAE,EAAE,CAAC;gBACLb,QAAQ,EAAE,EAAE;gBACZoB,UAAU,EAAE,GAAG;gBACfO,aAAa,EAAE,MAAM;gBACrBC,KAAK,EACH3I,SAAS,KAAK,aAAa,GACvB,cAAc,GACd,gBAAgB;gBACtB2H,YAAY,EAAE,CAAC;gBACfT,WAAW,EACTlH,SAAS,KAAK,aAAa,GACvB,cAAc,GACd,aAAa;gBACnBiG,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE;kBACTE,eAAe,EAAE,aAAa;kBAC9BwC,KAAK,EACH3I,SAAS,KAAK,aAAa,GACvB,cAAc,GACd;gBACR;cACF,CAAE;cAAAoG,QAAA,EACH;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjI,OAAA,CAACzC,MAAM;cACLqM,OAAO,EAAEA,CAAA,KAAMxI,YAAY,CAAC,SAAS,CAAE;cACvC4F,EAAE,EAAE;gBACF+B,EAAE,EAAE,CAAC;gBACLb,QAAQ,EAAE,EAAE;gBACZoB,UAAU,EAAE,GAAG;gBACfO,aAAa,EAAE,MAAM;gBACrBC,KAAK,EACH3I,SAAS,KAAK,SAAS,GACnB,cAAc,GACd,gBAAgB;gBACtB2H,YAAY,EAAE,CAAC;gBACfT,WAAW,EACTlH,SAAS,KAAK,SAAS,GAAG,cAAc,GAAG,aAAa;gBAC1DiG,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE;kBACTE,eAAe,EAAE,aAAa;kBAC9BwC,KAAK,EACH3I,SAAS,KAAK,SAAS,GACnB,cAAc,GACd;gBACR;cACF,CAAE;cAAAoG,QAAA,EACH;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjI,OAAA,CAACzC,MAAM;cACLqM,OAAO,EAAEA,CAAA,KAAMxI,YAAY,CAAC,iBAAiB,CAAE;cAC/C4F,EAAE,EAAE;gBACF+B,EAAE,EAAE,CAAC;gBACLb,QAAQ,EAAE,EAAE;gBACZoB,UAAU,EAAE,GAAG;gBACfO,aAAa,EAAE,MAAM;gBACrBC,KAAK,EACH3I,SAAS,KAAK,iBAAiB,GAC3B,cAAc,GACd,gBAAgB;gBACtB2H,YAAY,EAAE,CAAC;gBACfT,WAAW,EACTlH,SAAS,KAAK,iBAAiB,GAC3B,cAAc,GACd,aAAa;gBACnBiG,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE;kBACTE,eAAe,EAAE,aAAa;kBAC9BwC,KAAK,EACH3I,SAAS,KAAK,iBAAiB,GAC3B,cAAc,GACd;gBACR;cACF,CAAE;cAAAoG,QAAA,EACH;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjI,OAAA,CAAC1C,GAAG;UAAC0J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAI,CAAE;UAAAM,QAAA,EACjBlF,cAAc,gBACbrC,OAAA,CAAC1C,GAAG;YAAAiK,QAAA,gBACFvH,OAAA,CAAC+G,qBAAqB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBjI,OAAA,CAACgJ,yBAAyB;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,GACJ9G,SAAS,KAAK,aAAa,gBAC7BnB,OAAA,CAAC1C,GAAG;YAAAiK,QAAA,gBAEFvH,OAAA,CAACxC,IAAI;cACHwJ,EAAE,EAAE;gBACFC,CAAC,EAAE,CAAC;gBACJC,EAAE,EAAE,CAAC;gBACLC,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,CAAC;gBACfC,SAAS,EAAE,gCAAgC;gBAC3C,SAAS,EAAE;kBACTA,SAAS,EAAE;gBACb,CAAC;gBACD0C,UAAU,EAAE,sBAAsB;gBAClCzC,eAAe,EAAE;cACnB,CAAE;cAAAC,QAAA,gBAGFvH,OAAA,CAAClC,KAAK;gBACJ0J,SAAS,EAAC,KAAK;gBACfC,cAAc,EAAC,eAAe;gBAC9BC,UAAU,EAAC,YAAY;gBACvBV,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAK,QAAA,gBAEdvH,OAAA,CAAClC,KAAK;kBAAC0J,SAAS,EAAC,KAAK;kBAACG,OAAO,EAAE,CAAE;kBAACD,UAAU,EAAC,QAAQ;kBAAAH,QAAA,GACnDhG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyI,IAAI,gBACZhK,OAAA,CAAC3C,MAAM;oBACL4M,GAAG,EAAE1I,OAAO,CAACyI,IAAK;oBAClBE,GAAG,EAAC,cAAc;oBAClBlD,EAAE,EAAE;sBACFa,MAAM,EAAE,EAAE;sBACVjC,KAAK,EAAE,EAAE;sBACTwB,YAAY,EAAE;oBAChB;kBAAE;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAEFjI,OAAA,CAAC3C,MAAM;oBACL2J,EAAE,EAAE;sBACFpB,KAAK,EAAE,EAAE;sBACTiC,MAAM,EAAE,EAAE;sBACVsC,UAAU,EAAE,SAAS;sBACrB/C,YAAY,EAAE,CAAC;sBACfc,QAAQ,EAAE,EAAE;sBACZoB,UAAU,EAAE,MAAM;sBAClBQ,KAAK,EAAE;oBACT,CAAE;oBAAAvC,QAAA,EAEDjI,iBAAiB,CAACiC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmI,IAAI;kBAAC;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CACT,eAEDjI,OAAA,CAAC1C,GAAG;oBAAAiK,QAAA,gBACFvH,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,IAAI;sBACZZ,EAAE,EAAE;wBACFsC,UAAU,EAAE,GAAG;wBACfpC,EAAE,EAAE,GAAG;wBACPgB,QAAQ,EAAE,QAAQ;wBAClB4B,KAAK,EAAE;sBACT,CAAE;sBAAAvC,QAAA,EAEDhG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmI;oBAAI;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eAIbjI,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,OAAO;sBACfZ,EAAE,EAAE;wBACF8C,KAAK,EAAE,SAAS;wBAChB5C,EAAE,EAAE,CAAC;wBACLgB,QAAQ,EAAE;sBACZ,CAAE;sBAAAX,QAAA,EAEDhG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE6I,cAAc,gBACtBpK,OAAA,CAAAE,SAAA;wBAAAqH,QAAA,GAAE,yBAEA,eAAAvH,OAAA;0BACEgG,KAAK,EAAE;4BACL8D,KAAK,EAAE,SAAS;4BAChBR,UAAU,EAAE,KAAK;4BACjBe,cAAc,EAAE;0BAClB,CAAE;0BAAA9C,QAAA,EAEDhG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6I;wBAAc;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC;sBAAA,eACP,CAAC,GACD;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGbjI,OAAA,CAAClC,KAAK;sBAAC0J,SAAS,EAAC,KAAK;sBAACG,OAAO,EAAE,CAAE;sBAACD,UAAU,EAAC,QAAQ;sBAAAH,QAAA,eACpDvH,OAAA,CAAClC,KAAK;wBACJ0J,SAAS,EAAC,KAAK;wBACfG,OAAO,EAAE,CAAE;wBACXD,UAAU,EAAC,QAAQ;wBAAAH,QAAA,EAElBxF,YAAY,gBACX/B,OAAA,CAACvC,IAAI;0BACHwG,IAAI,eACFjE,OAAA,CAAC7C,UAAU;4BACT6J,EAAE,EAAE;8BACFkB,QAAQ,EAAE,gBAAgB;8BAC1B4B,KAAK,EAAE;4BACT;0BAAE;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACF;0BACDqC,KAAK,EAAC,WAAW;0BACjB/B,IAAI,EAAC,OAAO;0BACZvB,EAAE,EAAE;4BACFM,eAAe,EAAE,SAAS;4BAC1BwC,KAAK,EAAE,SAAS;4BAChB3C,MAAM,EAAE,MAAM;4BACdU,MAAM,EAAE,EAAE;4BACVK,QAAQ,EAAE,EAAE;4BACZoB,UAAU,EAAE,GAAG;4BACflC,YAAY,EAAE,MAAM;4BACpB,iBAAiB,EAAE;8BACjB0C,KAAK,EAAE,SAAS;8BAChBS,UAAU,EAAE;4BACd,CAAC;4BACD,kBAAkB,EAAE;8BAClBC,WAAW,EAAE,KAAK;8BAClBC,YAAY,EAAE;4BAChB;0BACF;wBAAE;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEFjI,OAAA,CAACvC,IAAI;0BACHwG,IAAI,eACFjE,OAAA,CAAC7C,UAAU;4BACT6J,EAAE,EAAE;8BACFkB,QAAQ,EAAE,gBAAgB;8BAC1B4B,KAAK,EAAE;4BACT;0BAAE;4BAAAhC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACF;0BACDqC,KAAK,EAAC,eAAe;0BACrB/B,IAAI,EAAC,OAAO;0BACZvB,EAAE,EAAE;4BACFM,eAAe,EAAE,SAAS;4BAC1BwC,KAAK,EAAE,SAAS;4BAChB3C,MAAM,EAAE,MAAM;4BACdU,MAAM,EAAE,EAAE;4BACVK,QAAQ,EAAE,EAAE;4BACZoB,UAAU,EAAE,GAAG;4BACflC,YAAY,EAAE,MAAM;4BACpB,iBAAiB,EAAE;8BACjB0C,KAAK,EAAE,SAAS;8BAChBS,UAAU,EAAE;4BACd,CAAC;4BACD,kBAAkB,EAAE;8BAClBC,WAAW,EAAE,KAAK;8BAClBC,YAAY,EAAE;4BAChB;0BACF;wBAAE;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACF;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAOH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGRjI,OAAA,CAAClC,KAAK;kBAAC0J,SAAS,EAAC,KAAK;kBAACG,OAAO,EAAE,CAAE;kBAACD,UAAU,EAAC,QAAQ;kBAAAH,QAAA,gBACpDvH,OAAA,CAACzC,MAAM;oBACLqK,OAAO,EAAC,WAAW;oBACnBW,IAAI,EAAC,OAAO;oBACZvB,EAAE,EAAE;sBACF0D,QAAQ,EAAE,EAAE;sBACZ7C,MAAM,EAAE,EAAE;sBACV8C,OAAO,EAAE,CAAC;sBACVrD,eAAe,EAAE,MAAM;sBACvBwC,KAAK,EAAE,SAAS;sBAChB3C,MAAM,EAAE,mBAAmB;sBAC3BE,SAAS,EAAE,MAAM;sBACjB,SAAS,EAAE;wBACTC,eAAe,EAAE,SAAS;wBAC1BD,SAAS,EAAE;sBACb;oBACF,CAAE;oBAAAE,QAAA,eAEFvH,OAAA,CAAC5C,aAAa;sBAAA0K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,EAERlG,YAAY,gBACX/B,OAAA,CAACzC,MAAM;oBACLqK,OAAO,EAAC,WAAW;oBACnBW,IAAI,EAAC,OAAO;oBACZvB,EAAE,EAAE;sBACFkB,QAAQ,EAAE,MAAM;sBAChBoB,UAAU,EAAE,GAAG;sBACfV,EAAE,EAAE,EAAE;sBACNhD,KAAK,EAAE,GAAG;sBACViC,MAAM,EAAE,EAAE;sBACVgC,aAAa,EAAE,MAAM;sBACrBzC,YAAY,EAAE,GAAG;sBACjBE,eAAe,EAAE,SAAS;sBAC1BwC,KAAK,EAAE,MAAM;sBACbc,UAAU,EAAE,QAAQ;sBACpBC,QAAQ,EAAE,QAAQ;sBAClBC,YAAY,EAAE,UAAU;sBACxB,SAAS,EAAE;wBACTxD,eAAe,EAAE;sBACnB,CAAC;sBACD,YAAY,EAAE;wBACZA,eAAe,EAAE,SAAS;wBAC1BwC,KAAK,EAAE;sBACT;oBACF,CAAE;oBACFF,OAAO,EAAEtE,mBAAoB;oBAC7ByF,QAAQ,EAAE9I,UAAU,IAAII,cAAe;oBAAAkF,QAAA,EAEtCtF,UAAU,GACP,kBAAkB,GAClB;kBAA4B;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,gBAETjI,OAAA,CAAC1C,GAAG;oBACF0J,EAAE,EAAE;sBACFgE,QAAQ,EAAE,UAAU;sBACpBN,QAAQ,EAAE,GAAG;sBACb7C,MAAM,EAAE;oBACV,CAAE;oBAAAN,QAAA,gBAEFvH,OAAA,CAAC1C,GAAG;sBACF2N,SAAS,EAAC,KAAK;sBACfhB,GAAG,EAAE1K,SAAU;sBACf2K,GAAG,EAAC,uBAAuB;sBAC3BlD,EAAE,EAAE;wBACFkE,MAAM,EAAE,SAAS;wBACjBrD,MAAM,EAAE,EAAE;wBACVjC,KAAK,EAAE,MAAM;wBACbmE,UAAU,EAAE,0BAA0B;wBACtCoB,OAAO,EAAElJ,UAAU,IAAII,cAAc,GAAG,GAAG,GAAG,CAAC;wBAC/C+I,aAAa,EACXnJ,UAAU,IAAII,cAAc,GAAG,MAAM,GAAG,MAAM;wBAChD,SAAS,EAAE;0BAAE8I,OAAO,EAAE;wBAAE;sBAC1B,CAAE;sBACFvB,OAAO,EACL,CAAC3H,UAAU,IAAI,CAACI,cAAc,GAC1ByC,gBAAgB,GAChBuG;oBACL;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACFjI,OAAA,CAAC1C,GAAG;sBACF2N,SAAS,EAAC,KAAK;sBACfhB,GAAG,EAAEzK,cAAe;sBACpB0K,GAAG,EAAC,uBAAuB;sBAC3BlD,EAAE,EAAE;wBACFgE,QAAQ,EAAE,UAAU;wBACpBM,GAAG,EAAE,CAAC;wBACNC,IAAI,EAAE,CAAC;wBACP1D,MAAM,EAAE,EAAE;wBACVjC,KAAK,EAAE,MAAM;wBACbuF,OAAO,EAAE,CAAC;wBACVpB,UAAU,EAAE,0BAA0B;wBACtC,SAAS,EAAE;0BAAEoB,OAAO,EAAE;wBAAE,CAAC;wBACzBC,aAAa,EACXnJ,UAAU,IAAII,cAAc,GAAG,MAAM,GAAG;sBAC5C,CAAE;sBACFuH,OAAO,EACL,CAAC3H,UAAU,IAAI,CAACI,cAAc,GAC1ByC,gBAAgB,GAChBuG;oBACL;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,EACD,CAAChG,UAAU,IAAII,cAAc,kBAC5BrC,OAAA,CAAC1C,GAAG;sBACF0J,EAAE,EAAE;wBACFgE,QAAQ,EAAE,UAAU;wBACpBM,GAAG,EAAE,CAAC;wBACNC,IAAI,EAAE,CAAC;wBACPC,KAAK,EAAE,CAAC;wBACRC,MAAM,EAAE,CAAC;wBACTC,OAAO,EAAE,MAAM;wBACfhE,UAAU,EAAE,QAAQ;wBACpBD,cAAc,EAAE,QAAQ;wBACxBH,eAAe,EAAE,0BAA0B;wBAC3CF,YAAY,EAAE;sBAChB,CAAE;sBAAAG,QAAA,eAEFvH,OAAA,CAACtC,gBAAgB;wBAAC6K,IAAI,EAAE;sBAAG;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eAEDjI,OAAA,CAACzC,MAAM;oBACLqK,OAAO,EAAC,UAAU;oBAClBW,IAAI,EAAC,OAAO;oBACZvB,EAAE,EAAE;sBACFkB,QAAQ,EAAE,EAAE;sBACZU,EAAE,EAAE,CAAC;sBACLhD,KAAK,EAAE,GAAG;sBACViC,MAAM,EAAE,EAAE;sBACVgC,aAAa,EAAE,MAAM;sBACrBxB,WAAW,EAAE,SAAS;sBACtByB,KAAK,EAAE,SAAS;sBAChBR,UAAU,EAAE,GAAG;sBACflC,YAAY,EAAE,GAAG;sBACjBwD,UAAU,EAAE,QAAQ;sBACpBC,QAAQ,EAAE,QAAQ;sBAClBC,YAAY,EAAE,UAAU;sBACxB,SAAS,EAAE;wBACTxD,eAAe,EAAE,SAAS;wBAC1Be,WAAW,EAAE;sBACf,CAAC;sBACD,YAAY,EAAE;wBACZf,eAAe,EAAE,SAAS;wBAC1BwC,KAAK,EAAE,SAAS;wBAChBzB,WAAW,EAAE;sBACf;oBACF,CAAE;oBACFuB,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAAC,CAAE;oBAC5BmE,QAAQ,EAAE9I,UAAU,IAAII,cAAe;oBACvCsJ,SAAS,EAAE,IAAK;oBAAApE,QAAA,EACjB;kBAED;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAIRjI,OAAA,CAAC1C,GAAG;gBAAC0J,EAAE,EAAE;kBAAEmB,EAAE,EAAE,CAAC;kBAAEC,SAAS,EAAE,CAAC;kBAAEC,WAAW,EAAE;gBAAU,CAAE;gBAAAd,QAAA,eACvDvH,OAAA,CAACpC,IAAI;kBAAC0K,SAAS;kBAACX,OAAO,EAAE,CAAE;kBAAAJ,QAAA,GAExB,CAAAhG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqK,OAAO,kBACf5L,OAAA,CAACpC,IAAI;oBAAC2K,IAAI,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBAC3BvH,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,SAAS;sBACjBZ,EAAE,EAAE;wBACF8C,KAAK,EAAE,gBAAgB;wBACvBR,UAAU,EAAE,GAAG;wBACfoC,OAAO,EAAE,OAAO;wBAChBxE,EAAE,EAAE,GAAG;wBACP2C,aAAa,EAAE;sBACjB,CAAE;sBAAAtC,QAAA,EACH;oBAED;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjI,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,OAAO;sBACfZ,EAAE,EAAE;wBAAEsC,UAAU,EAAE,GAAG;wBAAEQ,KAAK,EAAE;sBAAe,CAAE;sBAAAvC,QAAA,EAE9C,CAAChG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqK,OAAO,EAAErK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEL,KAAK,EAAEK,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEsK,IAAI,CAAC,CAC/CC,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI;oBAAC;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACP,eAGDjI,OAAA,CAACpC,IAAI;oBAAC2K,IAAI,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBAC3BvH,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,SAAS;sBACjBZ,EAAE,EAAE;wBACF8C,KAAK,EAAE,gBAAgB;wBACvBR,UAAU,EAAE,GAAG;wBACfoC,OAAO,EAAE,OAAO;wBAChBxE,EAAE,EAAE,GAAG;wBACP2C,aAAa,EAAE;sBACjB,CAAE;sBAAAtC,QAAA,EACH;oBAED;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjI,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,OAAO;sBACfZ,EAAE,EAAE;wBAAEsC,UAAU,EAAE,GAAG;wBAAEQ,KAAK,EAAE;sBAAe,CAAE;sBAAAvC,QAAA,EAE9ClI,UAAU,CAACkC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0K,eAAe;oBAAC;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAEPjI,OAAA,CAACpC,IAAI;oBAAC2K,IAAI,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBAC3BvH,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,SAAS;sBACjBZ,EAAE,EAAE;wBACF8C,KAAK,EAAE,gBAAgB;wBACvBR,UAAU,EAAE,GAAG;wBACfoC,OAAO,EAAE,OAAO;wBAChBxE,EAAE,EAAE,GAAG;wBACP2C,aAAa,EAAE;sBACjB,CAAE;sBAAAtC,QAAA,EACH;oBAED;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjI,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,OAAO;sBACfZ,EAAE,EAAE;wBAAEsC,UAAU,EAAE,GAAG;wBAAEQ,KAAK,EAAE;sBAAe,CAAE;sBAAAvC,QAAA,EAE9ChG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2K;oBAAK;sBAAApE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,EAGN,CAAA1G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4K,MAAM,kBACdnM,OAAA,CAACpC,IAAI;oBAAC2K,IAAI,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAAlB,QAAA,gBAC3BvH,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,SAAS;sBACjBZ,EAAE,EAAE;wBACF8C,KAAK,EAAE,gBAAgB;wBACvBR,UAAU,EAAE,GAAG;wBACfoC,OAAO,EAAE,OAAO;wBAChBxE,EAAE,EAAE,GAAG;wBACP2C,aAAa,EAAE;sBACjB,CAAE;sBAAAtC,QAAA,EACH;oBAED;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjI,OAAA,CAACjC,UAAU;sBACT6J,OAAO,EAAC,OAAO;sBACfZ,EAAE,EAAE;wBAAEsC,UAAU,EAAE,GAAG;wBAAEQ,KAAK,EAAE;sBAAe,CAAE;sBAAAvC,QAAA,EAE9ChG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4K;oBAAM;sBAAArE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAEL,CAAA1G,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6K,WAAW,kBACnBpM,OAAA,CAAC1C,GAAG;gBACF0J,EAAE,EAAE;kBACF0B,EAAE,EAAE,CAAC;kBACLP,EAAE,EAAE,CAAC;kBACLC,SAAS,EAAE,CAAC;kBACZC,WAAW,EAAE;gBACf,CAAE;gBAAAd,QAAA,gBAEFvH,OAAA,CAACjC,UAAU;kBACT6J,OAAO,EAAC,SAAS;kBACjBZ,EAAE,EAAE;oBACF8C,KAAK,EAAE,gBAAgB;oBACvBR,UAAU,EAAE,GAAG;oBACfoC,OAAO,EAAE,OAAO;oBAChBxE,EAAE,EAAE,GAAG;oBACP2C,aAAa,EAAE;kBACjB,CAAE;kBAAAtC,QAAA,EACH;gBAED;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EACZ1G,OAAO,CAAC6K,WAAW,CAACC,MAAM,GAAG,EAAE,gBAC9BrM,OAAA,CAAC1C,GAAG;kBAAC2N,SAAS,EAAC,SAAS;kBAACjE,EAAE,EAAE;oBAAEkE,MAAM,EAAE;kBAAU,CAAE;kBAAA3D,QAAA,gBACjDvH,OAAA,CAACjC,UAAU;oBACTkN,SAAS,EAAC,SAAS;oBACnBrD,OAAO,EAAC,OAAO;oBACfZ,EAAE,EAAE;sBACFsC,UAAU,EAAE,GAAG;sBACfQ,KAAK,EAAE,cAAc;sBACrB,SAAS,EAAE;wBAAEA,KAAK,EAAE;sBAAe,CAAC;sBACpCC,UAAU,EAAE;oBACd,CAAE;oBAAAxC,QAAA,GAEDhG,OAAO,CAAC6K,WAAW,CAACpJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KACpC;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbjI,OAAA,CAACjC,UAAU;oBACT6J,OAAO,EAAC,OAAO;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE,CAAC;sBAAEoB,KAAK,EAAE;oBAAiB,CAAE;oBAAAvC,QAAA,EAEtChG,OAAO,CAAC6K;kBAAW;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,gBAENjI,OAAA,CAACjC,UAAU;kBACT6J,OAAO,EAAC,OAAO;kBACfZ,EAAE,EAAE;oBAAEsC,UAAU,EAAE,GAAG;oBAAEQ,KAAK,EAAE;kBAAe,CAAE;kBAAAvC,QAAA,EAE9ChG,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE6K;gBAAW;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGPjI,OAAA,CAACH,oBAAoB;cACnBkC,YAAY,EAAEA,YAAa;cAC3BE,UAAU,EAAEA,UAAW;cACvBE,SAAS,EAAEA,SAAU;cACrBZ,OAAO,EAAEA,OAAQ;cACjB+K,SAAS,EAAExH,gBAAiB;cAC5ByH,YAAY,EAAEjH,mBAAoB;cAClCkH,eAAe,EAAE3H;YAAe;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAGFjI,OAAA,CAACJ,iBAAiB;cAChB6M,YAAY,EAAElL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkL,YAAa;cACpC1K,YAAY,EAAEA,YAAa;cAC3BE,UAAU,EAAEA,UAAW;cACvBI,cAAc,EAAEA,cAAe;cAC/BqK,QAAQ,EAAEjK,eAAgB;cAC1BkK,MAAM,EAAE1J,UAAW;cACnBV,WAAW,EAAEA,WAAY;cACzBqK,WAAW,EAAErL;YAAQ;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,GACJ9G,SAAS,KAAK,SAAS,gBACzBnB,OAAA,CAACL,OAAO;YACNyB,YAAY,EAAEA,YAAa;YAC3BkC,SAAS,EAAErC,EAAG;YACdwL,YAAY,EAAElL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkL;UAAa;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,GACA9G,SAAS,KAAK,iBAAiB,gBACjCnB,OAAA,CAACF,cAAc;YAACwD,SAAS,EAAErC;UAAG;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAC/B;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELtG,SAAS,iBACR3B,OAAA,CAACP,cAAc;MACboN,OAAO,EAAEA,CAAA,KAAMjL,YAAY,CAAC,KAAK,CAAE;MACnC8K,QAAQ,EAAE/J,YAAa;MACvBW,SAAS,EAAErC,EAAG;MACdyB,IAAI,EAAEjB,eAAgB;MACtBqL,SAAS,EAAEzI;IAAU;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACF,eAEDjI,OAAA,CAACN,WAAW;MACVqN,MAAM,EAAElL,eAAgB;MACxBgL,OAAO,EAAEA,CAAA,KAAM/K,kBAAkB,CAAC,KAAK,CAAE;MACzC8K,WAAW,EAAErL,OAAQ;MACrByL,MAAM,EAAEnG;IAAkB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAAA,eACF,CAAC;AAEP;AAACtH,EAAA,CA3tCQD,aAAa;EAAA,QACHrC,WAAW,EACbC,SAAS;AAAA;AAAA2O,EAAA,GAFjBvM,aAAa;AA6tCtB,eAAeA,aAAa;AAAC,IAAAuM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}