{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\QboCallback\\\\qboCallback.jsx\",\n  _s = $RefreshSig$();\n// frontend\\src\\pages\\QboCallback\\qboCallback.jsx\nimport React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { Box, Typography, CircularProgress, Stack, Button, LinearProgress, Fade, Slide, Paper, Divider } from \"@mui/material\";\nimport { CheckCircle as CheckCircleIcon, Error as ErrorIcon, Refresh as RefreshIcon, ArrowForward as ArrowForwardIcon } from \"@mui/icons-material\";\nimport qboButton from \"../../assets/C2QB_green_btn_med_default.svg\";\nimport axiosInstance from \"../../services/axiosInstance\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QboCallback = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [status, setStatus] = useState(\"processing\");\n  const [message, setMessage] = useState(\"Connecting to QuickBooks...\");\n  const [subMessage, setSubMessage] = useState(\"Please wait while we establish the connection...\");\n  const [redirectCountdown, setRedirectCountdown] = useState(null);\n  const [progress, setProgress] = useState(0);\n  const [connectionSteps, setConnectionSteps] = useState([{\n    label: \"Authenticating\",\n    completed: false,\n    active: true\n  }, {\n    label: \"Exchanging tokens\",\n    completed: false,\n    active: false\n  }, {\n    label: \"Verifying connection\",\n    completed: false,\n    active: false\n  }, {\n    label: \"Finalizing setup\",\n    completed: false,\n    active: false\n  }]);\n  const redirectTimeoutRef = useRef(null);\n  const getQueryParams = useCallback(() => {\n    const searchParams = new URLSearchParams(location.search);\n    return {\n      code: searchParams.get(\"code\"),\n      realmId: searchParams.get(\"realmId\"),\n      state: searchParams.get(\"state\")\n    };\n  }, [location.search]);\n  const getCompanyId = useCallback((parsedState, data) => (parsedState === null || parsedState === void 0 ? void 0 : parsedState.companyId) || (data === null || data === void 0 ? void 0 : data.companyId), []);\n  const getRedirectPath = useCallback(companyId => companyId ? `/company/${companyId}` : \"/dashboard\", []);\n  const updateConnectionStep = useCallback((stepIndex, completed = false) => {\n    setConnectionSteps(prev => {\n      const updated = prev.map((step, index) => ({\n        ...step,\n        completed: index < stepIndex ? true : index === stepIndex ? completed : false,\n        active: index === stepIndex && !completed\n      }));\n      setProgress(stepIndex / (updated.length - 1) * 100);\n      return updated;\n    });\n  }, []);\n  useEffect(() => {\n    const fetchCallback = async () => {\n      const {\n        code,\n        realmId,\n        state\n      } = getQueryParams();\n      if (!code || !realmId || !state) {\n        setStatus(\"error\");\n        setMessage(\"Connection Failed\");\n        setSubMessage(\"Authorization with QuickBooks failed.\");\n        setConnectionSteps(prev => prev.map(step => ({\n          ...step,\n          completed: false,\n          active: false\n        })));\n        setRedirectCountdown(5);\n        redirectTimeoutRef.current = setTimeout(() => navigate(\"/dashboard\"), 5000);\n        return;\n      }\n\n      // Start progress simulation\n      updateConnectionStep(0, false);\n      let parsedState;\n      try {\n        parsedState = JSON.parse(decodeURIComponent(state));\n      } catch (error) {\n        parsedState = null;\n      }\n      try {\n        // Step 1: Authenticating\n        setMessage(\"Authenticating with QuickBooks...\");\n        setSubMessage(\"Verifying your authorization credentials...\");\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        updateConnectionStep(0, true);\n\n        // Step 2: Exchanging tokens\n        updateConnectionStep(1, false);\n        setMessage(\"Exchanging Authorization Tokens...\");\n        setSubMessage(\"Securely exchanging tokens with QuickBooks...\");\n        const response = await axiosInstance.get(`${process.env.REACT_APP_API_URL}/api/v1/qbo/callback?code=${encodeURIComponent(code)}&realmId=${encodeURIComponent(realmId)}&state=${encodeURIComponent(state)}`);\n        const data = response.data;\n        updateConnectionStep(1, true);\n\n        // Step 3: Verifying connection\n        updateConnectionStep(2, false);\n        setMessage(\"Verifying Connection...\");\n        setSubMessage(\"Testing the connection to your QuickBooks company...\");\n        await new Promise(resolve => setTimeout(resolve, 800));\n        if (response.status === 200 && data.success) {\n          updateConnectionStep(2, true);\n\n          // Step 4: Finalizing setup\n          updateConnectionStep(3, false);\n          setMessage(\"Finalizing Setup...\");\n          setSubMessage(\"Completing the integration setup...\");\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          updateConnectionStep(3, true);\n          setStatus(\"success\");\n          setMessage(\"ValiSights Company Successfully Connected to QuickBooks!\");\n          setSubMessage(\"Your QuickBooks integration with ValiSights Company is now active and ready to use.\");\n          localStorage.setItem(\"qbo_callback_success\", \"Successfully connected to QuickBooks Online!\");\n          setRedirectCountdown(5);\n          redirectTimeoutRef.current = setTimeout(() => {\n            const companyId = getCompanyId(parsedState, data);\n            navigate(getRedirectPath(companyId), {\n              replace: true\n            });\n          }, 5000);\n        } else {\n          setStatus(\"error\");\n          setMessage(\"Connection Failed\");\n          setSubMessage(data.message || \"Unable to establish connection with QuickBooks.\");\n          setConnectionSteps(prev => prev.map(step => ({\n            ...step,\n            completed: false,\n            active: false\n          })));\n          localStorage.setItem(\"qbo_callback_error\", data.message || \"Error connecting to QuickBooks.\");\n          setRedirectCountdown(10);\n          redirectTimeoutRef.current = setTimeout(() => {\n            var _parsedState;\n            const companyId = (_parsedState = parsedState) === null || _parsedState === void 0 ? void 0 : _parsedState.companyId;\n            navigate(getRedirectPath(companyId), {\n              replace: true\n            });\n          }, 10000);\n        }\n      } catch (error) {\n        var _error$response, _error$response$data;\n        // console.error(\"Connection error:\", error);\n        setStatus(\"error\");\n        setMessage(\"Connection Error\");\n        setSubMessage((error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"An unexpected error occurred while connecting to QuickBooks.\");\n        setConnectionSteps(prev => prev.map(step => ({\n          ...step,\n          completed: false,\n          active: false\n        })));\n        localStorage.setItem(\"qbo_callback_error\", \"Error connecting to QuickBooks.\");\n        setRedirectCountdown(10);\n        redirectTimeoutRef.current = setTimeout(() => {\n          var _parsedState2;\n          const companyId = (_parsedState2 = parsedState) === null || _parsedState2 === void 0 ? void 0 : _parsedState2.companyId;\n          navigate(getRedirectPath(companyId), {\n            replace: true\n          });\n        }, 10000);\n      }\n    };\n    fetchCallback();\n    return () => {\n      if (redirectTimeoutRef.current) clearTimeout(redirectTimeoutRef.current);\n    };\n  }, [location.search, navigate, getQueryParams, getCompanyId, getRedirectPath, updateConnectionStep]);\n  useEffect(() => {\n    if (redirectCountdown === null) return;\n    if (redirectCountdown <= 0) return;\n    const interval = setInterval(() => {\n      setRedirectCountdown(prev => prev > 0 ? prev - 1 : 0);\n    }, 1000);\n    return () => clearInterval(interval);\n  }, [redirectCountdown]);\n  const handleManualRedirect = e => {\n    var _parsedState3;\n    e.preventDefault();\n    if (redirectTimeoutRef.current) clearTimeout(redirectTimeoutRef.current);\n    const {\n      state\n    } = getQueryParams();\n    let parsedState = null;\n    try {\n      parsedState = JSON.parse(decodeURIComponent(state));\n    } catch {}\n    const companyId = (_parsedState3 = parsedState) === null || _parsedState3 === void 0 ? void 0 : _parsedState3.companyId;\n    navigate(getRedirectPath(companyId), {\n      replace: true\n    });\n  };\n  const handleRetryConnection = () => {\n    window.location.reload();\n  };\n  const renderIcon = () => {\n    switch (status) {\n      case \"processing\":\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"relative\",\n            display: \"inline-flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 60,\n            thickness: 4,\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              top: 0,\n              left: 0,\n              bottom: 0,\n              right: 0,\n              position: \"absolute\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: qboButton,\n              alt: \"QuickBooks\",\n              sx: {\n                width: 24,\n                height: 24,\n                filter: \"brightness(1.2)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this);\n      case \"success\":\n        return /*#__PURE__*/_jsxDEV(Fade, {\n          in: true,\n          timeout: 800,\n          children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            sx: {\n              fontSize: 60,\n              color: \"success.main\",\n              filter: \"drop-shadow(0 2px 4px rgba(0,0,0,0.1))\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this);\n      case \"error\":\n        return /*#__PURE__*/_jsxDEV(Fade, {\n          in: true,\n          timeout: 800,\n          children: /*#__PURE__*/_jsxDEV(ErrorIcon, {\n            sx: {\n              fontSize: 60,\n              color: \"error.main\",\n              filter: \"drop-shadow(0 2px 4px rgba(0,0,0,0.1))\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const renderConnectionSteps = () => {\n    if (status !== \"processing\") return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: \"100%\",\n        maxWidth: 400,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\",\n        value: progress,\n        sx: {\n          height: 6,\n          borderRadius: 3,\n          backgroundColor: \"grey.200\",\n          \"& .MuiLinearProgress-bar\": {\n            borderRadius: 3\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 1,\n        sx: {\n          mt: 2\n        },\n        children: connectionSteps.map((step, index) => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 16,\n              height: 16,\n              borderRadius: \"50%\",\n              backgroundColor: step.completed ? \"success.main\" : step.active ? \"primary.main\" : \"grey.300\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              transition: \"all 0.3s ease\"\n            },\n            children: [step.completed && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              sx: {\n                fontSize: 12,\n                color: \"white\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 19\n            }, this), step.active && !step.completed && /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 10,\n              thickness: 6,\n              sx: {\n                color: \"white\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: step.completed || step.active ? \"text.primary\" : \"text.secondary\",\n              fontWeight: step.active ? 600 : 400,\n              transition: \"all 0.3s ease\"\n            },\n            children: step.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    minHeight: \"100vh\",\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    bgcolor: \"#f8fafc\",\n    px: 2,\n    children: /*#__PURE__*/_jsxDEV(Slide, {\n      direction: \"up\",\n      in: true,\n      timeout: 600,\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 8,\n        sx: {\n          backgroundColor: \"white\",\n          padding: {\n            xs: 3,\n            sm: 5\n          },\n          borderRadius: 3,\n          maxWidth: 500,\n          width: \"100%\",\n          textAlign: \"center\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 4,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              spacing: 2,\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: 600,\n                color: \"primary.main\",\n                children: \"ValiSights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this), \"+\", /*#__PURE__*/_jsxDEV(Box, {\n                component: \"img\",\n                src: qboButton,\n                alt: \"QuickBooks\",\n                sx: {\n                  height: 40,\n                  width: \"auto\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: 600,\n                color: \"text.secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: 700,\n              color: \"text.primary\",\n              sx: {\n                mb: 1\n              },\n              children: \"Connect ValiSights Company to QuickBooks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                width: 80,\n                mx: \"auto\",\n                height: 3,\n                bgcolor: \"primary.main\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), renderIcon(), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: 600,\n              color: \"text.primary\",\n              sx: {\n                mb: 1\n              },\n              children: message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                maxWidth: 400\n              },\n              children: subMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), renderConnectionSteps(), redirectCountdown !== null && redirectCountdown > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Redirecting in \", redirectCountdown, \" second\", redirectCountdown !== 1 ? \"s\" : \"\", \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 2,\n              justifyContent: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                size: \"small\",\n                onClick: handleManualRedirect,\n                startIcon: /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 32\n                }, this),\n                children: \"Continue Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), status === \"error\" && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                size: \"small\",\n                onClick: handleRetryConnection,\n                startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 34\n                }, this),\n                color: \"primary\",\n                children: \"Retry Connection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this), status === \"processing\" && !redirectCountdown && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            sx: {\n              fontStyle: \"italic\"\n            },\n            children: \"This may take a few moments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 376,\n    columnNumber: 5\n  }, this);\n};\n_s(QboCallback, \"CTBWUr+E1hjOvynAzWTZRev1KTg=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = QboCallback;\nexport default QboCallback;\nvar _c;\n$RefreshReg$(_c, \"QboCallback\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "useLocation", "useNavigate", "Box", "Typography", "CircularProgress", "<PERSON><PERSON>", "<PERSON><PERSON>", "LinearProgress", "Fade", "Slide", "Paper", "Divider", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Refresh", "RefreshIcon", "ArrowForward", "ArrowForwardIcon", "qboButton", "axiosInstance", "jsxDEV", "_jsxDEV", "QboCallback", "_s", "location", "navigate", "status", "setStatus", "message", "setMessage", "subMessage", "setSubMessage", "redirectCountdown", "setRedirectCountdown", "progress", "setProgress", "connectionSteps", "setConnectionSteps", "label", "completed", "active", "redirectTimeoutRef", "getQueryParams", "searchParams", "URLSearchParams", "search", "code", "get", "realmId", "state", "getCompanyId", "parsedState", "data", "companyId", "getRedirectPath", "updateConnectionStep", "stepIndex", "prev", "updated", "map", "step", "index", "length", "fetchCallback", "current", "setTimeout", "JSON", "parse", "decodeURIComponent", "error", "Promise", "resolve", "response", "process", "env", "REACT_APP_API_URL", "encodeURIComponent", "success", "localStorage", "setItem", "replace", "_parsedState", "_error$response", "_error$response$data", "_parsedState2", "clearTimeout", "interval", "setInterval", "clearInterval", "handleManualRedirect", "e", "_parsedState3", "preventDefault", "handleRetryConnection", "window", "reload", "renderIcon", "sx", "position", "display", "children", "size", "thickness", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "bottom", "right", "alignItems", "justifyContent", "component", "src", "alt", "width", "height", "filter", "in", "timeout", "fontSize", "renderConnectionSteps", "max<PERSON><PERSON><PERSON>", "mt", "variant", "value", "borderRadius", "backgroundColor", "spacing", "gap", "transition", "fontWeight", "minHeight", "bgcolor", "px", "direction", "elevation", "padding", "xs", "sm", "textAlign", "mb", "mx", "onClick", "startIcon", "fontStyle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/QboCallback/qboCallback.jsx"], "sourcesContent": ["// frontend\\src\\pages\\QboCallback\\qboCallback.jsx\r\nimport React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport { useLocation, useNavigate } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  CircularProgress,\r\n  Stack,\r\n  Button,\r\n  LinearProgress,\r\n  Fade,\r\n  Slide,\r\n  Paper,\r\n  Divider,\r\n} from \"@mui/material\";\r\nimport {\r\n  CheckCircle as CheckCircleIcon,\r\n  Error as ErrorIcon,\r\n  Refresh as RefreshIcon,\r\n  ArrowForward as ArrowForwardIcon,\r\n} from \"@mui/icons-material\";\r\nimport qboButton from \"../../assets/C2QB_green_btn_med_default.svg\";\r\nimport axiosInstance from \"../../services/axiosInstance\";\r\n\r\nconst QboCallback = () => {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const [status, setStatus] = useState(\"processing\");\r\n  const [message, setMessage] = useState(\"Connecting to QuickBooks...\");\r\n  const [subMessage, setSubMessage] = useState(\r\n    \"Please wait while we establish the connection...\"\r\n  );\r\n  const [redirectCountdown, setRedirectCountdown] = useState(null);\r\n  const [progress, setProgress] = useState(0);\r\n  const [connectionSteps, setConnectionSteps] = useState([\r\n    { label: \"Authenticating\", completed: false, active: true },\r\n    { label: \"Exchanging tokens\", completed: false, active: false },\r\n    { label: \"Verifying connection\", completed: false, active: false },\r\n    { label: \"Finalizing setup\", completed: false, active: false },\r\n  ]);\r\n  const redirectTimeoutRef = useRef(null);\r\n\r\n  const getQueryParams = useCallback(() => {\r\n    const searchParams = new URLSearchParams(location.search);\r\n    return {\r\n      code: searchParams.get(\"code\"),\r\n      realmId: searchParams.get(\"realmId\"),\r\n      state: searchParams.get(\"state\"),\r\n    };\r\n  }, [location.search]);\r\n\r\n  const getCompanyId = useCallback(\r\n    (parsedState, data) => parsedState?.companyId || data?.companyId,\r\n    []\r\n  );\r\n\r\n  const getRedirectPath = useCallback(\r\n    (companyId) => (companyId ? `/company/${companyId}` : \"/dashboard\"),\r\n    []\r\n  );\r\n\r\n  const updateConnectionStep = useCallback((stepIndex, completed = false) => {\r\n    setConnectionSteps((prev) => {\r\n      const updated = prev.map((step, index) => ({\r\n        ...step,\r\n        completed:\r\n          index < stepIndex ? true : index === stepIndex ? completed : false,\r\n        active: index === stepIndex && !completed,\r\n      }));\r\n      setProgress((stepIndex / (updated.length - 1)) * 100);\r\n      return updated;\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchCallback = async () => {\r\n      const { code, realmId, state } = getQueryParams();\r\n\r\n      if (!code || !realmId || !state) {\r\n        setStatus(\"error\");\r\n        setMessage(\"Connection Failed\");\r\n        setSubMessage(\"Authorization with QuickBooks failed.\");\r\n        setConnectionSteps((prev) =>\r\n          prev.map((step) => ({ ...step, completed: false, active: false }))\r\n        );\r\n        setRedirectCountdown(5);\r\n        redirectTimeoutRef.current = setTimeout(\r\n          () => navigate(\"/dashboard\"),\r\n          5000\r\n        );\r\n        return;\r\n      }\r\n\r\n      // Start progress simulation\r\n      updateConnectionStep(0, false);\r\n\r\n      let parsedState;\r\n      try {\r\n        parsedState = JSON.parse(decodeURIComponent(state));\r\n      } catch (error) {\r\n        parsedState = null;\r\n      }\r\n\r\n      try {\r\n        // Step 1: Authenticating\r\n        setMessage(\"Authenticating with QuickBooks...\");\r\n        setSubMessage(\"Verifying your authorization credentials...\");\r\n        await new Promise((resolve) => setTimeout(resolve, 1000));\r\n        updateConnectionStep(0, true);\r\n\r\n        // Step 2: Exchanging tokens\r\n        updateConnectionStep(1, false);\r\n        setMessage(\"Exchanging Authorization Tokens...\");\r\n        setSubMessage(\"Securely exchanging tokens with QuickBooks...\");\r\n\r\n        const response = await axiosInstance.get(\r\n          `${\r\n            process.env.REACT_APP_API_URL\r\n          }/api/v1/qbo/callback?code=${encodeURIComponent(\r\n            code\r\n          )}&realmId=${encodeURIComponent(realmId)}&state=${encodeURIComponent(\r\n            state\r\n          )}`\r\n        );\r\n\r\n        const data = response.data;\r\n        updateConnectionStep(1, true);\r\n\r\n        // Step 3: Verifying connection\r\n        updateConnectionStep(2, false);\r\n        setMessage(\"Verifying Connection...\");\r\n        setSubMessage(\"Testing the connection to your QuickBooks company...\");\r\n        await new Promise((resolve) => setTimeout(resolve, 800));\r\n\r\n        if (response.status === 200 && data.success) {\r\n          updateConnectionStep(2, true);\r\n\r\n          // Step 4: Finalizing setup\r\n          updateConnectionStep(3, false);\r\n          setMessage(\"Finalizing Setup...\");\r\n          setSubMessage(\"Completing the integration setup...\");\r\n          await new Promise((resolve) => setTimeout(resolve, 1000));\r\n          updateConnectionStep(3, true);\r\n\r\n          setStatus(\"success\");\r\n          setMessage(\r\n            \"ValiSights Company Successfully Connected to QuickBooks!\"\r\n          );\r\n          setSubMessage(\r\n            \"Your QuickBooks integration with ValiSights Company is now active and ready to use.\"\r\n          );\r\n          localStorage.setItem(\r\n            \"qbo_callback_success\",\r\n            \"Successfully connected to QuickBooks Online!\"\r\n          );\r\n\r\n          setRedirectCountdown(5);\r\n          redirectTimeoutRef.current = setTimeout(() => {\r\n            const companyId = getCompanyId(parsedState, data);\r\n            navigate(getRedirectPath(companyId), { replace: true });\r\n          }, 5000);\r\n        } else {\r\n          setStatus(\"error\");\r\n          setMessage(\"Connection Failed\");\r\n          setSubMessage(\r\n            data.message || \"Unable to establish connection with QuickBooks.\"\r\n          );\r\n          setConnectionSteps((prev) =>\r\n            prev.map((step) => ({ ...step, completed: false, active: false }))\r\n          );\r\n          localStorage.setItem(\r\n            \"qbo_callback_error\",\r\n            data.message || \"Error connecting to QuickBooks.\"\r\n          );\r\n\r\n          setRedirectCountdown(10);\r\n          redirectTimeoutRef.current = setTimeout(() => {\r\n            const companyId = parsedState?.companyId;\r\n            navigate(getRedirectPath(companyId), { replace: true });\r\n          }, 10000);\r\n        }\r\n      } catch (error) {\r\n        // console.error(\"Connection error:\", error);\r\n        setStatus(\"error\");\r\n        setMessage(\"Connection Error\");\r\n        setSubMessage(\r\n          error?.response?.data?.message ||\r\n            \"An unexpected error occurred while connecting to QuickBooks.\"\r\n        );\r\n        setConnectionSteps((prev) =>\r\n          prev.map((step) => ({ ...step, completed: false, active: false }))\r\n        );\r\n        localStorage.setItem(\r\n          \"qbo_callback_error\",\r\n          \"Error connecting to QuickBooks.\"\r\n        );\r\n\r\n        setRedirectCountdown(10);\r\n        redirectTimeoutRef.current = setTimeout(() => {\r\n          const companyId = parsedState?.companyId;\r\n          navigate(getRedirectPath(companyId), { replace: true });\r\n        }, 10000);\r\n      }\r\n    };\r\n\r\n    fetchCallback();\r\n\r\n    return () => {\r\n      if (redirectTimeoutRef.current) clearTimeout(redirectTimeoutRef.current);\r\n    };\r\n  }, [\r\n    location.search,\r\n    navigate,\r\n    getQueryParams,\r\n    getCompanyId,\r\n    getRedirectPath,\r\n    updateConnectionStep,\r\n  ]);\r\n\r\n  useEffect(() => {\r\n    if (redirectCountdown === null) return;\r\n    if (redirectCountdown <= 0) return;\r\n    const interval = setInterval(() => {\r\n      setRedirectCountdown((prev) => (prev > 0 ? prev - 1 : 0));\r\n    }, 1000);\r\n    return () => clearInterval(interval);\r\n  }, [redirectCountdown]);\r\n\r\n  const handleManualRedirect = (e) => {\r\n    e.preventDefault();\r\n    if (redirectTimeoutRef.current) clearTimeout(redirectTimeoutRef.current);\r\n    const { state } = getQueryParams();\r\n    let parsedState = null;\r\n    try {\r\n      parsedState = JSON.parse(decodeURIComponent(state));\r\n    } catch {}\r\n    const companyId = parsedState?.companyId;\r\n    navigate(getRedirectPath(companyId), { replace: true });\r\n  };\r\n\r\n  const handleRetryConnection = () => {\r\n    window.location.reload();\r\n  };\r\n\r\n  const renderIcon = () => {\r\n    switch (status) {\r\n      case \"processing\":\r\n        return (\r\n          <Box sx={{ position: \"relative\", display: \"inline-flex\" }}>\r\n            <CircularProgress size={60} thickness={4} color=\"primary\" />\r\n            <Box\r\n              sx={{\r\n                top: 0,\r\n                left: 0,\r\n                bottom: 0,\r\n                right: 0,\r\n                position: \"absolute\",\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\",\r\n              }}\r\n            >\r\n              <Box\r\n                component=\"img\"\r\n                src={qboButton}\r\n                alt=\"QuickBooks\"\r\n                sx={{\r\n                  width: 24,\r\n                  height: 24,\r\n                  filter: \"brightness(1.2)\",\r\n                }}\r\n              />\r\n            </Box>\r\n          </Box>\r\n        );\r\n      case \"success\":\r\n        return (\r\n          <Fade in timeout={800}>\r\n            <CheckCircleIcon\r\n              sx={{\r\n                fontSize: 60,\r\n                color: \"success.main\",\r\n                filter: \"drop-shadow(0 2px 4px rgba(0,0,0,0.1))\",\r\n              }}\r\n            />\r\n          </Fade>\r\n        );\r\n      case \"error\":\r\n        return (\r\n          <Fade in timeout={800}>\r\n            <ErrorIcon\r\n              sx={{\r\n                fontSize: 60,\r\n                color: \"error.main\",\r\n                filter: \"drop-shadow(0 2px 4px rgba(0,0,0,0.1))\",\r\n              }}\r\n            />\r\n          </Fade>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const renderConnectionSteps = () => {\r\n    if (status !== \"processing\") return null;\r\n\r\n    return (\r\n      <Box sx={{ width: \"100%\", maxWidth: 400, mt: 3 }}>\r\n        <LinearProgress\r\n          variant=\"determinate\"\r\n          value={progress}\r\n          sx={{\r\n            height: 6,\r\n            borderRadius: 3,\r\n            backgroundColor: \"grey.200\",\r\n            \"& .MuiLinearProgress-bar\": {\r\n              borderRadius: 3,\r\n            },\r\n          }}\r\n        />\r\n        <Stack spacing={1} sx={{ mt: 2 }}>\r\n          {connectionSteps.map((step, index) => (\r\n            <Box\r\n              key={index}\r\n              sx={{ display: \"flex\", alignItems: \"center\", gap: 1 }}\r\n            >\r\n              <Box\r\n                sx={{\r\n                  width: 16,\r\n                  height: 16,\r\n                  borderRadius: \"50%\",\r\n                  backgroundColor: step.completed\r\n                    ? \"success.main\"\r\n                    : step.active\r\n                    ? \"primary.main\"\r\n                    : \"grey.300\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  transition: \"all 0.3s ease\",\r\n                }}\r\n              >\r\n                {step.completed && (\r\n                  <CheckCircleIcon sx={{ fontSize: 12, color: \"white\" }} />\r\n                )}\r\n                {step.active && !step.completed && (\r\n                  <CircularProgress\r\n                    size={10}\r\n                    thickness={6}\r\n                    sx={{ color: \"white\" }}\r\n                  />\r\n                )}\r\n              </Box>\r\n              <Typography\r\n                variant=\"caption\"\r\n                sx={{\r\n                  color:\r\n                    step.completed || step.active\r\n                      ? \"text.primary\"\r\n                      : \"text.secondary\",\r\n                  fontWeight: step.active ? 600 : 400,\r\n                  transition: \"all 0.3s ease\",\r\n                }}\r\n              >\r\n                {step.label}\r\n              </Typography>\r\n            </Box>\r\n          ))}\r\n        </Stack>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box\r\n      minHeight=\"100vh\"\r\n      display=\"flex\"\r\n      alignItems=\"center\"\r\n      justifyContent=\"center\"\r\n      bgcolor=\"#f8fafc\"\r\n      px={2}\r\n    >\r\n      <Slide direction=\"up\" in timeout={600}>\r\n        <Paper\r\n          elevation={8}\r\n          sx={{\r\n            backgroundColor: \"white\",\r\n            padding: { xs: 3, sm: 5 },\r\n            borderRadius: 3,\r\n            maxWidth: 500,\r\n            width: \"100%\",\r\n            textAlign: \"center\",\r\n          }}\r\n        >\r\n          <Stack spacing={4} alignItems=\"center\">\r\n            {/* Header */}\r\n            <Box>\r\n              <Stack\r\n                direction=\"row\"\r\n                alignItems=\"center\"\r\n                justifyContent=\"center\"\r\n                spacing={2}\r\n                sx={{ mb: 2 }}\r\n              >\r\n                <Typography variant=\"h6\" fontWeight={600} color=\"primary.main\">\r\n                  ValiSights\r\n                </Typography>\r\n                +\r\n                <Box\r\n                  component=\"img\"\r\n                  src={qboButton}\r\n                  alt=\"QuickBooks\"\r\n                  sx={{ height: 40, width: \"auto\" }}\r\n                />\r\n                <Typography\r\n                  variant=\"h5\"\r\n                  fontWeight={600}\r\n                  color=\"text.secondary\"\r\n                />\r\n              </Stack>\r\n              <Typography\r\n                variant=\"h4\"\r\n                fontWeight={700}\r\n                color=\"text.primary\"\r\n                sx={{ mb: 1 }}\r\n              >\r\n                Connect ValiSights Company to QuickBooks\r\n              </Typography>\r\n              <Divider\r\n                sx={{\r\n                  width: 80,\r\n                  mx: \"auto\",\r\n                  height: 3,\r\n                  bgcolor: \"primary.main\",\r\n                }}\r\n              />\r\n            </Box>\r\n\r\n            {/* Icon */}\r\n            {renderIcon()}\r\n\r\n            {/* Main Message */}\r\n            <Box sx={{ textAlign: \"center\" }}>\r\n              <Typography\r\n                variant=\"h6\"\r\n                fontWeight={600}\r\n                color=\"text.primary\"\r\n                sx={{ mb: 1 }}\r\n              >\r\n                {message}\r\n              </Typography>\r\n              <Typography\r\n                variant=\"body2\"\r\n                color=\"text.secondary\"\r\n                sx={{ maxWidth: 400 }}\r\n              >\r\n                {subMessage}\r\n              </Typography>\r\n            </Box>\r\n\r\n            {/* Connection Steps */}\r\n            {renderConnectionSteps()}\r\n\r\n            {/* Action Buttons and Countdown */}\r\n            {redirectCountdown !== null && redirectCountdown > 0 && (\r\n              <Box sx={{ textAlign: \"center\" }}>\r\n                <Typography\r\n                  variant=\"body2\"\r\n                  color=\"text.secondary\"\r\n                  sx={{ mb: 2 }}\r\n                >\r\n                  Redirecting in {redirectCountdown} second\r\n                  {redirectCountdown !== 1 ? \"s\" : \"\"}...\r\n                </Typography>\r\n                <Stack direction=\"row\" spacing={2} justifyContent=\"center\">\r\n                  <Button\r\n                    variant=\"outlined\"\r\n                    size=\"small\"\r\n                    onClick={handleManualRedirect}\r\n                    startIcon={<ArrowForwardIcon />}\r\n                  >\r\n                    Continue Now\r\n                  </Button>\r\n                  {status === \"error\" && (\r\n                    <Button\r\n                      variant=\"contained\"\r\n                      size=\"small\"\r\n                      onClick={handleRetryConnection}\r\n                      startIcon={<RefreshIcon />}\r\n                      color=\"primary\"\r\n                    >\r\n                      Retry Connection\r\n                    </Button>\r\n                  )}\r\n                </Stack>\r\n              </Box>\r\n            )}\r\n\r\n            {/* Processing Message */}\r\n            {status === \"processing\" && !redirectCountdown && (\r\n              <Typography\r\n                variant=\"caption\"\r\n                color=\"text.secondary\"\r\n                sx={{ fontStyle: \"italic\" }}\r\n              >\r\n                This may take a few moments...\r\n              </Typography>\r\n            )}\r\n          </Stack>\r\n        </Paper>\r\n      </Slide>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default QboCallback;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,GAAG,EACHC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,MAAM,EACNC,cAAc,EACdC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,SAAS,MAAM,6CAA6C;AACnE,OAAOC,aAAa,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAC,YAAY,CAAC;EAClD,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,6BAA6B,CAAC;EACrE,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAC1C,kDACF,CAAC;EACD,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,CACrD;IAAE2C,KAAK,EAAE,gBAAgB;IAAEC,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAK,CAAC,EAC3D;IAAEF,KAAK,EAAE,mBAAmB;IAAEC,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC/D;IAAEF,KAAK,EAAE,sBAAsB;IAAEC,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAM,CAAC,EAClE;IAAEF,KAAK,EAAE,kBAAkB;IAAEC,SAAS,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC/D,CAAC;EACF,MAAMC,kBAAkB,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAEvC,MAAM8C,cAAc,GAAG7C,WAAW,CAAC,MAAM;IACvC,MAAM8C,YAAY,GAAG,IAAIC,eAAe,CAACpB,QAAQ,CAACqB,MAAM,CAAC;IACzD,OAAO;MACLC,IAAI,EAAEH,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC;MAC9BC,OAAO,EAAEL,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC;MACpCE,KAAK,EAAEN,YAAY,CAACI,GAAG,CAAC,OAAO;IACjC,CAAC;EACH,CAAC,EAAE,CAACvB,QAAQ,CAACqB,MAAM,CAAC,CAAC;EAErB,MAAMK,YAAY,GAAGrD,WAAW,CAC9B,CAACsD,WAAW,EAAEC,IAAI,KAAK,CAAAD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,SAAS,MAAID,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,SAAS,GAChE,EACF,CAAC;EAED,MAAMC,eAAe,GAAGzD,WAAW,CAChCwD,SAAS,IAAMA,SAAS,GAAG,YAAYA,SAAS,EAAE,GAAG,YAAa,EACnE,EACF,CAAC;EAED,MAAME,oBAAoB,GAAG1D,WAAW,CAAC,CAAC2D,SAAS,EAAEjB,SAAS,GAAG,KAAK,KAAK;IACzEF,kBAAkB,CAAEoB,IAAI,IAAK;MAC3B,MAAMC,OAAO,GAAGD,IAAI,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;QACzC,GAAGD,IAAI;QACPrB,SAAS,EACPsB,KAAK,GAAGL,SAAS,GAAG,IAAI,GAAGK,KAAK,KAAKL,SAAS,GAAGjB,SAAS,GAAG,KAAK;QACpEC,MAAM,EAAEqB,KAAK,KAAKL,SAAS,IAAI,CAACjB;MAClC,CAAC,CAAC,CAAC;MACHJ,WAAW,CAAEqB,SAAS,IAAIE,OAAO,CAACI,MAAM,GAAG,CAAC,CAAC,GAAI,GAAG,CAAC;MACrD,OAAOJ,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAENhE,SAAS,CAAC,MAAM;IACd,MAAMqE,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,MAAM;QAAEjB,IAAI;QAAEE,OAAO;QAAEC;MAAM,CAAC,GAAGP,cAAc,CAAC,CAAC;MAEjD,IAAI,CAACI,IAAI,IAAI,CAACE,OAAO,IAAI,CAACC,KAAK,EAAE;QAC/BtB,SAAS,CAAC,OAAO,CAAC;QAClBE,UAAU,CAAC,mBAAmB,CAAC;QAC/BE,aAAa,CAAC,uCAAuC,CAAC;QACtDM,kBAAkB,CAAEoB,IAAI,IACtBA,IAAI,CAACE,GAAG,CAAEC,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAErB,SAAS,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAM,CAAC,CAAC,CACnE,CAAC;QACDP,oBAAoB,CAAC,CAAC,CAAC;QACvBQ,kBAAkB,CAACuB,OAAO,GAAGC,UAAU,CACrC,MAAMxC,QAAQ,CAAC,YAAY,CAAC,EAC5B,IACF,CAAC;QACD;MACF;;MAEA;MACA8B,oBAAoB,CAAC,CAAC,EAAE,KAAK,CAAC;MAE9B,IAAIJ,WAAW;MACf,IAAI;QACFA,WAAW,GAAGe,IAAI,CAACC,KAAK,CAACC,kBAAkB,CAACnB,KAAK,CAAC,CAAC;MACrD,CAAC,CAAC,OAAOoB,KAAK,EAAE;QACdlB,WAAW,GAAG,IAAI;MACpB;MAEA,IAAI;QACF;QACAtB,UAAU,CAAC,mCAAmC,CAAC;QAC/CE,aAAa,CAAC,6CAA6C,CAAC;QAC5D,MAAM,IAAIuC,OAAO,CAAEC,OAAO,IAAKN,UAAU,CAACM,OAAO,EAAE,IAAI,CAAC,CAAC;QACzDhB,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC;;QAE7B;QACAA,oBAAoB,CAAC,CAAC,EAAE,KAAK,CAAC;QAC9B1B,UAAU,CAAC,oCAAoC,CAAC;QAChDE,aAAa,CAAC,+CAA+C,CAAC;QAE9D,MAAMyC,QAAQ,GAAG,MAAMrD,aAAa,CAAC4B,GAAG,CACtC,GACE0B,OAAO,CAACC,GAAG,CAACC,iBAAiB,6BACFC,kBAAkB,CAC7C9B,IACF,CAAC,YAAY8B,kBAAkB,CAAC5B,OAAO,CAAC,UAAU4B,kBAAkB,CAClE3B,KACF,CAAC,EACH,CAAC;QAED,MAAMG,IAAI,GAAGoB,QAAQ,CAACpB,IAAI;QAC1BG,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC;;QAE7B;QACAA,oBAAoB,CAAC,CAAC,EAAE,KAAK,CAAC;QAC9B1B,UAAU,CAAC,yBAAyB,CAAC;QACrCE,aAAa,CAAC,sDAAsD,CAAC;QACrE,MAAM,IAAIuC,OAAO,CAAEC,OAAO,IAAKN,UAAU,CAACM,OAAO,EAAE,GAAG,CAAC,CAAC;QAExD,IAAIC,QAAQ,CAAC9C,MAAM,KAAK,GAAG,IAAI0B,IAAI,CAACyB,OAAO,EAAE;UAC3CtB,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC;;UAE7B;UACAA,oBAAoB,CAAC,CAAC,EAAE,KAAK,CAAC;UAC9B1B,UAAU,CAAC,qBAAqB,CAAC;UACjCE,aAAa,CAAC,qCAAqC,CAAC;UACpD,MAAM,IAAIuC,OAAO,CAAEC,OAAO,IAAKN,UAAU,CAACM,OAAO,EAAE,IAAI,CAAC,CAAC;UACzDhB,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC;UAE7B5B,SAAS,CAAC,SAAS,CAAC;UACpBE,UAAU,CACR,0DACF,CAAC;UACDE,aAAa,CACX,qFACF,CAAC;UACD+C,YAAY,CAACC,OAAO,CAClB,sBAAsB,EACtB,8CACF,CAAC;UAED9C,oBAAoB,CAAC,CAAC,CAAC;UACvBQ,kBAAkB,CAACuB,OAAO,GAAGC,UAAU,CAAC,MAAM;YAC5C,MAAMZ,SAAS,GAAGH,YAAY,CAACC,WAAW,EAAEC,IAAI,CAAC;YACjD3B,QAAQ,CAAC6B,eAAe,CAACD,SAAS,CAAC,EAAE;cAAE2B,OAAO,EAAE;YAAK,CAAC,CAAC;UACzD,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLrD,SAAS,CAAC,OAAO,CAAC;UAClBE,UAAU,CAAC,mBAAmB,CAAC;UAC/BE,aAAa,CACXqB,IAAI,CAACxB,OAAO,IAAI,iDAClB,CAAC;UACDS,kBAAkB,CAAEoB,IAAI,IACtBA,IAAI,CAACE,GAAG,CAAEC,IAAI,KAAM;YAAE,GAAGA,IAAI;YAAErB,SAAS,EAAE,KAAK;YAAEC,MAAM,EAAE;UAAM,CAAC,CAAC,CACnE,CAAC;UACDsC,YAAY,CAACC,OAAO,CAClB,oBAAoB,EACpB3B,IAAI,CAACxB,OAAO,IAAI,iCAClB,CAAC;UAEDK,oBAAoB,CAAC,EAAE,CAAC;UACxBQ,kBAAkB,CAACuB,OAAO,GAAGC,UAAU,CAAC,MAAM;YAAA,IAAAgB,YAAA;YAC5C,MAAM5B,SAAS,IAAA4B,YAAA,GAAG9B,WAAW,cAAA8B,YAAA,uBAAXA,YAAA,CAAa5B,SAAS;YACxC5B,QAAQ,CAAC6B,eAAe,CAACD,SAAS,CAAC,EAAE;cAAE2B,OAAO,EAAE;YAAK,CAAC,CAAC;UACzD,CAAC,EAAE,KAAK,CAAC;QACX;MACF,CAAC,CAAC,OAAOX,KAAK,EAAE;QAAA,IAAAa,eAAA,EAAAC,oBAAA;QACd;QACAxD,SAAS,CAAC,OAAO,CAAC;QAClBE,UAAU,CAAC,kBAAkB,CAAC;QAC9BE,aAAa,CACX,CAAAsC,KAAK,aAALA,KAAK,wBAAAa,eAAA,GAALb,KAAK,CAAEG,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiB9B,IAAI,cAAA+B,oBAAA,uBAArBA,oBAAA,CAAuBvD,OAAO,KAC5B,8DACJ,CAAC;QACDS,kBAAkB,CAAEoB,IAAI,IACtBA,IAAI,CAACE,GAAG,CAAEC,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAErB,SAAS,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAM,CAAC,CAAC,CACnE,CAAC;QACDsC,YAAY,CAACC,OAAO,CAClB,oBAAoB,EACpB,iCACF,CAAC;QAED9C,oBAAoB,CAAC,EAAE,CAAC;QACxBQ,kBAAkB,CAACuB,OAAO,GAAGC,UAAU,CAAC,MAAM;UAAA,IAAAmB,aAAA;UAC5C,MAAM/B,SAAS,IAAA+B,aAAA,GAAGjC,WAAW,cAAAiC,aAAA,uBAAXA,aAAA,CAAa/B,SAAS;UACxC5B,QAAQ,CAAC6B,eAAe,CAACD,SAAS,CAAC,EAAE;YAAE2B,OAAO,EAAE;UAAK,CAAC,CAAC;QACzD,CAAC,EAAE,KAAK,CAAC;MACX;IACF,CAAC;IAEDjB,aAAa,CAAC,CAAC;IAEf,OAAO,MAAM;MACX,IAAItB,kBAAkB,CAACuB,OAAO,EAAEqB,YAAY,CAAC5C,kBAAkB,CAACuB,OAAO,CAAC;IAC1E,CAAC;EACH,CAAC,EAAE,CACDxC,QAAQ,CAACqB,MAAM,EACfpB,QAAQ,EACRiB,cAAc,EACdQ,YAAY,EACZI,eAAe,EACfC,oBAAoB,CACrB,CAAC;EAEF7D,SAAS,CAAC,MAAM;IACd,IAAIsC,iBAAiB,KAAK,IAAI,EAAE;IAChC,IAAIA,iBAAiB,IAAI,CAAC,EAAE;IAC5B,MAAMsD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCtD,oBAAoB,CAAEwB,IAAI,IAAMA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAE,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM+B,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACtD,iBAAiB,CAAC,CAAC;EAEvB,MAAMyD,oBAAoB,GAAIC,CAAC,IAAK;IAAA,IAAAC,aAAA;IAClCD,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB,IAAInD,kBAAkB,CAACuB,OAAO,EAAEqB,YAAY,CAAC5C,kBAAkB,CAACuB,OAAO,CAAC;IACxE,MAAM;MAAEf;IAAM,CAAC,GAAGP,cAAc,CAAC,CAAC;IAClC,IAAIS,WAAW,GAAG,IAAI;IACtB,IAAI;MACFA,WAAW,GAAGe,IAAI,CAACC,KAAK,CAACC,kBAAkB,CAACnB,KAAK,CAAC,CAAC;IACrD,CAAC,CAAC,MAAM,CAAC;IACT,MAAMI,SAAS,IAAAsC,aAAA,GAAGxC,WAAW,cAAAwC,aAAA,uBAAXA,aAAA,CAAatC,SAAS;IACxC5B,QAAQ,CAAC6B,eAAe,CAACD,SAAS,CAAC,EAAE;MAAE2B,OAAO,EAAE;IAAK,CAAC,CAAC;EACzD,CAAC;EAED,MAAMa,qBAAqB,GAAGA,CAAA,KAAM;IAClCC,MAAM,CAACtE,QAAQ,CAACuE,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQtE,MAAM;MACZ,KAAK,YAAY;QACf,oBACEL,OAAA,CAACrB,GAAG;UAACiG,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,OAAO,EAAE;UAAc,CAAE;UAAAC,QAAA,gBACxD/E,OAAA,CAACnB,gBAAgB;YAACmG,IAAI,EAAE,EAAG;YAACC,SAAS,EAAE,CAAE;YAACC,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DtF,OAAA,CAACrB,GAAG;YACFiG,EAAE,EAAE;cACFW,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,MAAM,EAAE,CAAC;cACTC,KAAK,EAAE,CAAC;cACRb,QAAQ,EAAE,UAAU;cACpBC,OAAO,EAAE,MAAM;cACfa,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAb,QAAA,eAEF/E,OAAA,CAACrB,GAAG;cACFkH,SAAS,EAAC,KAAK;cACfC,GAAG,EAAEjG,SAAU;cACfkG,GAAG,EAAC,YAAY;cAChBnB,EAAE,EAAE;gBACFoB,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVC,MAAM,EAAE;cACV;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEtF,OAAA,CAACf,IAAI;UAACkH,EAAE;UAACC,OAAO,EAAE,GAAI;UAAArB,QAAA,eACpB/E,OAAA,CAACV,eAAe;YACdsF,EAAE,EAAE;cACFyB,QAAQ,EAAE,EAAE;cACZnB,KAAK,EAAE,cAAc;cACrBgB,MAAM,EAAE;YACV;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEX,KAAK,OAAO;QACV,oBACEtF,OAAA,CAACf,IAAI;UAACkH,EAAE;UAACC,OAAO,EAAE,GAAI;UAAArB,QAAA,eACpB/E,OAAA,CAACR,SAAS;YACRoF,EAAE,EAAE;cACFyB,QAAQ,EAAE,EAAE;cACZnB,KAAK,EAAE,YAAY;cACnBgB,MAAM,EAAE;YACV;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAEX;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMgB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIjG,MAAM,KAAK,YAAY,EAAE,OAAO,IAAI;IAExC,oBACEL,OAAA,CAACrB,GAAG;MAACiG,EAAE,EAAE;QAAEoB,KAAK,EAAE,MAAM;QAAEO,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAzB,QAAA,gBAC/C/E,OAAA,CAAChB,cAAc;QACbyH,OAAO,EAAC,aAAa;QACrBC,KAAK,EAAE7F,QAAS;QAChB+D,EAAE,EAAE;UACFqB,MAAM,EAAE,CAAC;UACTU,YAAY,EAAE,CAAC;UACfC,eAAe,EAAE,UAAU;UAC3B,0BAA0B,EAAE;YAC1BD,YAAY,EAAE;UAChB;QACF;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFtF,OAAA,CAAClB,KAAK;QAAC+H,OAAO,EAAE,CAAE;QAACjC,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,EAC9BhE,eAAe,CAACuB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/BxC,OAAA,CAACrB,GAAG;UAEFiG,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEa,UAAU,EAAE,QAAQ;YAAEmB,GAAG,EAAE;UAAE,CAAE;UAAA/B,QAAA,gBAEtD/E,OAAA,CAACrB,GAAG;YACFiG,EAAE,EAAE;cACFoB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVU,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAErE,IAAI,CAACrB,SAAS,GAC3B,cAAc,GACdqB,IAAI,CAACpB,MAAM,GACX,cAAc,GACd,UAAU;cACd2D,OAAO,EAAE,MAAM;cACfa,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBmB,UAAU,EAAE;YACd,CAAE;YAAAhC,QAAA,GAEDxC,IAAI,CAACrB,SAAS,iBACblB,OAAA,CAACV,eAAe;cAACsF,EAAE,EAAE;gBAAEyB,QAAQ,EAAE,EAAE;gBAAEnB,KAAK,EAAE;cAAQ;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACzD,EACA/C,IAAI,CAACpB,MAAM,IAAI,CAACoB,IAAI,CAACrB,SAAS,iBAC7BlB,OAAA,CAACnB,gBAAgB;cACfmG,IAAI,EAAE,EAAG;cACTC,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBAAEM,KAAK,EAAE;cAAQ;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNtF,OAAA,CAACpB,UAAU;YACT6H,OAAO,EAAC,SAAS;YACjB7B,EAAE,EAAE;cACFM,KAAK,EACH3C,IAAI,CAACrB,SAAS,IAAIqB,IAAI,CAACpB,MAAM,GACzB,cAAc,GACd,gBAAgB;cACtB6F,UAAU,EAAEzE,IAAI,CAACpB,MAAM,GAAG,GAAG,GAAG,GAAG;cACnC4F,UAAU,EAAE;YACd,CAAE;YAAAhC,QAAA,EAEDxC,IAAI,CAACtB;UAAK;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GA1CR9C,KAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,oBACEtF,OAAA,CAACrB,GAAG;IACFsI,SAAS,EAAC,OAAO;IACjBnC,OAAO,EAAC,MAAM;IACda,UAAU,EAAC,QAAQ;IACnBC,cAAc,EAAC,QAAQ;IACvBsB,OAAO,EAAC,SAAS;IACjBC,EAAE,EAAE,CAAE;IAAApC,QAAA,eAEN/E,OAAA,CAACd,KAAK;MAACkI,SAAS,EAAC,IAAI;MAACjB,EAAE;MAACC,OAAO,EAAE,GAAI;MAAArB,QAAA,eACpC/E,OAAA,CAACb,KAAK;QACJkI,SAAS,EAAE,CAAE;QACbzC,EAAE,EAAE;UACFgC,eAAe,EAAE,OAAO;UACxBU,OAAO,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAC;UACzBb,YAAY,EAAE,CAAC;UACfJ,QAAQ,EAAE,GAAG;UACbP,KAAK,EAAE,MAAM;UACbyB,SAAS,EAAE;QACb,CAAE;QAAA1C,QAAA,eAEF/E,OAAA,CAAClB,KAAK;UAAC+H,OAAO,EAAE,CAAE;UAAClB,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAEpC/E,OAAA,CAACrB,GAAG;YAAAoG,QAAA,gBACF/E,OAAA,CAAClB,KAAK;cACJsI,SAAS,EAAC,KAAK;cACfzB,UAAU,EAAC,QAAQ;cACnBC,cAAc,EAAC,QAAQ;cACvBiB,OAAO,EAAE,CAAE;cACXjC,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE,CAAE;cAAA3C,QAAA,gBAEd/E,OAAA,CAACpB,UAAU;gBAAC6H,OAAO,EAAC,IAAI;gBAACO,UAAU,EAAE,GAAI;gBAAC9B,KAAK,EAAC,cAAc;gBAAAH,QAAA,EAAC;cAE/D;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,KAEb,eAAAtF,OAAA,CAACrB,GAAG;gBACFkH,SAAS,EAAC,KAAK;gBACfC,GAAG,EAAEjG,SAAU;gBACfkG,GAAG,EAAC,YAAY;gBAChBnB,EAAE,EAAE;kBAAEqB,MAAM,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAO;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACFtF,OAAA,CAACpB,UAAU;gBACT6H,OAAO,EAAC,IAAI;gBACZO,UAAU,EAAE,GAAI;gBAChB9B,KAAK,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACRtF,OAAA,CAACpB,UAAU;cACT6H,OAAO,EAAC,IAAI;cACZO,UAAU,EAAE,GAAI;cAChB9B,KAAK,EAAC,cAAc;cACpBN,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE,CAAE;cAAA3C,QAAA,EACf;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtF,OAAA,CAACZ,OAAO;cACNwF,EAAE,EAAE;gBACFoB,KAAK,EAAE,EAAE;gBACT2B,EAAE,EAAE,MAAM;gBACV1B,MAAM,EAAE,CAAC;gBACTiB,OAAO,EAAE;cACX;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLX,UAAU,CAAC,CAAC,eAGb3E,OAAA,CAACrB,GAAG;YAACiG,EAAE,EAAE;cAAE6C,SAAS,EAAE;YAAS,CAAE;YAAA1C,QAAA,gBAC/B/E,OAAA,CAACpB,UAAU;cACT6H,OAAO,EAAC,IAAI;cACZO,UAAU,EAAE,GAAI;cAChB9B,KAAK,EAAC,cAAc;cACpBN,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE,CAAE;cAAA3C,QAAA,EAEbxE;YAAO;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACbtF,OAAA,CAACpB,UAAU;cACT6H,OAAO,EAAC,OAAO;cACfvB,KAAK,EAAC,gBAAgB;cACtBN,EAAE,EAAE;gBAAE2B,QAAQ,EAAE;cAAI,CAAE;cAAAxB,QAAA,EAErBtE;YAAU;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAGLgB,qBAAqB,CAAC,CAAC,EAGvB3F,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,GAAG,CAAC,iBAClDX,OAAA,CAACrB,GAAG;YAACiG,EAAE,EAAE;cAAE6C,SAAS,EAAE;YAAS,CAAE;YAAA1C,QAAA,gBAC/B/E,OAAA,CAACpB,UAAU;cACT6H,OAAO,EAAC,OAAO;cACfvB,KAAK,EAAC,gBAAgB;cACtBN,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE,CAAE;cAAA3C,QAAA,GACf,iBACgB,EAACpE,iBAAiB,EAAC,SAClC,EAACA,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,KACtC;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtF,OAAA,CAAClB,KAAK;cAACsI,SAAS,EAAC,KAAK;cAACP,OAAO,EAAE,CAAE;cAACjB,cAAc,EAAC,QAAQ;cAAAb,QAAA,gBACxD/E,OAAA,CAACjB,MAAM;gBACL0H,OAAO,EAAC,UAAU;gBAClBzB,IAAI,EAAC,OAAO;gBACZ4C,OAAO,EAAExD,oBAAqB;gBAC9ByD,SAAS,eAAE7H,OAAA,CAACJ,gBAAgB;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAP,QAAA,EACjC;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACRjF,MAAM,KAAK,OAAO,iBACjBL,OAAA,CAACjB,MAAM;gBACL0H,OAAO,EAAC,WAAW;gBACnBzB,IAAI,EAAC,OAAO;gBACZ4C,OAAO,EAAEpD,qBAAsB;gBAC/BqD,SAAS,eAAE7H,OAAA,CAACN,WAAW;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3BJ,KAAK,EAAC,SAAS;gBAAAH,QAAA,EAChB;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,EAGAjF,MAAM,KAAK,YAAY,IAAI,CAACM,iBAAiB,iBAC5CX,OAAA,CAACpB,UAAU;YACT6H,OAAO,EAAC,SAAS;YACjBvB,KAAK,EAAC,gBAAgB;YACtBN,EAAE,EAAE;cAAEkD,SAAS,EAAE;YAAS,CAAE;YAAA/C,QAAA,EAC7B;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpF,EAAA,CA1eID,WAAW;EAAA,QACExB,WAAW,EACXC,WAAW;AAAA;AAAAqJ,EAAA,GAFxB9H,WAAW;AA4ejB,eAAeA,WAAW;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}