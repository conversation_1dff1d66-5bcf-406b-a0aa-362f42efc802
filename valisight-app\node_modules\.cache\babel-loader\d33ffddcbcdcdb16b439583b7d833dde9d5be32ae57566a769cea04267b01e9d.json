{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\FiscalYear.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from \"react\";\nimport ApexCharts from \"apexcharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FiscalYearDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  fiscalData = null,\n  contentSettings = null // Add contentSettings prop\n}) => {\n  _s();\n  var _fiscal, _reportData;\n  const stackedColumnRef = useRef(null);\n  const netIncomeRef = useRef(null);\n  const grossProfitRef = useRef(null);\n  const netProfitMarginRef = useRef(null);\n\n  // Enhanced data validation function - more lenient approach\n  const isDataLoaded = () => {\n    var _fiscalData$monthlyPe, _fiscalData$monthlyGr, _fiscalData$nerProfit, _fiscalData$netIncome;\n    if (!fiscalData) {\n      console.log('FiscalYear - No fiscalData provided');\n      return false;\n    }\n    console.log('FiscalYear - fiscalData keys:', Object.keys(fiscalData));\n    console.log('FiscalYear - Full fiscalData:', fiscalData);\n\n    // Check if at least one of the required data arrays exists and has content\n    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown && Array.isArray(fiscalData.monthlyPerformanceBreakDown) && fiscalData.monthlyPerformanceBreakDown.length > 0;\n    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin) && fiscalData.monthlyGrossProfitMargin.length > 0;\n    const hasNetProfitMargin = fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin) && fiscalData.nerProfitMargin.length > 0;\n    const hasNetIncomeData = fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss) && fiscalData.netIncomeLoss.length > 0;\n    console.log('FiscalYear - Data validation:', {\n      hasMonthlyData,\n      hasGrossProfitMargin,\n      hasNetProfitMargin,\n      hasNetIncomeData,\n      monthlyDataLength: ((_fiscalData$monthlyPe = fiscalData.monthlyPerformanceBreakDown) === null || _fiscalData$monthlyPe === void 0 ? void 0 : _fiscalData$monthlyPe.length) || 0,\n      grossProfitMarginLength: ((_fiscalData$monthlyGr = fiscalData.monthlyGrossProfitMargin) === null || _fiscalData$monthlyGr === void 0 ? void 0 : _fiscalData$monthlyGr.length) || 0,\n      netProfitMarginLength: ((_fiscalData$nerProfit = fiscalData.nerProfitMargin) === null || _fiscalData$nerProfit === void 0 ? void 0 : _fiscalData$nerProfit.length) || 0,\n      netIncomeLength: ((_fiscalData$netIncome = fiscalData.netIncomeLoss) === null || _fiscalData$netIncome === void 0 ? void 0 : _fiscalData$netIncome.length) || 0\n    });\n\n    // Log sample data for debugging\n    if (hasMonthlyData) {\n      console.log('FiscalYear - Sample monthly data:', fiscalData.monthlyPerformanceBreakDown[0]);\n    }\n    if (hasGrossProfitMargin) {\n      console.log('FiscalYear - Sample gross profit data:', fiscalData.monthlyGrossProfitMargin[0]);\n    }\n\n    // Return true if we have at least the monthly data (most important)\n    // We'll handle missing other data gracefully in the charts\n    return hasMonthlyData;\n  };\n\n  // Function to check if we have any meaningful data (not all zeros)\n  const hasAnyUsableData = () => {\n    if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) {\n      return false;\n    }\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n    // Check if any monthly data has meaningful values\n    const hasMeaningfulMonthlyData = monthlyData.some(item => {\n      const income = parseFloat(item.totalIncome || 0);\n      const cogs = parseFloat(item.totalCOGS || 0);\n      const expenses = parseFloat(item.totalExpenses || 0);\n      return income > 0 || cogs > 0 || expenses > 0;\n    });\n    console.log('FiscalYear - Has meaningful data:', hasMeaningfulMonthlyData);\n    return hasMeaningfulMonthlyData;\n  };\n\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      // Clear charts first\n      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n        if (ref.current) {\n          ref.current.innerHTML = \"\";\n        }\n      });\n      // Initialize charts with new data\n      initializeCharts();\n    }\n  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array\n\n  const formatMonthYear = (year, month) => {\n    const monthNames = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  function formatNumber(num) {\n    // Round to 2 decimal places to fix floating point precision issues\n    const roundedNum = Math.round(num * 100) / 100;\n    const isNegative = roundedNum < 0;\n    const absNum = Math.abs(roundedNum);\n\n    // For numbers under 10k, show with appropriate decimal places (no suffix)\n    if (absNum < 1000) {\n      return (isNegative ? \"-\" : \"\") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));\n    }\n    const suffixes = [{\n      value: 1e12,\n      suffix: \"T\"\n    }, {\n      value: 1e9,\n      suffix: \"B\"\n    }, {\n      value: 1e6,\n      suffix: \"M\"\n    }, {\n      value: 1e3,\n      suffix: \"K\"\n    }];\n    for (let i = 0; i < suffixes.length; i++) {\n      if (absNum >= suffixes[i].value) {\n        const formatted = (absNum / suffixes[i].value).toFixed(1);\n        const cleanFormatted = formatted.endsWith(\".0\") ? formatted.slice(0, -2) : formatted;\n        return (isNegative ? \"-\" : \"\") + cleanFormatted + suffixes[i].suffix;\n      }\n    }\n    return (isNegative ? \"-\" : \"\") + roundedNum.toString();\n  }\n  const initializeCharts = () => {\n    if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) return;\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n    // Create categories from monthlyPerformanceBreakDown\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n\n    // Create lookup maps with fallback for missing data\n    const netProfitMarginMap = new Map();\n    if (fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin)) {\n      fiscalData.nerProfitMargin.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.nerProfitMargin) || 0;\n        netProfitMarginMap.set(key, value);\n      });\n    }\n    const grossProfitMarginMap = new Map();\n    if (fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin)) {\n      fiscalData.monthlyGrossProfitMargin.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.Gross_Profit_Margin);\n        const percentage = value > 1 ? value : value * 100;\n        grossProfitMarginMap.set(key, percentage || 0);\n      });\n    }\n    const netIncomeMap = new Map();\n    if (fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss)) {\n      fiscalData.netIncomeLoss.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;\n        netIncomeMap.set(key, value);\n      });\n    }\n\n    // Create aligned data arrays\n    const incomeData = monthlyData.map(item => parseFloat(item.totalIncome) / 1000 || 0);\n    const cogsData = monthlyData.map(item => parseFloat(item.totalCOGS) / 1000 || 0);\n    const expenseData = monthlyData.map(item => parseFloat(item.totalExpenses) / 1000 || 0);\n    const grossProfitMarginData = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return grossProfitMarginMap.get(key) || 0;\n    });\n    const netProfitMarginRaw = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return netProfitMarginMap.get(key) || 0;\n    });\n    const netIncomeData = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return netIncomeMap.get(key) || 0;\n    });\n\n    // Log processed data for debugging\n    console.log('FiscalYear - Processed chart data:', {\n      categoriesLength: categories.length,\n      incomeDataLength: incomeData.length,\n      incomeDataSample: incomeData.slice(0, 3),\n      cogsDataSample: cogsData.slice(0, 3),\n      expenseDataSample: expenseData.slice(0, 3),\n      grossProfitMarginSample: grossProfitMarginData.slice(0, 3),\n      netProfitMarginSample: netProfitMarginRaw.slice(0, 3),\n      netIncomeSample: netIncomeData.slice(0, 3)\n    });\n\n    // 1. Stacked Column Chart (incomeSummary)\n    const stackedColumnOptions = {\n      series: [{\n        name: \"Income\",\n        type: \"line\",\n        data: incomeData\n      }, {\n        name: \"Expense\",\n        type: \"column\",\n        data: expenseData\n      }, {\n        name: \"Cost of Goods Sold\",\n        type: \"column\",\n        data: cogsData\n      }],\n      chart: {\n        height: 450,\n        type: \"bar\",\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      dataLabels: {\n        enabled: true,\n        enabledOnSeries: [0, 1, 2],\n        // Show labels on all three series: Income, Expense, and COGS\n        formatter: function (val, opts) {\n          if (val === null || val === undefined || isNaN(val)) return \"\";\n          const absVal = Math.abs(val);\n          if (absVal >= 1000) {\n            return '$' + (val / 1000).toFixed(1) + 'm';\n          } else if (absVal >= 1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else {\n            return '$' + (val * 1000).toFixed(0);\n          }\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#20b2aa\", \"#333\", \"#333\"],\n          // Different colors for each series\n          fontWeight: \"500\"\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: [2, 0, 0],\n        curve: \"smooth\"\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: \"60%\",\n          dataLabels: {\n            total: {\n              enabled: false,\n              // Disable total labels\n              offsetY: -20,\n              style: {\n                fontSize: \"14px\",\n                fontWeight: \"500\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\n                const absVal = Math.abs(val);\n                if (absVal >= 1000) {\n                  return '$' + (val / 1000).toFixed(1) + 'm';\n                } else if (absVal >= 1) {\n                  return '$' + val.toFixed(1) + 'k';\n                } else {\n                  return '$' + (val * 1000).toFixed(0);\n                }\n              }\n            }\n          }\n        }\n      },\n      fill: {\n        opacity: [1, 1, 1] // Make all series fully opaque\n      },\n      labels: categories,\n      markers: {\n        size: [5, 0, 0],\n        fontSize: \"14px\",\n        strokeColors: \"#fff\",\n        strokeWidth: 2,\n        fillOpacity: 1,\n        hover: {\n          size: 7\n        }\n      },\n      xaxis: {\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          },\n          offsetY: 15 // Push month labels down by 15px\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false,\n        // More conservative scaling to ensure small COGS values are visible\n        min: 0,\n        max: function () {\n          // Calculate max considering stacked values\n          const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));\n          const maxIncome = Math.max(...incomeData);\n          return Math.max(maxStacked, maxIncome) * 1.2;\n        }\n      },\n      colors: [\"#20b2aa\", \"#ff8a80\", \"#53579f\"],\n      // Colors for Income, Expense, COGS\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        fontSize: \"14px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 12,\n          height: 12,\n          radius: 6 // Circular markers\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false\n        },\n        itemMargin: {\n          horizontal: 15,\n          vertical: 4\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: true // Enable clicking to hide/show series\n        },\n        onItemHover: {\n          highlightDataSeries: true // Enable hover highlighting\n        }\n      },\n      tooltip: {\n        shared: true,\n        intersect: false,\n        // Custom tooltip to clearly show all values\n        custom: function ({\n          series,\n          seriesIndex,\n          dataPointIndex,\n          w\n        }) {\n          const income = (series[0][dataPointIndex] * 1000).toFixed(0);\n          const expense = (series[1][dataPointIndex] * 1000).toFixed(0);\n          const cogs = (series[2][dataPointIndex] * 1000).toFixed(0);\n          const category = w.globals.labels[dataPointIndex];\n          return `\n        <div style=\"padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;\">\n          <div style=\"font-weight: 600; margin-bottom: 8px; color: #333;\">${category}</div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <div style=\"width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">Income: <strong>$${income}</strong></span>\n          </div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <div style=\"width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">Expense: <strong>$${expense}</strong></span>\n          </div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}\">\n            <div style=\"width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>\n          </div>\n          <div style=\"margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;\">\n            Total Costs: <strong>$${(parseFloat(expense) + parseFloat(cogs)).toFixed(0)}</strong>\n          </div>\n        </div>\n      `;\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 30 // Increased bottom padding from 0 to 30\n        }\n      },\n      // Add annotations to highlight if COGS data exists\n      annotations: {\n        yaxis: cogsData.some(val => val > 0) ? [] : [{\n          y: 0,\n          borderColor: '#FF4560',\n          label: {\n            borderColor: '#FF4560',\n            style: {\n              color: '#fff',\n              background: '#FF4560'\n            }\n          }\n        }]\n      }\n    };\n\n    // 2. Net Income Chart\n    const netIncomeOptions = {\n      series: [{\n        name: 'Net Income',\n        data: netIncomeData\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val >= 0.1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val <= -0.1) {\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\n          } else {\n            return '$' + val.toFixed(0) + 'k';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netIncomeData.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default color for positive values\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            // Teal for positive values\n            colorBelowThreshold: '#d70015' // Red for negative values\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return '$' + val.toFixed(2) + ' k';\n            } else if (val >= 0.1) {\n              return '$' + val.toFixed(2) + ' k';\n            } else if (val <= -0.1) {\n              return '-$' + Math.abs(val).toFixed(2) + ' k';\n            } else {\n              return '$' + val.toFixed(0) + ' k';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n\n    // 3. Gross Profit Margin Chart\n    const grossProfitOptions = {\n      series: [{\n        name: \"Gross Profit Margin\",\n        data: grossProfitMarginData\n      }],\n      chart: {\n        type: \"bar\",\n        height: 350,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          columnWidth: \"55%\",\n          endingShape: \"rounded\",\n          dataLabels: {\n            position: \"top\"\n          }\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        position: 'top',\n        formatter: function (val) {\n          if (val >= 1) {\n            return val.toFixed(2) + '%';\n          } else if (val >= 0.1) {\n            return val.toFixed(2) + '%';\n          } else if (val <= -0.1) {\n            return '-' + Math.abs(val).toFixed(2) + '%';\n          } else {\n            return val.toFixed(0) + '%';\n          }\n        },\n        offsetY: -20,\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#333\"],\n          fontWeight: \"500\"\n        }\n      },\n      stroke: {\n        show: true,\n        width: 2,\n        colors: [\"transparent\"]\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false,\n        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,\n        max: Math.max(...grossProfitMarginData) * 1.2\n      },\n      fill: {\n        opacity: 1\n      },\n      colors: [\"#4a4a9a\"],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"N/A\";\n            return val.toFixed(2) + \"%\";\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      }\n    };\n\n    // 4. Net Profit Margin Chart\n    const netProfitMarginOptions = {\n      series: [{\n        name: 'Net Profit Margin',\n        data: netProfitMarginRaw\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return val.toFixed(2) + '%';\n          } else if (val >= 0.1) {\n            return val.toFixed(2) + '%';\n          } else if (val <= -0.1) {\n            return '-' + Math.abs(val).toFixed(2) + '%';\n          } else {\n            return val.toFixed(0) + '%';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netProfitMarginRaw.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default line color\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            colorBelowThreshold: '#d70015'\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return val.toFixed(2) + '%';\n            } else if (val >= 0.1) {\n              return val.toFixed(2) + '%';\n            } else if (val <= -0.1) {\n              return '-' + Math.abs(val).toFixed(2) + '%';\n            } else {\n              return val.toFixed(0) + '%';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n\n    // Clear existing charts\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n      if (ref.current) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n\n    // Render charts conditionally based on content settings and data availability\n    const chartsToRender = [];\n    if (shouldDisplayChart('incomeSummary') && categories.length > 0) {\n      chartsToRender.push({\n        ref: stackedColumnRef,\n        options: stackedColumnOptions,\n        name: 'Stacked Column'\n      });\n    }\n    if (shouldDisplayChart('netIncome') && categories.length > 0 && netIncomeData.some(val => val !== 0)) {\n      chartsToRender.push({\n        ref: netIncomeRef,\n        options: netIncomeOptions,\n        name: 'Net Income'\n      });\n    }\n    if (shouldDisplayChart('grossProfitMargin') && categories.length > 0 && grossProfitMarginData.some(val => val !== 0)) {\n      chartsToRender.push({\n        ref: grossProfitRef,\n        options: grossProfitOptions,\n        name: 'Gross Profit Margin'\n      });\n    }\n    if (shouldDisplayChart('netProfitMargin') && categories.length > 0 && netProfitMarginRaw.some(val => val !== 0)) {\n      chartsToRender.push({\n        ref: netProfitMarginRef,\n        options: netProfitMarginOptions,\n        name: 'Net Profit Margin'\n      });\n    }\n    console.log('FiscalYear - Charts to render:', chartsToRender.map(c => c.name));\n\n    // Render the enabled charts with error handling\n    chartsToRender.forEach(({\n      ref,\n      options,\n      name\n    }) => {\n      if (ref.current) {\n        try {\n          console.log(`FiscalYear - Rendering ${name} chart`);\n          const chart = new ApexCharts(ref.current, options);\n          chart.render();\n\n          // Store chart instances globally for export\n          if (name === \"Stacked Column\") {\n            window.stackedColumnChart = chart;\n          } else if (name === \"Net Income\") {\n            window.netIncomeChart = chart;\n          } else if (name === \"Gross Profit Margin\") {\n            window.grossProfitChart = chart;\n          } else if (name === \"Net Profit Margin\") {\n            window.netProfitMarginChart = chart;\n          }\n        } catch (error) {\n          console.error(`FiscalYear - Error rendering ${name} chart:`, error);\n          // Show error message in chart container\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${name} chart</div>`;\n        }\n      }\n    });\n    const hasStackedColumnData = () => {\n      if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) {\n        return false;\n      }\n      const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n      // Check if any monthly data has meaningful values\n      const hasMeaningfulData = monthlyData.some(item => {\n        const income = parseFloat(item.totalIncome || 0);\n        const cogs = parseFloat(item.totalCOGS || 0);\n        const expenses = parseFloat(item.totalExpenses || 0);\n        return income > 0 || cogs > 0 || expenses > 0;\n      });\n      console.log('FiscalYear - Has stacked column data:', hasMeaningfulData);\n      return hasMeaningfulData;\n    };\n\n    // Handle charts that won't be rendered due to missing data\n    if (!shouldDisplayChart('incomeSummary') || !hasStackedColumnData()) {\n      if (stackedColumnRef.current) {\n        stackedColumnRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful monthly performance data available</div>';\n      }\n    }\n    if (!shouldDisplayChart('netIncome') || !netIncomeData.some(val => val !== 0)) {\n      if (netIncomeRef.current) {\n        netIncomeRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful net income data available</div>';\n      }\n    }\n    if (!shouldDisplayChart('grossProfitMargin') || !grossProfitMarginData.some(val => val !== 0)) {\n      if (grossProfitRef.current) {\n        grossProfitRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful gross profit margin data available</div>';\n      }\n    }\n    if (!shouldDisplayChart('netProfitMargin') || !netProfitMarginRaw.some(val => val !== 0)) {\n      if (netProfitMarginRef.current) {\n        netProfitMarginRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful net profit margin data available</div>';\n      }\n    }\n  };\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n\n  // Calculate YTD totals from monthly data\n  const calculateYTDTotals = () => {\n    if (!fiscalData.monthlyPerformanceBreakDown) return {};\n    return fiscalData.monthlyPerformanceBreakDown.reduce((totals, month) => {\n      totals.totalIncome += parseFloat(month.totalIncome || 0);\n      totals.totalCOGS += parseFloat(month.totalCOGS || 0);\n      totals.totalExpenses += parseFloat(month.totalExpenses || 0);\n      return totals;\n    }, {\n      totalIncome: 0,\n      totalCOGS: 0,\n      totalExpenses: 0\n    });\n  };\n  const ytdTotals = calculateYTDTotals();\n  // Fix floating point precision for net profit calculation\n  const netProfit = Math.round((ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) * 100) / 100;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-10 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Current Fiscal Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartYear, fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartMonth), \" | \", (_fiscal = fiscal) === null || _fiscal === void 0 ? void 0 : _fiscal.companyName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 885,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-flex grid grid-cols-4 gap-5 pb-8 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Total Income\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalIncome)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          style: {\n            backgroundColor: \"#d2e9ea\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Cost of Goods Sold\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalCOGS)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Total Expense\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalExpenses)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          style: {\n            backgroundColor: \"#d2e9ea\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Net Profit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(netProfit)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 9\n      }, this), shouldDisplayChart('incomeSummary') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Monthly Performance Breakdown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: stackedColumnRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 11\n      }, this), shouldDisplayChart('netIncome') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Net Income/(Loss)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: netIncomeRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 883,\n      columnNumber: 7\n    }, this), (shouldDisplayChart('grossProfitMargin') || shouldDisplayChart('netProfitMargin')) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[415mm] mx-auto bg-white flex flex-col  gap-10 p-10 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Current Fiscal Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 972,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartYear, fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartMonth), \" | \", (_reportData = reportData) === null || _reportData === void 0 ? void 0 : _reportData.companyName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 971,\n        columnNumber: 11\n      }, this), shouldDisplayChart('grossProfitMargin') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Gross Profit Margin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: grossProfitRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-teal-600 text-2xl\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"lighter\"\n            },\n            children: \"Gross Profit Margin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: contentTextStyle,\n            children: \"Is a share of Gross Profit in Total Income or the profit left for covering operating and other expenses. A good Gross Profit Margin is high enough to cover overhead and leave a reasonable Net Profit.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 993,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 13\n      }, this), shouldDisplayChart('netProfitMargin') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Net Profit Margin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: netProfitMarginRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-teal-600 text-2xl\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"lighter\"\n            },\n            children: \"Net Profit Margin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1021,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: contentTextStyle,\n            children: \"Shows the profit earned per dollar of income. A 10% Net Profit Margin is considered an excellent ratio. If your company has a low Net Profit Margin you are making very little profit after all costs. That implies the revenue is getting eaten up by expenses. It also increases the risk your firm will be unable to meet obligations. With a low margin, a sudden dip in sales over the next month or year could turn your company unprofitable. A high margin indicates your company has solid competitive advantages.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1020,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 969,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 882,\n    columnNumber: 5\n  }, this);\n};\n_s(FiscalYearDashboard, \"TNVw/Nsc0yyRtbTo+qU1kNvpirA=\");\n_c = FiscalYearDashboard;\nexport default FiscalYearDashboard;\nvar _c;\n$RefreshReg$(_c, \"FiscalYearDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "FiscalYearDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "fiscalData", "contentSettings", "_s", "_fiscal", "_reportData", "stackedColumnRef", "netIncomeRef", "grossProfitRef", "netProfitMarginRef", "isDataLoaded", "_fiscalData$monthlyPe", "_fiscalData$monthlyGr", "_fiscalData$nerProfit", "_fiscalData$netIncome", "console", "log", "Object", "keys", "hasMonthlyData", "monthlyPerformanceBreakDown", "Array", "isArray", "length", "hasGrossProfitMargin", "monthlyGrossProfitMargin", "hasNetProfitMargin", "nerProfitMargin", "hasNetIncomeData", "netIncomeLoss", "monthlyDataLength", "grossProfitMarginLength", "netProfitMarginLength", "netIncomeLength", "hasAnyUsableData", "monthlyData", "hasMeaningfulMonthlyData", "some", "item", "income", "parseFloat", "totalIncome", "cogs", "totalCOGS", "expenses", "totalExpenses", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "for<PERSON>ach", "ref", "current", "innerHTML", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "formatNumber", "num", "roundedNum", "Math", "round", "isNegative", "absNum", "abs", "toString", "toFixed", "suffixes", "value", "suffix", "i", "formatted", "cleanFormatted", "endsWith", "categories", "map", "netProfitMarginMap", "Map", "key", "set", "grossProfitMarginMap", "Gross_Profit_Margin", "percentage", "netIncomeMap", "incomeData", "cogsData", "expenseData", "grossProfitMarginData", "get", "netProfitMarginRaw", "netIncomeData", "categoriesLength", "incomeDataLength", "incomeDataSample", "cogsDataSample", "expenseDataSample", "grossProfitMarginSample", "netProfitMarginSample", "netIncomeSample", "stackedColumnOptions", "series", "name", "type", "data", "chart", "height", "stacked", "toolbar", "show", "background", "dataLabels", "enabled", "enabledOnSeries", "formatter", "val", "opts", "undefined", "isNaN", "absVal", "style", "fontSize", "colors", "fontWeight", "offsetY", "dropShadow", "stroke", "width", "curve", "plotOptions", "bar", "columnWidth", "total", "color", "fill", "opacity", "labels", "markers", "size", "strokeColors", "strokeWidth", "fillOpacity", "hover", "xaxis", "axisBorder", "axisTicks", "yaxis", "min", "max", "maxStacked", "max<PERSON><PERSON><PERSON>", "legend", "position", "horizontalAlign", "radius", "useSeriesColors", "itemMargin", "horizontal", "vertical", "onItemClick", "toggleDataSeries", "onItemHover", "highlightDataSeries", "tooltip", "shared", "intersect", "custom", "seriesIndex", "dataPointIndex", "w", "expense", "category", "globals", "grid", "padding", "left", "right", "top", "bottom", "annotations", "y", "borderColor", "label", "netIncomeOptions", "zoom", "discrete", "index", "fillColor", "strokeColor", "line", "threshold", "colorAboveThreshold", "colorBelowThreshold", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grossProfitOptions", "endingShape", "netProfitMarginOptions", "chartsToRender", "push", "options", "c", "render", "window", "stackedColumnChart", "netIncomeChart", "grossProfitChart", "netProfitMarginChart", "error", "hasStackedColumnData", "hasMeaningfulData", "formatHeaderPeriod", "startYear", "startMonth", "startMonthName", "calculateYTDTotals", "reduce", "totals", "ytdTotals", "netProfit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYStartYear", "FYStartMonth", "fiscal", "companyName", "backgroundColor", "reportData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/FiscalYear.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\r\nimport ApexCharts from \"apexcharts\";\r\n\r\nconst FiscalYearDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  fiscalData = null,\r\n  contentSettings = null, // Add contentSettings prop\r\n}) => {\r\n  const stackedColumnRef = useRef(null);\r\n  const netIncomeRef = useRef(null);\r\n  const grossProfitRef = useRef(null);\r\n  const netProfitMarginRef = useRef(null);\r\n\r\n  // Enhanced data validation function - more lenient approach\r\n  const isDataLoaded = () => {\r\n    if (!fiscalData) {\r\n      console.log('FiscalYear - No fiscalData provided');\r\n      return false;\r\n    }\r\n\r\n    console.log('FiscalYear - fiscalData keys:', Object.keys(fiscalData));\r\n    console.log('FiscalYear - Full fiscalData:', fiscalData);\r\n\r\n    // Check if at least one of the required data arrays exists and has content\r\n    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown &&\r\n      Array.isArray(fiscalData.monthlyPerformanceBreakDown) &&\r\n      fiscalData.monthlyPerformanceBreakDown.length > 0;\r\n\r\n    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin &&\r\n      Array.isArray(fiscalData.monthlyGrossProfitMargin) &&\r\n      fiscalData.monthlyGrossProfitMargin.length > 0;\r\n\r\n    const hasNetProfitMargin = fiscalData.nerProfitMargin &&\r\n      Array.isArray(fiscalData.nerProfitMargin) &&\r\n      fiscalData.nerProfitMargin.length > 0;\r\n\r\n    const hasNetIncomeData = fiscalData.netIncomeLoss &&\r\n      Array.isArray(fiscalData.netIncomeLoss) &&\r\n      fiscalData.netIncomeLoss.length > 0;\r\n\r\n    console.log('FiscalYear - Data validation:', {\r\n      hasMonthlyData,\r\n      hasGrossProfitMargin,\r\n      hasNetProfitMargin,\r\n      hasNetIncomeData,\r\n      monthlyDataLength: fiscalData.monthlyPerformanceBreakDown?.length || 0,\r\n      grossProfitMarginLength: fiscalData.monthlyGrossProfitMargin?.length || 0,\r\n      netProfitMarginLength: fiscalData.nerProfitMargin?.length || 0,\r\n      netIncomeLength: fiscalData.netIncomeLoss?.length || 0,\r\n    });\r\n\r\n    // Log sample data for debugging\r\n    if (hasMonthlyData) {\r\n      console.log('FiscalYear - Sample monthly data:', fiscalData.monthlyPerformanceBreakDown[0]);\r\n    }\r\n    if (hasGrossProfitMargin) {\r\n      console.log('FiscalYear - Sample gross profit data:', fiscalData.monthlyGrossProfitMargin[0]);\r\n    }\r\n\r\n    // Return true if we have at least the monthly data (most important)\r\n    // We'll handle missing other data gracefully in the charts\r\n    return hasMonthlyData;\r\n  };\r\n\r\n  // Function to check if we have any meaningful data (not all zeros)\r\n  const hasAnyUsableData = () => {\r\n    if (!fiscalData?.monthlyPerformanceBreakDown) {\r\n      return false;\r\n    }\r\n\r\n\r\n\r\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n    // Check if any monthly data has meaningful values\r\n    const hasMeaningfulMonthlyData = monthlyData.some(item => {\r\n      const income = parseFloat(item.totalIncome || 0);\r\n      const cogs = parseFloat(item.totalCOGS || 0);\r\n      const expenses = parseFloat(item.totalExpenses || 0);\r\n\r\n      return income > 0 || cogs > 0 || expenses > 0;\r\n    });\r\n\r\n    console.log('FiscalYear - Has meaningful data:', hasMeaningfulMonthlyData);\r\n    return hasMeaningfulMonthlyData;\r\n  };\r\n\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      // Clear charts first\r\n      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n        if (ref.current) {\r\n          ref.current.innerHTML = \"\";\r\n        }\r\n      });\r\n      // Initialize charts with new data\r\n      initializeCharts();\r\n    }\r\n  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = [\r\n      \"Jan\",\r\n      \"Feb\",\r\n      \"Mar\",\r\n      \"Apr\",\r\n      \"May\",\r\n      \"Jun\",\r\n      \"Jul\",\r\n      \"Aug\",\r\n      \"Sep\",\r\n      \"Oct\",\r\n      \"Nov\",\r\n      \"Dec\",\r\n    ];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  function formatNumber(num) {\r\n    // Round to 2 decimal places to fix floating point precision issues\r\n    const roundedNum = Math.round(num * 100) / 100;\r\n    const isNegative = roundedNum < 0;\r\n    const absNum = Math.abs(roundedNum);\r\n\r\n    // For numbers under 10k, show with appropriate decimal places (no suffix)\r\n    if (absNum < 1000) {\r\n      return (isNegative ? \"-\" : \"\") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));\r\n    }\r\n\r\n    const suffixes = [\r\n      { value: 1e12, suffix: \"T\" },\r\n      { value: 1e9, suffix: \"B\" },\r\n      { value: 1e6, suffix: \"M\" },\r\n      { value: 1e3, suffix: \"K\" },\r\n    ];\r\n\r\n    for (let i = 0; i < suffixes.length; i++) {\r\n      if (absNum >= suffixes[i].value) {\r\n        const formatted = (absNum / suffixes[i].value).toFixed(1);\r\n        const cleanFormatted = formatted.endsWith(\".0\")\r\n          ? formatted.slice(0, -2)\r\n          : formatted;\r\n        return (isNegative ? \"-\" : \"\") + cleanFormatted + suffixes[i].suffix;\r\n      }\r\n    }\r\n\r\n    return (isNegative ? \"-\" : \"\") + roundedNum.toString();\r\n  }\r\n\r\n  const initializeCharts = () => {\r\n    if (!fiscalData?.monthlyPerformanceBreakDown) return;\r\n\r\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n    // Create categories from monthlyPerformanceBreakDown\r\n    const categories = monthlyData.map((item) =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    // Create lookup maps with fallback for missing data\r\n    const netProfitMarginMap = new Map();\r\n    if (fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin)) {\r\n      fiscalData.nerProfitMargin.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.nerProfitMargin) || 0;\r\n        netProfitMarginMap.set(key, value);\r\n      });\r\n    }\r\n\r\n    const grossProfitMarginMap = new Map();\r\n    if (fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin)) {\r\n      fiscalData.monthlyGrossProfitMargin.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.Gross_Profit_Margin);\r\n        const percentage = value > 1 ? value : value * 100;\r\n        grossProfitMarginMap.set(key, percentage || 0);\r\n      });\r\n    }\r\n\r\n    const netIncomeMap = new Map();\r\n    if (fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss)) {\r\n      fiscalData.netIncomeLoss.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;\r\n        netIncomeMap.set(key, value);\r\n      });\r\n    }\r\n\r\n    // Create aligned data arrays\r\n    const incomeData = monthlyData.map((item) => parseFloat(item.totalIncome) / 1000 || 0);\r\n    const cogsData = monthlyData.map((item) => parseFloat(item.totalCOGS) / 1000 || 0);\r\n    const expenseData = monthlyData.map((item) => parseFloat(item.totalExpenses) / 1000 || 0);\r\n\r\n    const grossProfitMarginData = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return grossProfitMarginMap.get(key) || 0;\r\n    });\r\n\r\n    const netProfitMarginRaw = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return netProfitMarginMap.get(key) || 0;\r\n    });\r\n\r\n    const netIncomeData = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return netIncomeMap.get(key) || 0;\r\n    });\r\n\r\n    // Log processed data for debugging\r\n    console.log('FiscalYear - Processed chart data:', {\r\n      categoriesLength: categories.length,\r\n      incomeDataLength: incomeData.length,\r\n      incomeDataSample: incomeData.slice(0, 3),\r\n      cogsDataSample: cogsData.slice(0, 3),\r\n      expenseDataSample: expenseData.slice(0, 3),\r\n      grossProfitMarginSample: grossProfitMarginData.slice(0, 3),\r\n      netProfitMarginSample: netProfitMarginRaw.slice(0, 3),\r\n      netIncomeSample: netIncomeData.slice(0, 3),\r\n    });\r\n\r\n    // 1. Stacked Column Chart (incomeSummary)\r\nconst stackedColumnOptions = {\r\n  series: [\r\n    { name: \"Income\", type: \"line\", data: incomeData },\r\n    { name: \"Expense\", type: \"column\", data: expenseData },\r\n    { name: \"Cost of Goods Sold\", type: \"column\", data: cogsData },\r\n  ],\r\n  chart: {\r\n    height: 450,\r\n    type: \"bar\",\r\n    stacked: true,\r\n    toolbar: { show: false },\r\n    background: \"transparent\",\r\n  },\r\n  dataLabels: {\r\n    enabled: true,\r\n    enabledOnSeries: [0, 1, 2], // Show labels on all three series: Income, Expense, and COGS\r\n    formatter: function (val, opts) {\r\n      if (val === null || val === undefined || isNaN(val)) return \"\";\r\n      \r\n      const absVal = Math.abs(val);\r\n      \r\n      if (absVal >= 1000) {\r\n        return '$' + (val / 1000).toFixed(1) + 'm';\r\n      } else if (absVal >= 1) {\r\n        return '$' + val.toFixed(2) + 'k';\r\n      } else {\r\n        return '$' + (val * 1000).toFixed(0);\r\n      }\r\n    },\r\n    style: {\r\n      fontSize: \"14px\",\r\n      colors: [\"#20b2aa\", \"#333\", \"#333\"], // Different colors for each series\r\n      fontWeight: \"500\",\r\n    },\r\n    offsetY: -10,\r\n    background: {\r\n      enabled: false,\r\n    },\r\n    dropShadow: {\r\n      enabled: false,\r\n    },\r\n  },\r\n  stroke: {\r\n    width: [2, 0, 0],\r\n    curve: \"smooth\",\r\n  },\r\n  plotOptions: {\r\n    bar: {\r\n      columnWidth: \"60%\",\r\n      dataLabels: {\r\n        total: {\r\n          enabled: false, // Disable total labels\r\n          offsetY: -20,\r\n          style: {\r\n            fontSize: \"14px\",\r\n            fontWeight: \"500\",\r\n            color: \"#333\",\r\n          },\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n            \r\n            const absVal = Math.abs(val);\r\n            \r\n            if (absVal >= 1000) {\r\n              return '$' + (val / 1000).toFixed(1) + 'm';\r\n            } else if (absVal >= 1) {\r\n              return '$' + val.toFixed(1) + 'k';\r\n            } else {\r\n              return '$' + (val * 1000).toFixed(0);\r\n            }\r\n          },\r\n        },\r\n      },\r\n    },\r\n  },\r\n  fill: {\r\n    opacity: [1, 1, 1], // Make all series fully opaque\r\n  },\r\n  labels: categories,\r\n  markers: {\r\n    size: [5, 0, 0],\r\n    fontSize: \"14px\",\r\n    strokeColors: \"#fff\",\r\n    strokeWidth: 2,\r\n    fillOpacity: 1,\r\n    hover: {\r\n      size: 7,\r\n    },\r\n  },\r\n  xaxis: {\r\n    labels: {\r\n      style: {\r\n        colors: \"#666\",\r\n        fontSize: \"14px\",\r\n      },\r\n      offsetY: 15, // Push month labels down by 15px\r\n    },\r\n    axisBorder: {\r\n      show: false,\r\n    },\r\n    axisTicks: {\r\n      show: false,\r\n    },\r\n  },\r\n  yaxis: {\r\n    show: false,\r\n    // More conservative scaling to ensure small COGS values are visible\r\n    min: 0,\r\n    max: function () {\r\n      // Calculate max considering stacked values\r\n      const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));\r\n      const maxIncome = Math.max(...incomeData);\r\n      return Math.max(maxStacked, maxIncome) * 1.2;\r\n    },\r\n  },\r\n  colors: [\"#20b2aa\", \"#ff8a80\", \"#53579f\"], // Colors for Income, Expense, COGS\r\n  legend: {\r\n    position: \"bottom\",\r\n    horizontalAlign: \"center\",\r\n    fontSize: \"14px\",\r\n    fontWeight: \"400\",\r\n    markers: {\r\n      width: 12,\r\n      height: 12,\r\n      radius: 6, // Circular markers\r\n    },\r\n    labels: {\r\n      colors: \"#333\",\r\n      useSeriesColors: false,\r\n    },\r\n    itemMargin: {\r\n      horizontal: 15,\r\n      vertical: 4,\r\n    },\r\n    offsetY: 10,\r\n    onItemClick: {\r\n      toggleDataSeries: true, // Enable clicking to hide/show series\r\n    },\r\n    onItemHover: {\r\n      highlightDataSeries: true, // Enable hover highlighting\r\n    },\r\n  },\r\n  tooltip: {\r\n    shared: true,\r\n    intersect: false,\r\n    // Custom tooltip to clearly show all values\r\n    custom: function ({ series, seriesIndex, dataPointIndex, w }) {\r\n      const income = (series[0][dataPointIndex] * 1000).toFixed(0);\r\n      const expense = (series[1][dataPointIndex] * 1000).toFixed(0);\r\n      const cogs = (series[2][dataPointIndex] * 1000).toFixed(0);\r\n      const category = w.globals.labels[dataPointIndex];\r\n\r\n      return `\r\n        <div style=\"padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;\">\r\n          <div style=\"font-weight: 600; margin-bottom: 8px; color: #333;\">${category}</div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\r\n            <div style=\"width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">Income: <strong>$${income}</strong></span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\r\n            <div style=\"width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">Expense: <strong>$${expense}</strong></span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}\">\r\n            <div style=\"width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>\r\n          </div>\r\n          <div style=\"margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;\">\r\n            Total Costs: <strong>$${(parseFloat(expense) + parseFloat(cogs)).toFixed(0)}</strong>\r\n          </div>\r\n        </div>\r\n      `;\r\n    },\r\n  },\r\n  grid: {\r\n    show: false,\r\n    padding: {\r\n      left: 25,\r\n      right: 25,\r\n      top: 20,\r\n      bottom: 30, // Increased bottom padding from 0 to 30\r\n    },\r\n  },\r\n  // Add annotations to highlight if COGS data exists\r\n  annotations: {\r\n    yaxis: cogsData.some(val => val > 0) ? [] : [{\r\n      y: 0,\r\n      borderColor: '#FF4560',\r\n      label: {\r\n        borderColor: '#FF4560',\r\n        style: {\r\n          color: '#fff',\r\n          background: '#FF4560',\r\n        },\r\n      }\r\n    }]\r\n  }\r\n};\r\n\r\n    // 2. Net Income Chart\r\n    const netIncomeOptions = {\r\n      series: [{\r\n        name: 'Net Income',\r\n        data: netIncomeData\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom : {\r\n          enabled : false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val >= 0.1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val <= -0.1) {\r\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\r\n          } else {\r\n            return '$' + val.toFixed(0) + 'k';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netIncomeData.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px',\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default color for positive values\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C', // Teal for positive values\r\n            colorBelowThreshold: '#d70015'   // Red for negative values\r\n          }\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return '$' + val.toFixed(2) + ' k';\r\n            } else if (val >= 0.1) {\r\n              return '$' + val.toFixed(2) + ' k';\r\n            } else if (val <= -0.1) {\r\n              return '-$' + Math.abs(val).toFixed(2) + ' k';\r\n            } else {\r\n              return '$' + val.toFixed(0) + ' k';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n\r\n    // 3. Gross Profit Margin Chart\r\n    const grossProfitOptions = {\r\n      series: [{ name: \"Gross Profit Margin\", data: grossProfitMarginData }],\r\n      chart: {\r\n        type: \"bar\",\r\n        height: 350,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          columnWidth: \"55%\",\r\n          endingShape: \"rounded\",\r\n          dataLabels: { position: \"top\" },\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        position: 'top',\r\n       formatter: function (val) {\r\n          if (val >= 1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val >= 0.1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val <= -0.1) {\r\n            return '-' + Math.abs(val).toFixed(2) + '%';\r\n          } else {\r\n            return val.toFixed(0) + '%';\r\n          }\r\n        },\r\n        offsetY: -20,\r\n        style: { fontSize: \"14px\", colors: [\"#333\"], fontWeight: \"500\" },\r\n      },\r\n      stroke: { show: true, width: 2, colors: [\"transparent\"] },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: { style: { colors: \"#666\", fontSize: \"14px\" } },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,\r\n        max: Math.max(...grossProfitMarginData) * 1.2,\r\n      },\r\n      fill: { opacity: 1 },\r\n      colors: [\"#4a4a9a\"],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"N/A\";\r\n            return val.toFixed(2) + \"%\";\r\n          },\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: { left: 25, right: 25, top: 20, bottom: 0 },\r\n      },\r\n    };\r\n\r\n    // 4. Net Profit Margin Chart\r\n    const netProfitMarginOptions = {\r\n      series: [{\r\n        name: 'Net Profit Margin',\r\n        data: netProfitMarginRaw\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent', \r\n        zoom : {\r\n          enabled : false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val >= 0.1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val <= -0.1) {\r\n            return '-' + Math.abs(val).toFixed(2) + '%';\r\n          } else {\r\n            return val.toFixed(0) + '%';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netProfitMarginRaw.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default line color\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C',\r\n            colorBelowThreshold: '#d70015',\r\n          },\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return val.toFixed(2) + '%';\r\n            } else if (val >= 0.1) {\r\n              return val.toFixed(2) + '%';\r\n            } else if (val <= -0.1) {\r\n              return '-' + Math.abs(val).toFixed(2) + '%';\r\n            } else {\r\n              return val.toFixed(0) + '%';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n\r\n    // Clear existing charts\r\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n      if (ref.current) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n\r\n    \r\n\r\n    // Render charts conditionally based on content settings and data availability\r\n    const chartsToRender = [];\r\n\r\n    if (shouldDisplayChart('incomeSummary') && categories.length > 0) {\r\n      chartsToRender.push({ ref: stackedColumnRef, options: stackedColumnOptions, name: 'Stacked Column' });\r\n    }\r\n\r\n    if (shouldDisplayChart('netIncome') && categories.length > 0 && netIncomeData.some(val => val !== 0)) {\r\n      chartsToRender.push({ ref: netIncomeRef, options: netIncomeOptions, name: 'Net Income' });\r\n    }\r\n\r\n    if (shouldDisplayChart('grossProfitMargin') && categories.length > 0 && grossProfitMarginData.some(val => val !== 0)) {\r\n      chartsToRender.push({ ref: grossProfitRef, options: grossProfitOptions, name: 'Gross Profit Margin' });\r\n    }\r\n\r\n    if (shouldDisplayChart('netProfitMargin') && categories.length > 0 && netProfitMarginRaw.some(val => val !== 0)) {\r\n      chartsToRender.push({ ref: netProfitMarginRef, options: netProfitMarginOptions, name: 'Net Profit Margin' });\r\n    }\r\n\r\n    console.log('FiscalYear - Charts to render:', chartsToRender.map(c => c.name));\r\n\r\n    // Render the enabled charts with error handling\r\n    chartsToRender.forEach(({ ref, options, name }) => {\r\n      if (ref.current) {\r\n        try {\r\n          console.log(`FiscalYear - Rendering ${name} chart`);\r\n          const chart = new ApexCharts(ref.current, options);\r\n          chart.render();\r\n\r\n          // Store chart instances globally for export\r\n          if (name === \"Stacked Column\") {\r\n            window.stackedColumnChart = chart;\r\n          } else if (name === \"Net Income\") {\r\n            window.netIncomeChart = chart;\r\n          } else if (name === \"Gross Profit Margin\") {\r\n            window.grossProfitChart = chart;\r\n          } else if (name === \"Net Profit Margin\") {\r\n            window.netProfitMarginChart = chart;\r\n          }\r\n        } catch (error) {\r\n          console.error(`FiscalYear - Error rendering ${name} chart:`, error);\r\n          // Show error message in chart container\r\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${name} chart</div>`;\r\n        }\r\n      }\r\n    });\r\n\r\n    const hasStackedColumnData = () => {\r\n  if (!fiscalData?.monthlyPerformanceBreakDown) {\r\n    return false;\r\n  }\r\n\r\n  const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n  // Check if any monthly data has meaningful values\r\n  const hasMeaningfulData = monthlyData.some(item => {\r\n    const income = parseFloat(item.totalIncome || 0);\r\n    const cogs = parseFloat(item.totalCOGS || 0);\r\n    const expenses = parseFloat(item.totalExpenses || 0);\r\n\r\n    return income > 0 || cogs > 0 || expenses > 0;\r\n  });\r\n\r\n  console.log('FiscalYear - Has stacked column data:', hasMeaningfulData);\r\n  return hasMeaningfulData;\r\n};\r\n\r\n    // Handle charts that won't be rendered due to missing data\r\n    if (!shouldDisplayChart('incomeSummary') || !hasStackedColumnData()) {\r\n  if (stackedColumnRef.current) {\r\n    stackedColumnRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful monthly performance data available</div>';\r\n  } \r\n}\r\n\r\n    \r\n    if (!shouldDisplayChart('netIncome') || !netIncomeData.some(val => val !== 0)) {\r\n      if (netIncomeRef.current) {\r\n        netIncomeRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful net income data available</div>';\r\n      }\r\n    }\r\n\r\n    if (!shouldDisplayChart('grossProfitMargin') || !grossProfitMarginData.some(val => val !== 0)) {\r\n      if (grossProfitRef.current) {\r\n        grossProfitRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful gross profit margin data available</div>';\r\n      }\r\n    }\r\n\r\n    if (!shouldDisplayChart('netProfitMargin') || !netProfitMarginRaw.some(val => val !== 0)) {\r\n      if (netProfitMarginRef.current) {\r\n        netProfitMarginRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful net profit margin data available</div>';\r\n      }\r\n    }\r\n  };\r\n\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  // Calculate YTD totals from monthly data\r\n  const calculateYTDTotals = () => {\r\n    if (!fiscalData.monthlyPerformanceBreakDown) return {};\r\n\r\n    return fiscalData.monthlyPerformanceBreakDown.reduce(\r\n      (totals, month) => {\r\n        totals.totalIncome += parseFloat(month.totalIncome || 0);\r\n        totals.totalCOGS += parseFloat(month.totalCOGS || 0);\r\n        totals.totalExpenses += parseFloat(month.totalExpenses || 0);\r\n        return totals;\r\n      },\r\n      { totalIncome: 0, totalCOGS: 0, totalExpenses: 0 }\r\n    );\r\n  };\r\n\r\n  const ytdTotals = calculateYTDTotals();\r\n  // Fix floating point precision for net profit calculation\r\n  const netProfit =\r\n    Math.round(\r\n      (ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) *\r\n      100\r\n    ) / 100;\r\n\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-10 p-10 mb-8\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Current Fiscal Year\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(fiscalData?.FYStartYear, fiscalData?.FYStartMonth)} | {fiscal?.companyName}\r\n          </p>\r\n        </div>\r\n\r\n\r\n        {/* YTD Fiscal Metrics Grid */}\r\n        <div className=\"metrics-flex grid grid-cols-4 gap-5 pb-8 border-b-4 border-blue-900\">\r\n          <div className=\"p-4 text-center\">\r\n            <div className=\"text-xl mb-1\"\r\n             style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Total Income</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalIncome)}\r\n            </div>\r\n          </div>\r\n          <div\r\n            className=\"p-4 text-center\" \r\n            style={{ backgroundColor: \"#d2e9ea\" }}\r\n          >\r\n            <div className=\"text-xl mb-1\"\r\n            style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Cost of Goods Sold</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalCOGS)}\r\n            </div>\r\n          </div>\r\n          <div className=\"p-4 text-center\">\r\n            <div className=\"text-xl mb-1\"\r\n            style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Total Expense</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalExpenses)}\r\n            </div>\r\n          </div>\r\n          <div\r\n            className=\"p-4 text-center\"\r\n            style={{ backgroundColor: \"#d2e9ea\" }}\r\n          >\r\n            <div className=\"text-xl mb-1\"\r\n            style={{...contentTextStyle, fontSize : '20px', color : \"#4b5562\"}}\r\n            >YTD Net Profit</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(netProfit)}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Conditionally render Monthly Performance Breakdown Chart (incomeSummary) */}\r\n        {shouldDisplayChart('incomeSummary') && (\r\n          <div className=\"bg-white p-6 border-b-4 border-blue-900 mb-10\">\r\n            <div\r\n              className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n              style={subHeadingTextStyle}\r\n            >\r\n              Monthly Performance Breakdown\r\n            </div>\r\n            <div ref={stackedColumnRef}></div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Conditionally render Net Income Chart */}\r\n        {shouldDisplayChart('netIncome') && (\r\n          <div className=\"bg-white p-6 border-b-4 border-blue-900 mb-4\">\r\n            <div\r\n              className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n              style={subHeadingTextStyle}\r\n            >\r\n              Net Income/(Loss)\r\n            </div>\r\n            <div ref={netIncomeRef}></div>\r\n          </div>\r\n        )}\r\n\r\n      </div>\r\n\r\n      {(shouldDisplayChart('grossProfitMargin') || shouldDisplayChart('netProfitMargin')) && (\r\n        <div className=\"max-w-6xl h-[415mm] mx-auto bg-white flex flex-col  gap-10 p-10 mb-2\">\r\n\r\n          <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Current Fiscal Year\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(fiscalData?.FYStartYear, fiscalData?.FYStartMonth)} | {reportData?.companyName}\r\n          </p>\r\n        </div>\r\n\r\n          {/* Conditionally render Gross Profit Margin Chart */}\r\n          {shouldDisplayChart('grossProfitMargin') && (\r\n            <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n              <div\r\n                className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n                style={subHeadingTextStyle}\r\n              >\r\n                Gross Profit Margin\r\n              </div>\r\n              <div ref={grossProfitRef}></div>\r\n              <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n                <div\r\n                  className=\"text-teal-600 text-2xl\"\r\n                  style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n                >\r\n                  Gross Profit Margin\r\n                </div>\r\n                <div style={contentTextStyle}>\r\n                  Is a share of Gross Profit in Total Income or the profit left for\r\n                  covering operating and other expenses. A good Gross Profit Margin\r\n                  is high enough to cover overhead and leave a reasonable Net\r\n                  Profit.\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Conditionally render Net Profit Margin Chart */}\r\n          {shouldDisplayChart('netProfitMargin') && (\r\n            <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n              <div\r\n                className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n                style={subHeadingTextStyle}\r\n              >\r\n                Net Profit Margin\r\n              </div>\r\n              <div ref={netProfitMarginRef}></div>\r\n              <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg\">\r\n                <div\r\n                  className=\"text-teal-600 text-2xl\"\r\n                  style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n                >\r\n                  Net Profit Margin\r\n                </div>\r\n                <div style={contentTextStyle}>\r\n                  Shows the profit earned per dollar of income. A 10% Net Profit\r\n                  Margin is considered an excellent ratio. If your company has a low\r\n                  Net Profit Margin you are making very little profit after all\r\n                  costs. That implies the revenue is getting eaten up by expenses.\r\n                  It also increases the risk your firm will be unable to meet\r\n                  obligations. With a low margin, a sudden dip in sales over the\r\n                  next month or year could turn your company unprofitable. A high\r\n                  margin indicates your company has solid competitive advantages.\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FiscalYearDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI;EACjBC,eAAe,GAAG,IAAI,CAAE;AAC1B,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,OAAA,EAAAC,WAAA;EACJ,MAAMC,gBAAgB,GAAGd,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMe,YAAY,GAAGf,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMgB,cAAc,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiB,kBAAkB,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACzB,IAAI,CAACb,UAAU,EAAE;MACfc,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO,KAAK;IACd;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEC,MAAM,CAACC,IAAI,CAACjB,UAAU,CAAC,CAAC;IACrEc,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEf,UAAU,CAAC;;IAExD;IACA,MAAMkB,cAAc,GAAGlB,UAAU,CAACmB,2BAA2B,IAC3DC,KAAK,CAACC,OAAO,CAACrB,UAAU,CAACmB,2BAA2B,CAAC,IACrDnB,UAAU,CAACmB,2BAA2B,CAACG,MAAM,GAAG,CAAC;IAEnD,MAAMC,oBAAoB,GAAGvB,UAAU,CAACwB,wBAAwB,IAC9DJ,KAAK,CAACC,OAAO,CAACrB,UAAU,CAACwB,wBAAwB,CAAC,IAClDxB,UAAU,CAACwB,wBAAwB,CAACF,MAAM,GAAG,CAAC;IAEhD,MAAMG,kBAAkB,GAAGzB,UAAU,CAAC0B,eAAe,IACnDN,KAAK,CAACC,OAAO,CAACrB,UAAU,CAAC0B,eAAe,CAAC,IACzC1B,UAAU,CAAC0B,eAAe,CAACJ,MAAM,GAAG,CAAC;IAEvC,MAAMK,gBAAgB,GAAG3B,UAAU,CAAC4B,aAAa,IAC/CR,KAAK,CAACC,OAAO,CAACrB,UAAU,CAAC4B,aAAa,CAAC,IACvC5B,UAAU,CAAC4B,aAAa,CAACN,MAAM,GAAG,CAAC;IAErCR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3CG,cAAc;MACdK,oBAAoB;MACpBE,kBAAkB;MAClBE,gBAAgB;MAChBE,iBAAiB,EAAE,EAAAnB,qBAAA,GAAAV,UAAU,CAACmB,2BAA2B,cAAAT,qBAAA,uBAAtCA,qBAAA,CAAwCY,MAAM,KAAI,CAAC;MACtEQ,uBAAuB,EAAE,EAAAnB,qBAAA,GAAAX,UAAU,CAACwB,wBAAwB,cAAAb,qBAAA,uBAAnCA,qBAAA,CAAqCW,MAAM,KAAI,CAAC;MACzES,qBAAqB,EAAE,EAAAnB,qBAAA,GAAAZ,UAAU,CAAC0B,eAAe,cAAAd,qBAAA,uBAA1BA,qBAAA,CAA4BU,MAAM,KAAI,CAAC;MAC9DU,eAAe,EAAE,EAAAnB,qBAAA,GAAAb,UAAU,CAAC4B,aAAa,cAAAf,qBAAA,uBAAxBA,qBAAA,CAA0BS,MAAM,KAAI;IACvD,CAAC,CAAC;;IAEF;IACA,IAAIJ,cAAc,EAAE;MAClBJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEf,UAAU,CAACmB,2BAA2B,CAAC,CAAC,CAAC,CAAC;IAC7F;IACA,IAAII,oBAAoB,EAAE;MACxBT,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEf,UAAU,CAACwB,wBAAwB,CAAC,CAAC,CAAC,CAAC;IAC/F;;IAEA;IACA;IACA,OAAON,cAAc;EACvB,CAAC;;EAED;EACA,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACjC,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmB,2BAA2B,GAAE;MAC5C,OAAO,KAAK;IACd;IAIA,MAAMe,WAAW,GAAGlC,UAAU,CAACmB,2BAA2B;;IAE1D;IACA,MAAMgB,wBAAwB,GAAGD,WAAW,CAACE,IAAI,CAACC,IAAI,IAAI;MACxD,MAAMC,MAAM,GAAGC,UAAU,CAACF,IAAI,CAACG,WAAW,IAAI,CAAC,CAAC;MAChD,MAAMC,IAAI,GAAGF,UAAU,CAACF,IAAI,CAACK,SAAS,IAAI,CAAC,CAAC;MAC5C,MAAMC,QAAQ,GAAGJ,UAAU,CAACF,IAAI,CAACO,aAAa,IAAI,CAAC,CAAC;MAEpD,OAAON,MAAM,GAAG,CAAC,IAAIG,IAAI,GAAG,CAAC,IAAIE,QAAQ,GAAG,CAAC;IAC/C,CAAC,CAAC;IAEF7B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoB,wBAAwB,CAAC;IAC1E,OAAOA,wBAAwB;EACjC,CAAC;;EAED;EACA,MAAMU,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAAC7C,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAE8C,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAO9C,eAAe,CAAC8C,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;EAEDxD,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,CAACJ,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAACwC,OAAO,CAAEC,GAAG,IAAK;QACpF,IAAIA,GAAG,CAACC,OAAO,EAAE;UACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;QAC5B;MACF,CAAC,CAAC;MACF;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACpD,UAAU,EAAEC,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEnC,MAAMoD,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACD,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,SAASC,YAAYA,CAACC,GAAG,EAAE;IACzB;IACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;IAC9C,MAAMI,UAAU,GAAGH,UAAU,GAAG,CAAC;IACjC,MAAMI,MAAM,GAAGH,IAAI,CAACI,GAAG,CAACL,UAAU,CAAC;;IAEnC;IACA,IAAII,MAAM,GAAG,IAAI,EAAE;MACjB,OAAO,CAACD,UAAU,GAAG,GAAG,GAAG,EAAE,KAAKC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAGA,MAAM,CAACE,QAAQ,CAAC,CAAC,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7F;IAEA,MAAMC,QAAQ,GAAG,CACf;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC5B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC3B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC3B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,CAC5B;IAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAAC/C,MAAM,EAAEkD,CAAC,EAAE,EAAE;MACxC,IAAIP,MAAM,IAAII,QAAQ,CAACG,CAAC,CAAC,CAACF,KAAK,EAAE;QAC/B,MAAMG,SAAS,GAAG,CAACR,MAAM,GAAGI,QAAQ,CAACG,CAAC,CAAC,CAACF,KAAK,EAAEF,OAAO,CAAC,CAAC,CAAC;QACzD,MAAMM,cAAc,GAAGD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,GAC3CF,SAAS,CAACf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACtBe,SAAS;QACb,OAAO,CAACT,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIU,cAAc,GAAGL,QAAQ,CAACG,CAAC,CAAC,CAACD,MAAM;MACtE;IACF;IAEA,OAAO,CAACP,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIH,UAAU,CAACM,QAAQ,CAAC,CAAC;EACxD;EAEA,MAAMf,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACpD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmB,2BAA2B,GAAE;IAE9C,MAAMe,WAAW,GAAGlC,UAAU,CAACmB,2BAA2B;;IAE1D;IACA,MAAMyD,UAAU,GAAG1C,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IACtCgB,eAAe,CAAChB,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAACkB,KAAK,CACvC,CAAC;;IAED;IACA,MAAMuB,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC,IAAI/E,UAAU,CAAC0B,eAAe,IAAIN,KAAK,CAACC,OAAO,CAACrB,UAAU,CAAC0B,eAAe,CAAC,EAAE;MAC3E1B,UAAU,CAAC0B,eAAe,CAACsB,OAAO,CAACX,IAAI,IAAI;QACzC,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAG/B,UAAU,CAACF,IAAI,CAACX,eAAe,CAAC,IAAI,CAAC;QACnDoD,kBAAkB,CAACG,GAAG,CAACD,GAAG,EAAEV,KAAK,CAAC;MACpC,CAAC,CAAC;IACJ;IAEA,MAAMY,oBAAoB,GAAG,IAAIH,GAAG,CAAC,CAAC;IACtC,IAAI/E,UAAU,CAACwB,wBAAwB,IAAIJ,KAAK,CAACC,OAAO,CAACrB,UAAU,CAACwB,wBAAwB,CAAC,EAAE;MAC7FxB,UAAU,CAACwB,wBAAwB,CAACwB,OAAO,CAACX,IAAI,IAAI;QAClD,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAG/B,UAAU,CAACF,IAAI,CAAC8C,mBAAmB,CAAC;QAClD,MAAMC,UAAU,GAAGd,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAGA,KAAK,GAAG,GAAG;QAClDY,oBAAoB,CAACD,GAAG,CAACD,GAAG,EAAEI,UAAU,IAAI,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ;IAEA,MAAMC,YAAY,GAAG,IAAIN,GAAG,CAAC,CAAC;IAC9B,IAAI/E,UAAU,CAAC4B,aAAa,IAAIR,KAAK,CAACC,OAAO,CAACrB,UAAU,CAAC4B,aAAa,CAAC,EAAE;MACvE5B,UAAU,CAAC4B,aAAa,CAACoB,OAAO,CAACX,IAAI,IAAI;QACvC,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAG/B,UAAU,CAACF,IAAI,CAACT,aAAa,CAAC,GAAG,IAAI,IAAI,CAAC;QACxDyD,YAAY,CAACJ,GAAG,CAACD,GAAG,EAAEV,KAAK,CAAC;MAC9B,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMgB,UAAU,GAAGpD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAKE,UAAU,CAACF,IAAI,CAACG,WAAW,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IACtF,MAAM+C,QAAQ,GAAGrD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAKE,UAAU,CAACF,IAAI,CAACK,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAClF,MAAM8C,WAAW,GAAGtD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAKE,UAAU,CAACF,IAAI,CAACO,aAAa,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAEzF,MAAM6C,qBAAqB,GAAGvD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAK;MACtD,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;MACxC,OAAO2B,oBAAoB,CAACQ,GAAG,CAACV,GAAG,CAAC,IAAI,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAMW,kBAAkB,GAAGzD,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAK;MACnD,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;MACxC,OAAOuB,kBAAkB,CAACY,GAAG,CAACV,GAAG,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IAEF,MAAMY,aAAa,GAAG1D,WAAW,CAAC2C,GAAG,CAAExC,IAAI,IAAK;MAC9C,MAAM2C,GAAG,GAAG,GAAG3C,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACkB,KAAK,EAAE;MACxC,OAAO8B,YAAY,CAACK,GAAG,CAACV,GAAG,CAAC,IAAI,CAAC;IACnC,CAAC,CAAC;;IAEF;IACAlE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;MAChD8E,gBAAgB,EAAEjB,UAAU,CAACtD,MAAM;MACnCwE,gBAAgB,EAAER,UAAU,CAAChE,MAAM;MACnCyE,gBAAgB,EAAET,UAAU,CAAC5B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACxCsC,cAAc,EAAET,QAAQ,CAAC7B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACpCuC,iBAAiB,EAAET,WAAW,CAAC9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1CwC,uBAAuB,EAAET,qBAAqB,CAAC/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1DyC,qBAAqB,EAAER,kBAAkB,CAACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACrD0C,eAAe,EAAER,aAAa,CAAClC,KAAK,CAAC,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC;;IAEF;IACJ,MAAM2C,oBAAoB,GAAG;MAC3BC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAEnB;MAAW,CAAC,EAClD;QAAEiB,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAEjB;MAAY,CAAC,EACtD;QAAEe,IAAI,EAAE,oBAAoB;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAElB;MAAS,CAAC,CAC/D;MACDmB,KAAK,EAAE;QACLC,MAAM,EAAE,GAAG;QACXH,IAAI,EAAE,KAAK;QACXI,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAAE;QAC5BC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE;UAC9B,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;UAE9D,MAAMI,MAAM,GAAG1D,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC;UAE5B,IAAII,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,GAAG,GAAG,CAACJ,GAAG,GAAG,IAAI,EAAEhD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC5C,CAAC,MAAM,IAAIoD,MAAM,IAAI,CAAC,EAAE;YACtB,OAAO,GAAG,GAAGJ,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM;YACL,OAAO,GAAG,GAAG,CAACgD,GAAG,GAAG,IAAI,EAAEhD,OAAO,CAAC,CAAC,CAAC;UACtC;QACF,CAAC;QACDqD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;UAAE;UACrCC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClBpB,UAAU,EAAE;YACVqB,KAAK,EAAE;cACLpB,OAAO,EAAE,KAAK;cAAE;cAChBY,OAAO,EAAE,CAAC,EAAE;cACZJ,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBE,UAAU,EAAE,KAAK;gBACjBU,KAAK,EAAE;cACT,CAAC;cACDnB,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;gBACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;gBAEhE,MAAMI,MAAM,GAAG1D,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC;gBAE5B,IAAII,MAAM,IAAI,IAAI,EAAE;kBAClB,OAAO,GAAG,GAAG,CAACJ,GAAG,GAAG,IAAI,EAAEhD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;gBAC5C,CAAC,MAAM,IAAIoD,MAAM,IAAI,CAAC,EAAE;kBACtB,OAAO,GAAG,GAAGJ,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;gBACnC,CAAC,MAAM;kBACL,OAAO,GAAG,GAAG,CAACgD,GAAG,GAAG,IAAI,EAAEhD,OAAO,CAAC,CAAC,CAAC;gBACtC;cACF;YACF;UACF;QACF;MACF,CAAC;MACDmE,IAAI,EAAE;QACJC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;MACtB,CAAC;MACDC,MAAM,EAAE7D,UAAU;MAClB8D,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACfjB,QAAQ,EAAE,MAAM;QAChBkB,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR;MACF,CAAC;MACDK,KAAK,EAAE;QACLP,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ,CAAC;UACDG,OAAO,EAAE,EAAE,CAAE;QACf,CAAC;QACDoB,UAAU,EAAE;UACVnC,IAAI,EAAE;QACR,CAAC;QACDoC,SAAS,EAAE;UACTpC,IAAI,EAAE;QACR;MACF,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE,KAAK;QACX;QACAsC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,SAAAA,CAAA,EAAY;UACf;UACA,MAAMC,UAAU,GAAGxF,IAAI,CAACuF,GAAG,CAAC,GAAG9D,QAAQ,CAACV,GAAG,CAAC,CAACpC,IAAI,EAAE+B,CAAC,KAAK/B,IAAI,GAAG+C,WAAW,CAAChB,CAAC,CAAC,CAAC,CAAC;UAChF,MAAM+E,SAAS,GAAGzF,IAAI,CAACuF,GAAG,CAAC,GAAG/D,UAAU,CAAC;UACzC,OAAOxB,IAAI,CAACuF,GAAG,CAACC,UAAU,EAAEC,SAAS,CAAC,GAAG,GAAG;QAC9C;MACF,CAAC;MACD5B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAAE;MAC3C6B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBhC,QAAQ,EAAE,MAAM;QAChBE,UAAU,EAAE,KAAK;QACjBc,OAAO,EAAE;UACPV,KAAK,EAAE,EAAE;UACTrB,MAAM,EAAE,EAAE;UACVgD,MAAM,EAAE,CAAC,CAAE;QACb,CAAC;QACDlB,MAAM,EAAE;UACNd,MAAM,EAAE,MAAM;UACdiC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE;QACZ,CAAC;QACDlC,OAAO,EAAE,EAAE;QACXmC,WAAW,EAAE;UACXC,gBAAgB,EAAE,IAAI,CAAE;QAC1B,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE,IAAI,CAAE;QAC7B;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChB;QACAC,MAAM,EAAE,SAAAA,CAAU;UAAEjE,MAAM;UAAEkE,WAAW;UAAEC,cAAc;UAAEC;QAAE,CAAC,EAAE;UAC5D,MAAMpI,MAAM,GAAG,CAACgE,MAAM,CAAC,CAAC,CAAC,CAACmE,cAAc,CAAC,GAAG,IAAI,EAAErG,OAAO,CAAC,CAAC,CAAC;UAC5D,MAAMuG,OAAO,GAAG,CAACrE,MAAM,CAAC,CAAC,CAAC,CAACmE,cAAc,CAAC,GAAG,IAAI,EAAErG,OAAO,CAAC,CAAC,CAAC;UAC7D,MAAM3B,IAAI,GAAG,CAAC6D,MAAM,CAAC,CAAC,CAAC,CAACmE,cAAc,CAAC,GAAG,IAAI,EAAErG,OAAO,CAAC,CAAC,CAAC;UAC1D,MAAMwG,QAAQ,GAAGF,CAAC,CAACG,OAAO,CAACpC,MAAM,CAACgC,cAAc,CAAC;UAEjD,OAAO;AACb;AACA,4EAA4EG,QAAQ;AACpF;AACA;AACA,0DAA0DtI,MAAM;AAChE;AACA;AACA;AACA,2DAA2DqI,OAAO;AAClE;AACA,gFAAgFlI,IAAI,IAAI,CAAC,GAAG,eAAe,GAAG,EAAE;AAChH;AACA,wDAAwDA,IAAI,aAAaA,IAAI,IAAI,CAAC,GAAG,WAAW,GAAG,EAAE;AACrG;AACA;AACA,oCAAoC,CAACF,UAAU,CAACoI,OAAO,CAAC,GAAGpI,UAAU,CAACE,IAAI,CAAC,EAAE2B,OAAO,CAAC,CAAC,CAAC;AACvF;AACA;AACA,OAAO;QACH;MACF,CAAC;MACD0G,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE,EAAE,CAAE;QACd;MACF,CAAC;MACD;MACAC,WAAW,EAAE;QACXjC,KAAK,EAAE5D,QAAQ,CAACnD,IAAI,CAACgF,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;UAC3CiE,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,SAAS;UACtBC,KAAK,EAAE;YACLD,WAAW,EAAE,SAAS;YACtB7D,KAAK,EAAE;cACLa,KAAK,EAAE,MAAM;cACbvB,UAAU,EAAE;YACd;UACF;QACF,CAAC;MACH;IACF,CAAC;;IAEG;IACA,MAAMyE,gBAAgB,GAAG;MACvBlF,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,YAAY;QAClBE,IAAI,EAAEb;MACR,CAAC,CAAC;MACFc,KAAK,EAAE;QACLF,IAAI,EAAE,MAAM;QACZG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB0E,IAAI,EAAG;UACLxE,OAAO,EAAG;QACZ;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO,GAAG,GAAGA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAIgD,GAAG,IAAI,GAAG,EAAE;YACrB,OAAO,GAAG,GAAGA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAIgD,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI,GAAGtD,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC9C,CAAC,MAAM;YACL,OAAO,GAAG,GAAGgD,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC;QACF,CAAC;QACDqD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNE,KAAK,EAAE,UAAU;QACjBD,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ/B,IAAI,EAAE;MACR,CAAC;MACDkC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdlB,MAAM,EAAE/B,aAAa,CAACf,GAAG,CAACuC,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QAClE2B,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR,CAAC;QACD+C,QAAQ,EAAE9F,aAAa,CAACf,GAAG,CAAC,CAACuC,GAAG,EAAEuE,KAAK,MAAM;UAC3CnB,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEkB,KAAK;UACrBC,SAAS,EAAExE,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3CyE,WAAW,EAAE,MAAM;UACnBlD,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDK,KAAK,EAAE;QACLpE,UAAU,EAAEA,UAAU;QACtB6D,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE;MACR,CAAC;MACDa,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBO,WAAW,EAAE;QACX4D,IAAI,EAAE;UACJnE,MAAM,EAAE;YACNoE,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAAE;YAChCC,mBAAmB,EAAE,SAAS,CAAG;UACnC;QACF;MACF,CAAC;MACD7B,OAAO,EAAE;QACPiB,CAAC,EAAE;UACDlE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAO,GAAG,GAAGA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC,CAAC,MAAM,IAAIgD,GAAG,IAAI,GAAG,EAAE;cACrB,OAAO,GAAG,GAAGA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC,CAAC,MAAM,IAAIgD,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,IAAI,GAAGtD,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YAC/C,CAAC,MAAM;cACL,OAAO,GAAG,GAAGgD,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC;UACF;QACF;MACF,CAAC;MACD0G,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXjC,KAAK,EAAE,CAAC;UACNkC,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,MAAM;UACnBY,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClB3D,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;;IAED;IACA,MAAM4D,kBAAkB,GAAG;MACzB9F,MAAM,EAAE,CAAC;QAAEC,IAAI,EAAE,qBAAqB;QAAEE,IAAI,EAAEhB;MAAsB,CAAC,CAAC;MACtEiB,KAAK,EAAE;QACLF,IAAI,EAAE,KAAK;QACXG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDmB,WAAW,EAAE;QACXC,GAAG,EAAE;UACH2B,UAAU,EAAE,KAAK;UACjB1B,WAAW,EAAE,KAAK;UAClBiE,WAAW,EAAE,SAAS;UACtBrF,UAAU,EAAE;YAAEyC,QAAQ,EAAE;UAAM;QAChC;MACF,CAAC;MACDzC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbwC,QAAQ,EAAE,KAAK;QAChBtC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACvB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAIgD,GAAG,IAAI,GAAG,EAAE;YACrB,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAIgD,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,GAAG,GAAGtD,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7C,CAAC,MAAM;YACL,OAAOgD,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF,CAAC;QACDyD,OAAO,EAAE,CAAC,EAAE;QACZJ,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,MAAM,EAAE,CAAC,MAAM,CAAC;UAAEC,UAAU,EAAE;QAAM;MACjE,CAAC;MACDG,MAAM,EAAE;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,KAAK,EAAE,CAAC;QAAEL,MAAM,EAAE,CAAC,aAAa;MAAE,CAAC;MACzDqB,KAAK,EAAE;QACLpE,UAAU,EAAEA,UAAU;QACtB6D,MAAM,EAAE;UAAEhB,KAAK,EAAE;YAAEE,MAAM,EAAE,MAAM;YAAED,QAAQ,EAAE;UAAO;QAAE,CAAC;QACvDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE,KAAK;QACXsC,GAAG,EAAEtF,IAAI,CAACsF,GAAG,CAAC,GAAG3D,qBAAqB,CAAC,GAAG,CAAC,GAAG3B,IAAI,CAACsF,GAAG,CAAC,GAAG3D,qBAAqB,CAAC,GAAG,GAAG,GAAG,CAAC;QAC1F4D,GAAG,EAAEvF,IAAI,CAACuF,GAAG,CAAC,GAAG5D,qBAAqB,CAAC,GAAG;MAC5C,CAAC;MACD8C,IAAI,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACpBb,MAAM,EAAE,CAAC,SAAS,CAAC;MACnByC,OAAO,EAAE;QACPiB,CAAC,EAAE;UACDlE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,KAAK;YACjE,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF;MACF,CAAC;MACD0G,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE;MACrD;IACF,CAAC;;IAED;IACA,MAAMmB,sBAAsB,GAAG;MAC7BhG,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,mBAAmB;QACzBE,IAAI,EAAEd;MACR,CAAC,CAAC;MACFe,KAAK,EAAE;QACLF,IAAI,EAAE,MAAM;QACZG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB0E,IAAI,EAAG;UACLxE,OAAO,EAAG;QACZ;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAIgD,GAAG,IAAI,GAAG,EAAE;YACrB,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAIgD,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,GAAG,GAAGtD,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7C,CAAC,MAAM;YACL,OAAOgD,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF,CAAC;QACDqD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNE,KAAK,EAAE,UAAU;QACjBD,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ/B,IAAI,EAAE;MACR,CAAC;MACDkC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdlB,MAAM,EAAEhC,kBAAkB,CAACd,GAAG,CAACuC,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QACvE2B,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR,CAAC;QACD+C,QAAQ,EAAE/F,kBAAkB,CAACd,GAAG,CAAC,CAACuC,GAAG,EAAEuE,KAAK,MAAM;UAChDnB,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEkB,KAAK;UACrBC,SAAS,EAAExE,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3CyE,WAAW,EAAE,MAAM;UACnBlD,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDK,KAAK,EAAE;QACLpE,UAAU,EAAEA,UAAU;QACtB6D,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE;MACR,CAAC;MACDa,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBO,WAAW,EAAE;QACX4D,IAAI,EAAE;UACJnE,MAAM,EAAE;YACNoE,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAC9BC,mBAAmB,EAAE;UACvB;QACF;MACF,CAAC;MACD7B,OAAO,EAAE;QACPiB,CAAC,EAAE;UACDlE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B,CAAC,MAAM,IAAIgD,GAAG,IAAI,GAAG,EAAE;cACrB,OAAOA,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B,CAAC,MAAM,IAAIgD,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,GAAG,GAAGtD,IAAI,CAACI,GAAG,CAACkD,GAAG,CAAC,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7C,CAAC,MAAM;cACL,OAAOgD,GAAG,CAAChD,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B;UACF;QACF;MACF,CAAC;MACD0G,IAAI,EAAE;QACJhE,IAAI,EAAE,KAAK;QACXiE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXjC,KAAK,EAAE,CAAC;UACNkC,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,MAAM;UACnBY,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClB3D,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;;IAED;IACA,CAACnI,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAACwC,OAAO,CAAEC,GAAG,IAAK;MACpF,IAAIA,GAAG,CAACC,OAAO,EAAE;QACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;;IAIF;IACA,MAAMoJ,cAAc,GAAG,EAAE;IAEzB,IAAI1J,kBAAkB,CAAC,eAAe,CAAC,IAAI+B,UAAU,CAACtD,MAAM,GAAG,CAAC,EAAE;MAChEiL,cAAc,CAACC,IAAI,CAAC;QAAEvJ,GAAG,EAAE5C,gBAAgB;QAAEoM,OAAO,EAAEpG,oBAAoB;QAAEE,IAAI,EAAE;MAAiB,CAAC,CAAC;IACvG;IAEA,IAAI1D,kBAAkB,CAAC,WAAW,CAAC,IAAI+B,UAAU,CAACtD,MAAM,GAAG,CAAC,IAAIsE,aAAa,CAACxD,IAAI,CAACgF,GAAG,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;MACpGmF,cAAc,CAACC,IAAI,CAAC;QAAEvJ,GAAG,EAAE3C,YAAY;QAAEmM,OAAO,EAAEjB,gBAAgB;QAAEjF,IAAI,EAAE;MAAa,CAAC,CAAC;IAC3F;IAEA,IAAI1D,kBAAkB,CAAC,mBAAmB,CAAC,IAAI+B,UAAU,CAACtD,MAAM,GAAG,CAAC,IAAImE,qBAAqB,CAACrD,IAAI,CAACgF,GAAG,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;MACpHmF,cAAc,CAACC,IAAI,CAAC;QAAEvJ,GAAG,EAAE1C,cAAc;QAAEkM,OAAO,EAAEL,kBAAkB;QAAE7F,IAAI,EAAE;MAAsB,CAAC,CAAC;IACxG;IAEA,IAAI1D,kBAAkB,CAAC,iBAAiB,CAAC,IAAI+B,UAAU,CAACtD,MAAM,GAAG,CAAC,IAAIqE,kBAAkB,CAACvD,IAAI,CAACgF,GAAG,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;MAC/GmF,cAAc,CAACC,IAAI,CAAC;QAAEvJ,GAAG,EAAEzC,kBAAkB;QAAEiM,OAAO,EAAEH,sBAAsB;QAAE/F,IAAI,EAAE;MAAoB,CAAC,CAAC;IAC9G;IAEAzF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEwL,cAAc,CAAC1H,GAAG,CAAC6H,CAAC,IAAIA,CAAC,CAACnG,IAAI,CAAC,CAAC;;IAE9E;IACAgG,cAAc,CAACvJ,OAAO,CAAC,CAAC;MAAEC,GAAG;MAAEwJ,OAAO;MAAElG;IAAK,CAAC,KAAK;MACjD,IAAItD,GAAG,CAACC,OAAO,EAAE;QACf,IAAI;UACFpC,OAAO,CAACC,GAAG,CAAC,0BAA0BwF,IAAI,QAAQ,CAAC;UACnD,MAAMG,KAAK,GAAG,IAAIlH,UAAU,CAACyD,GAAG,CAACC,OAAO,EAAEuJ,OAAO,CAAC;UAClD/F,KAAK,CAACiG,MAAM,CAAC,CAAC;;UAEd;UACA,IAAIpG,IAAI,KAAK,gBAAgB,EAAE;YAC7BqG,MAAM,CAACC,kBAAkB,GAAGnG,KAAK;UACnC,CAAC,MAAM,IAAIH,IAAI,KAAK,YAAY,EAAE;YAChCqG,MAAM,CAACE,cAAc,GAAGpG,KAAK;UAC/B,CAAC,MAAM,IAAIH,IAAI,KAAK,qBAAqB,EAAE;YACzCqG,MAAM,CAACG,gBAAgB,GAAGrG,KAAK;UACjC,CAAC,MAAM,IAAIH,IAAI,KAAK,mBAAmB,EAAE;YACvCqG,MAAM,CAACI,oBAAoB,GAAGtG,KAAK;UACrC;QACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;UACdnM,OAAO,CAACmM,KAAK,CAAC,gCAAgC1G,IAAI,SAAS,EAAE0G,KAAK,CAAC;UACnE;UACAhK,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,kFAAkFoD,IAAI,cAAc;QAC9H;MACF;IACF,CAAC,CAAC;IAEF,MAAM2G,oBAAoB,GAAGA,CAAA,KAAM;MACrC,IAAI,EAAClN,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmB,2BAA2B,GAAE;QAC5C,OAAO,KAAK;MACd;MAEA,MAAMe,WAAW,GAAGlC,UAAU,CAACmB,2BAA2B;;MAE1D;MACA,MAAMgM,iBAAiB,GAAGjL,WAAW,CAACE,IAAI,CAACC,IAAI,IAAI;QACjD,MAAMC,MAAM,GAAGC,UAAU,CAACF,IAAI,CAACG,WAAW,IAAI,CAAC,CAAC;QAChD,MAAMC,IAAI,GAAGF,UAAU,CAACF,IAAI,CAACK,SAAS,IAAI,CAAC,CAAC;QAC5C,MAAMC,QAAQ,GAAGJ,UAAU,CAACF,IAAI,CAACO,aAAa,IAAI,CAAC,CAAC;QAEpD,OAAON,MAAM,GAAG,CAAC,IAAIG,IAAI,GAAG,CAAC,IAAIE,QAAQ,GAAG,CAAC;MAC/C,CAAC,CAAC;MAEF7B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoM,iBAAiB,CAAC;MACvE,OAAOA,iBAAiB;IAC1B,CAAC;;IAEG;IACA,IAAI,CAACtK,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAACqK,oBAAoB,CAAC,CAAC,EAAE;MACvE,IAAI7M,gBAAgB,CAAC6C,OAAO,EAAE;QAC5B7C,gBAAgB,CAAC6C,OAAO,CAACC,SAAS,GAAG,yHAAyH;MAChK;IACF;IAGI,IAAI,CAACN,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC+C,aAAa,CAACxD,IAAI,CAACgF,GAAG,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;MAC7E,IAAI9G,YAAY,CAAC4C,OAAO,EAAE;QACxB5C,YAAY,CAAC4C,OAAO,CAACC,SAAS,GAAG,gHAAgH;MACnJ;IACF;IAEA,IAAI,CAACN,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC4C,qBAAqB,CAACrD,IAAI,CAACgF,GAAG,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;MAC7F,IAAI7G,cAAc,CAAC2C,OAAO,EAAE;QAC1B3C,cAAc,CAAC2C,OAAO,CAACC,SAAS,GAAG,yHAAyH;MAC9J;IACF;IAEA,IAAI,CAACN,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC8C,kBAAkB,CAACvD,IAAI,CAACgF,GAAG,IAAIA,GAAG,KAAK,CAAC,CAAC,EAAE;MACxF,IAAI5G,kBAAkB,CAAC0C,OAAO,EAAE;QAC9B1C,kBAAkB,CAAC0C,OAAO,CAACC,SAAS,GAAG,uHAAuH;MAChK;IACF;EACF,CAAC;EAGD,MAAMiK,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAM9J,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAAC6J,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMC,cAAc,GAAG/J,UAAU,CAAC8J,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGC,cAAc,IAAIF,SAAS,EAAE;EACzC,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACxN,UAAU,CAACmB,2BAA2B,EAAE,OAAO,CAAC,CAAC;IAEtD,OAAOnB,UAAU,CAACmB,2BAA2B,CAACsM,MAAM,CAClD,CAACC,MAAM,EAAEnK,KAAK,KAAK;MACjBmK,MAAM,CAAClL,WAAW,IAAID,UAAU,CAACgB,KAAK,CAACf,WAAW,IAAI,CAAC,CAAC;MACxDkL,MAAM,CAAChL,SAAS,IAAIH,UAAU,CAACgB,KAAK,CAACb,SAAS,IAAI,CAAC,CAAC;MACpDgL,MAAM,CAAC9K,aAAa,IAAIL,UAAU,CAACgB,KAAK,CAACX,aAAa,IAAI,CAAC,CAAC;MAC5D,OAAO8K,MAAM;IACf,CAAC,EACD;MAAElL,WAAW,EAAE,CAAC;MAAEE,SAAS,EAAE,CAAC;MAAEE,aAAa,EAAE;IAAE,CACnD,CAAC;EACH,CAAC;EAED,MAAM+K,SAAS,GAAGH,kBAAkB,CAAC,CAAC;EACtC;EACA,MAAMI,SAAS,GACb9J,IAAI,CAACC,KAAK,CACR,CAAC4J,SAAS,CAACnL,WAAW,GAAGmL,SAAS,CAACjL,SAAS,GAAGiL,SAAS,CAAC/K,aAAa,IACtE,GACF,CAAC,GAAG,GAAG;EAGT,oBACElD,OAAA;IAAKmO,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBpO,OAAA;MAAKmO,SAAS,EAAC,qEAAqE;MAAAC,QAAA,gBAElFpO,OAAA;QAAKmO,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGpO,OAAA;UACEmO,SAAS,EAAC,sCAAsC;UAChDpG,KAAK,EAAE7H,eAAgB;UAAAkO,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxO,OAAA;UAAGmO,SAAS,EAAC,2BAA2B;UAACpG,KAAK,EAAE3H,mBAAoB;UAAAgO,QAAA,GACjEV,kBAAkB,CAACpN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEmO,WAAW,EAAEnO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoO,YAAY,CAAC,EAAC,KAAG,GAAAjO,OAAA,GAACkO,MAAM,cAAAlO,OAAA,uBAANA,OAAA,CAAQmO,WAAW;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAINxO,OAAA;QAAKmO,SAAS,EAAC,qEAAqE;QAAAC,QAAA,gBAClFpO,OAAA;UAAKmO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpO,OAAA;YAAKmO,SAAS,EAAC,cAAc;YAC5BpG,KAAK,EAAE;cAAC,GAAG1H,gBAAgB;cAAE2H,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAwF,QAAA,EACnE;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBxO,OAAA;YAAKmO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDnK,YAAY,CAACgK,SAAS,CAACnL,WAAW;UAAC;YAAAuL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxO,OAAA;UACEmO,SAAS,EAAC,iBAAiB;UAC3BpG,KAAK,EAAE;YAAE8G,eAAe,EAAE;UAAU,CAAE;UAAAT,QAAA,gBAEtCpO,OAAA;YAAKmO,SAAS,EAAC,cAAc;YAC7BpG,KAAK,EAAE;cAAC,GAAG1H,gBAAgB;cAAE2H,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAwF,QAAA,EAClE;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7BxO,OAAA;YAAKmO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDnK,YAAY,CAACgK,SAAS,CAACjL,SAAS;UAAC;YAAAqL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxO,OAAA;UAAKmO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpO,OAAA;YAAKmO,SAAS,EAAC,cAAc;YAC7BpG,KAAK,EAAE;cAAC,GAAG1H,gBAAgB;cAAE2H,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAwF,QAAA,EAClE;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBxO,OAAA;YAAKmO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDnK,YAAY,CAACgK,SAAS,CAAC/K,aAAa;UAAC;YAAAmL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxO,OAAA;UACEmO,SAAS,EAAC,iBAAiB;UAC3BpG,KAAK,EAAE;YAAE8G,eAAe,EAAE;UAAU,CAAE;UAAAT,QAAA,gBAEtCpO,OAAA;YAAKmO,SAAS,EAAC,cAAc;YAC7BpG,KAAK,EAAE;cAAC,GAAG1H,gBAAgB;cAAE2H,QAAQ,EAAG,MAAM;cAAEY,KAAK,EAAG;YAAS,CAAE;YAAAwF,QAAA,EAClE;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBxO,OAAA;YAAKmO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDnK,YAAY,CAACiK,SAAS;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLrL,kBAAkB,CAAC,eAAe,CAAC,iBAClCnD,OAAA;QAAKmO,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5DpO,OAAA;UACEmO,SAAS,EAAC,2CAA2C;UACrDpG,KAAK,EAAE3H,mBAAoB;UAAAgO,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxO,OAAA;UAAKuD,GAAG,EAAE5C;QAAiB;UAAA0N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACN,EAGArL,kBAAkB,CAAC,WAAW,CAAC,iBAC9BnD,OAAA;QAAKmO,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAC3DpO,OAAA;UACEmO,SAAS,EAAC,2CAA2C;UACrDpG,KAAK,EAAE3H,mBAAoB;UAAAgO,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxO,OAAA;UAAKuD,GAAG,EAAE3C;QAAa;UAAAyN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEE,CAAC,EAEL,CAACrL,kBAAkB,CAAC,mBAAmB,CAAC,IAAIA,kBAAkB,CAAC,iBAAiB,CAAC,kBAChFnD,OAAA;MAAKmO,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBAEnFpO,OAAA;QAAKmO,SAAS,EAAC,+FAA+F;QAAAC,QAAA,gBAC9GpO,OAAA;UACEmO,SAAS,EAAC,sCAAsC;UAChDpG,KAAK,EAAE7H,eAAgB;UAAAkO,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxO,OAAA;UAAGmO,SAAS,EAAC,2BAA2B;UAACpG,KAAK,EAAE3H,mBAAoB;UAAAgO,QAAA,GACjEV,kBAAkB,CAACpN,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEmO,WAAW,EAAEnO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoO,YAAY,CAAC,EAAC,KAAG,GAAAhO,WAAA,GAACoO,UAAU,cAAApO,WAAA,uBAAVA,WAAA,CAAYkO,WAAW;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGHrL,kBAAkB,CAAC,mBAAmB,CAAC,iBACtCnD,OAAA;QAAKmO,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDpO,OAAA;UACEmO,SAAS,EAAC,2CAA2C;UACrDpG,KAAK,EAAE3H,mBAAoB;UAAAgO,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxO,OAAA;UAAKuD,GAAG,EAAE1C;QAAe;UAAAwN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChCxO,OAAA;UAAKmO,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzEpO,OAAA;YACEmO,SAAS,EAAC,wBAAwB;YAClCpG,KAAK,EAAE;cAAE,GAAG3H,mBAAmB;cAAE8H,UAAU,EAAE;YAAU,CAAE;YAAAkG,QAAA,EAC1D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxO,OAAA;YAAK+H,KAAK,EAAE1H,gBAAiB;YAAA+N,QAAA,EAAC;UAK9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGArL,kBAAkB,CAAC,iBAAiB,CAAC,iBACpCnD,OAAA;QAAKmO,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDpO,OAAA;UACEmO,SAAS,EAAC,2CAA2C;UACrDpG,KAAK,EAAE3H,mBAAoB;UAAAgO,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxO,OAAA;UAAKuD,GAAG,EAAEzC;QAAmB;UAAAuN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCxO,OAAA;UAAKmO,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACpEpO,OAAA;YACEmO,SAAS,EAAC,wBAAwB;YAClCpG,KAAK,EAAE;cAAE,GAAG3H,mBAAmB;cAAE8H,UAAU,EAAE;YAAU,CAAE;YAAAkG,QAAA,EAC1D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxO,OAAA;YAAK+H,KAAK,EAAE1H,gBAAiB;YAAA+N,QAAA,EAAC;UAS9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChO,EAAA,CAhhCIP,mBAAmB;AAAA8O,EAAA,GAAnB9O,mBAAmB;AAkhCzB,eAAeA,mBAAmB;AAAC,IAAA8O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}