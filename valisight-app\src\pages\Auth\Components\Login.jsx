import React, { useState } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import Cookies from "js-cookie";
import { login } from "../../../services/auth";
import { useNavigate } from "react-router-dom";
import logo from "../../../assets/Logo1.png";
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
const Login = () => {
  const navigate = useNavigate();
  const savedEmail = Cookies.get("email") || "";
  const [showPassword, setShowPassword] = useState(false);

  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Please enter a valid email address")
      .required("Email is required"),
    password: Yup.string().required("Password is required"),
  });
  const handleSubmit = async (values, { setSubmitting, setErrors }) => {
    try {
      const { email, password, rememberMe } = values;

      const response = await login({ email, password });

      if (response.status === 200) {
        Cookies.set("auth_token", response.data.token);
        Cookies.set("username", response.data.user.username);
        Cookies.set("email", response.data.user.email);

        if (response.data.user.isPasswordReset) {
          navigate("/reset-link");
        } else {
          navigate("/dashboard");
        }
      }
    } catch (error) {
      setErrors({ server: "Invalid email or password" });
    }
    setSubmitting(false);
  };

  return (
    <div className="flex items-center justify-center h-screen bg-gray-50">
      <div className="bg-white shadow-lg rounded-lg p-8 w-full max-w-md">
        <div className="text-center mb-6">
        <img src={logo} alt="Logo" className="mx-auto w-12 h-12 mb-4" />
        <h2 className="text-2xl font-semibold">Log in to your account</h2>
          <p className="text-gray-500 text-sm mt-1">
            Welcome back! Please enter your details.
          </p>
        </div>

        {/* Form */}
        <Formik
          initialValues={{
            email: savedEmail,
            password: "",
            rememberMe: !!savedEmail,
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, errors }) => (
            <Form className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <Field
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  className="mt-1 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <ErrorMessage
                  name="email"
                  component="div"
                  className="text-sm text-red-500 mt-1"
                />
              </div>

              <div className="relative">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Password
                </label>
                <div className="relative">
                  <Field
                    type={showPassword ? "text" : "password"}
                    name="password"
                    placeholder="Enter your password"
                    className="mt-1 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-2 flex items-center text-gray-500 hover:text-gray-700"
                  >
                    {showPassword ? <VisibilityIcon /> : <VisibilityOffIcon />}
                  </button>
                </div>
                <ErrorMessage
                  name="password"
                  component="div"
                  className="text-sm text-red-500 mt-1"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="flex items-center text-sm text-gray-600">
                  <Field type="checkbox" name="rememberMe" className="mr-2" />
                  Remember for 30 days
                </label>
                <button
                  type="button"
                  onClick={() => navigate("/forgot-password")}
                  className="text-sm text-blue-500 hover:underline"
                >
                  Forgot password
                </button>
              </div>

              {errors.server && (
                <div className="text-sm text-red-500 text-center">
                  {errors.server}
                </div>
              )}

              <div>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {isSubmitting ? "Signing in..." : "Sign in"}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Login;
