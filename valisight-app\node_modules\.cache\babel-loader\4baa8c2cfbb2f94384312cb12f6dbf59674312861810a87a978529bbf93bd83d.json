{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\ReportSummary.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReportSummary = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null\n}) => {\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | formatCompanyName(reportData?.companyName)}\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-4\",\n          style: headingTextStyle,\n          children: \"Executive Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg leading-relaxed text-gray-700 mb-6\",\n          style: contentTextStyle,\n          children: \"Acme Print has demonstrated significant financial improvement as of January 2025, reporting a notable increase in net profit compared to the previous year. The company achieved a YTD Net Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3 million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up from a loss in the previous year. This positive trend is driven by increased sales, improved cost management, and operational efficiency.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl font-semibold text-teal-600 mb-5\",\n        style: headingTextStyle,\n        children: \"Analysis and Actionable Recommendations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: '-7px'\n          },\n          children: \"Profitability Ratios\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Net Profit Margin has increased 33% from a negative margin the previous year. This indicates improved profitability and cost management.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Gross Profit Margin has risen to 56%, highlighting better control over production costs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: \"bold\",\n            color: 'black',\n            marginTop: '-4px',\n            marginBottom: \"-4px\"\n          },\n          children: [\"Recommendation\", /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              marginTop: '-4px'\n            },\n            children: \"Maintain the current cost management strategies, particularly focusing on sustaining high-margin product lines and reducing labor inefficiencies.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Revenue and Income Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                ...subHeadingTextStyle,\n                fontWeight: \"bold\",\n                color: \"black\",\n                marginTop: '-4px',\n                marginBottom: '-4px'\n              },\n              children: \"Significant Sales Growth\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), \" Total income increased from $3.4 million in Jan 2024 to $5.2 million in Jan 2025.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Key Product Performance:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 05 - Top performer with sales of $1.63 million.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Freight Sales - Substantial growth to $1.31 million, up from $485K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Packaging Sales - Increased to $633K from $295K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Underperforming Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 02- Decreased sales from $72K to $6K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Product 04- Signifcant drop from $158K to $1K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Focus on promoting high-performing products (like Product 05) while reassessing the strategy for underperforming items, particularly Products 02 and 04.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | Acme Print\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Cost Management and COGS Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"COGS as a percentage of income reduced from 65% to 44%.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Cost Reductions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Freight COGS - Increased by 21%, indicating higher volume of sales.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Labor COGS - Improved management with costs decreasing in several product lines.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Material Costs - Significant reductions in Product 04 and Product 05.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Continue focusing on labor efficiency and explore more strategic sourcing for raw materials to reduce material cost variations.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Expense Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Total expenses decreased by 14.5%, saving over $205K compared to the previous year.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Major Reductions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Depreciation Expense- Reduced by $161K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Sales Commissions- Down by $136K, aligning commission structures with performance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\",\n              marginTop: '-4px',\n              marginBottom: '-4px'\n            },\n            children: \"Areas of Increase\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Rent Expense- Increased by 49.6K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Salaries - G&A- Up by $87K.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Investigate the rise in G&A salaries and rent. Consider evaluating ofce space utilization and exploring remote work options to mitigate rent costs.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Return on Equity (ROE) improved to 12%, reflecting better management of equity resources.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Quick Ratio improved to 1.48, indicating better short-term liquidity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Maintain or enhance operational efficiency by investing in technology that maximizes asset utilization and minimizes downtime.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Liquidity and Cash Flow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Quick Ratio improved to 1.48, indicating better short-term liquidity.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Net Change in Cash was positive $1.4 million in January 2025.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Continue maintaining liquidity by preserving cash reserves and managing receivables more efciently.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-2 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-2 border-b-4 pt-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: headerTextStyle,\n          children: \"Report Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: [formatHeaderPeriod(reportData === null || reportData === void 0 ? void 0 : reportData.FYStartYear, reportData === null || reportData === void 0 ? void 0 : reportData.FYStartMonth), \" | Acme Print\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            ...headingTextStyle,\n            color: 'black',\n            marginBottom: \"-7px\"\n          },\n          children: \"Expense Breakdown and Recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"leading-relaxed font-semibold\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: \"black\"\n            },\n            children: \"High-Impact Expenses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Depreciation- Despite reductions, still the largest single expense.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Salaries - G&A- Continues to be a signifcant cost driver.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" mb-2\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: \"bold\",\n              color: 'black',\n              marginTop: '-4px',\n              marginBottom: \"-4px\"\n            },\n            children: \"Recommendation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 leading-relaxed\",\n            style: contentTextStyle,\n            children: \"Consider asset revaluation to reduce depreciation.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: contentTextStyle,\n            children: \"Optimize G&A expenses by streamlining administrative processes.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white pb-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: headingTextStyle,\n          children: \"Key Focus Areas for 2025\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg leading-relaxed text-gray-700\",\n          style: contentTextStyle,\n          children: \"Acme Print has demonstrated significant financial improvement, particularly in profitability and operational efficiency. Strategic cost management and increased sales have positively impacted the bottom line. Moving forward, focusing on optimizing underperforming products, managing rent and salary costs, and maintaining liquidity will ensure continued financial stability and growth.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs py-9 px-12 mt-auto\",\n        style: {\n          position: 'absolute',\n          left: 0,\n          right: 0,\n          bottom: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or fnancial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable eforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_c = ReportSummary;\nexport default ReportSummary;\nvar _c;\n$RefreshReg$(_c, \"ReportSummary\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ReportSummary", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "formatHeaderPeriod", "startYear", "startMonth", "monthNames", "startMonthName", "formatCompanyName", "companyName", "length", "substring", "className", "children", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYStartYear", "FYStartMonth", "color", "marginBottom", "fontWeight", "marginTop", "position", "left", "right", "bottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/ReportSummary.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ReportSummary = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null\r\n}) => {\r\n\r\n    const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n    const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n    \r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n    \r\n    return companyName;\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10 mb-8\">\r\n\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | formatCompanyName(reportData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Executive Summary Section */}\r\n        <div className=\"bg-white mb-4\">\r\n          <div className=\"text-2xl font-semibold text-teal-600 mb-4\" style={headingTextStyle}>\r\n            Executive Summary\r\n          </div>\r\n          <p className=\"text-lg leading-relaxed text-gray-700 mb-6\" style={contentTextStyle}>\r\n            Acme Print has demonstrated significant financial improvement as of January 2025, reporting a\r\n            notable increase in net profit compared to the previous year. The company achieved a YTD Net\r\n            Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3\r\n            million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up\r\n            from a loss in the previous year. This positive trend is driven by increased sales, improved cost\r\n            management, and operational efficiency.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Analysis and Recommendations Header */}\r\n        <div className=\"text-2xl font-semibold text-teal-600 mb-5\" style={headingTextStyle}>\r\n          Analysis and Actionable Recommendations\r\n        </div>\r\n\r\n        {/* Profitability Ratios */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: '-7px' }}>\r\n            Profitability Ratios\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Net Profit Margin has increased 33% from a negative margin the previous year. This\r\n              indicates improved profitability and cost management.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Gross Profit Margin has risen to 56%, highlighting better control over production\r\n              costs.\r\n            </p>\r\n          </div>\r\n          <div\r\n           style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n            Recommendation\r\n            <div style={{ ...contentTextStyle, marginTop: '-4px' }}>\r\n              Maintain the current cost management strategies, particularly focusing on sustaining\r\n              high-margin product lines and reducing labor inefficiencies.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Revenue and Income Analysis */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Revenue and Income Analysis\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              <span style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>Significant Sales Growth</span> Total income increased from $3.4 million\r\n              in Jan 2024 to $5.2 million in Jan 2025.\r\n            </p>\r\n            <p style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Key Product Performance:\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 05 - Top performer with sales of $1.63 million.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Freight Sales - Substantial growth to $1.31 million, up from $485K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Packaging Sales - Increased to $633K from $295K.\r\n              </p>\r\n            </div>\r\n            <p style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Underperforming Products\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 02- Decreased sales from $72K to $6K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Product 04- Signifcant drop from $158K to $1K.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Focus on promoting high-performing products (like Product 05) while reassessing the\r\n              strategy for underperforming items, particularly Products 02 and 04.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-8\">\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Cost Management and COGS Analysis */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Cost Management and COGS Analysis\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              COGS as a percentage of income reduced from 65% to 44%.\r\n            </p>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Cost Reductions:\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Freight COGS - Increased by 21%, indicating higher volume of sales.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Labor COGS - Improved management with costs decreasing in several product lines.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Material Costs - Significant reductions in Product 04 and Product 05.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Continue focusing on labor efficiency and explore more strategic sourcing for raw\r\n              materials to reduce material cost variations.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Expense Management */}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Expense Management\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Total expenses decreased by 14.5%, saving over $205K compared to the previous year.\r\n            </p>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Major Reductions\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Depreciation Expense- Reduced by $161K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Sales Commissions- Down by $136K, aligning commission structures with performance.\r\n              </p>\r\n            </div>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\", marginTop: '-4px', marginBottom: '-4px' }}>\r\n              Areas of Increase\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Rent Expense- Increased by 49.6K.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Salaries - G&A- Up by $87K.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\" mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Investigate the rise in G&A salaries and rent. Consider evaluating ofce space utilization and\r\n              exploring remote work options to mitigate rent costs.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Operational Efficiency */}\r\n        <div className='mb-2'>\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Operational Efficiency\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Return on Equity (ROE) improved to 12%, reflecting better management of equity\r\n              resources.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Quick Ratio improved to 1.48, indicating better short-term liquidity.\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\"font-semibold mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Maintain or enhance operational efficiency by investing in technology that maximizes\r\n              asset utilization and minimizes downtime.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Liquidity and Cash Flow */}\r\n        <div className='mb-2'>\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Liquidity and Cash Flow\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Quick Ratio improved to 1.48, indicating better short-term liquidity.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.\r\n            </p>\r\n            <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n              Net Change in Cash was positive $1.4 million in January 2025.\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\"font-semibold mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <div className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Continue maintaining liquidity by preserving cash reserves and managing receivables more\r\n              efciently.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-6 p-10 mb-2 relative\">\r\n        <div className=\"component-header flex items-center justify-between gap-2 border-b-4 pt-4 border-blue-900\">\r\n          <h1 style={headerTextStyle}>Report Summary</h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            {formatHeaderPeriod(reportData?.FYStartYear, reportData?.FYStartMonth)} | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Expense Breakdown and Recommendations*/}\r\n        <div className=\"mb-2\">\r\n          <h3 style={{ ...headingTextStyle, color: 'black', marginBottom: \"-7px\" }}>\r\n            Expense Breakdown and Recommendations\r\n          </h3>\r\n          <div>\r\n            <p className=\"leading-relaxed font-semibold\" style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: \"black\" }}>\r\n              High-Impact Expenses\r\n            </p>\r\n            <div>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Depreciation- Despite reductions, still the largest single expense.\r\n              </p>\r\n              <p className=\"leading-relaxed\" style={contentTextStyle}>\r\n                Salaries - G&A- Continues to be a signifcant cost driver.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div\r\n              className=\" mb-2\"\r\n             style={{ ...subHeadingTextStyle, fontWeight: \"bold\", color: 'black', marginTop: '-4px', marginBottom: \"-4px\" }}>\r\n              Recommendation\r\n            </div>\r\n            <p className=\"text-gray-700 leading-relaxed\" style={contentTextStyle}>\r\n              Consider asset revaluation to reduce depreciation.\r\n            </p>\r\n            <p style={contentTextStyle}>\r\n              Optimize G&A expenses by streamlining administrative processes.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Key Focus Areas for 2025 */}\r\n        <div className=\"bg-white pb-5\">\r\n          <div className=\"text-2xl font-semibold text-teal-600 mb-5\" style={headingTextStyle}>\r\n            Key Focus Areas for 2025\r\n          </div>\r\n          <p className=\"text-lg leading-relaxed text-gray-700\" style={contentTextStyle}>\r\n            Acme Print has demonstrated significant financial improvement, particularly in profitability and\r\n            operational efficiency. Strategic cost management and increased sales have positively impacted\r\n            the bottom line. Moving forward, focusing on optimizing underperforming products, managing\r\n            rent and salary costs, and maintaining liquidity will ensure continued financial stability and\r\n            growth.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer - Always stick to bottom */}\r\n        <div\r\n          className='text-center text-slate-300 text-xs py-9 px-12 mt-auto'\r\n          style={{ position: 'absolute', left: 0, right: 0, bottom: 0 }}\r\n        >\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or fnancial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable eforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportSummary;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EACrBC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG;AACf,CAAC,KAAK;EAEF,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACtD,MAAMC,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACF,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAME,cAAc,GAAGD,UAAU,CAACD,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGE,cAAc,IAAIH,SAAS,EAAE;EACzC,CAAC;EAEC,MAAMI,iBAAiB,GAAIC,WAAW,IAAK;IAC3C,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAACC,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAOD,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOF,WAAW;EACpB,CAAC;EAED,oBACEb,OAAA;IAAKgB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BjB,OAAA;MAAKgB,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAGjFjB,OAAA;QAAKgB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAClGjB,OAAA;UAAIkB,KAAK,EAAEhB,eAAgB;UAAAe,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CtB,OAAA;UAAGgB,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEd,mBAAoB;UAAAa,QAAA,GACjEV,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiB,WAAW,EAAEjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,YAAY,CAAC,EAAC,gDACzE;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjB,OAAA;UAAKgB,SAAS,EAAC,2CAA2C;UAACE,KAAK,EAAEf,gBAAiB;UAAAc,QAAA,EAAC;QAEpF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtB,OAAA;UAAGgB,SAAS,EAAC,4CAA4C;UAACE,KAAK,EAAEb,gBAAiB;UAAAY,QAAA,EAAC;QAOnF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,2CAA2C;QAACE,KAAK,EAAEf,gBAAiB;QAAAc,QAAA,EAAC;MAEpF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtB,OAAA;UACCkB,KAAK,EAAE;YAAE,GAAGd,mBAAmB;YAAEuB,UAAU,EAAE,MAAM;YAAEF,KAAK,EAAE,OAAO;YAAEG,SAAS,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,GAAC,gBAE/G,eAAAjB,OAAA;YAAKkB,KAAK,EAAE;cAAE,GAAGb,gBAAgB;cAAEuB,SAAS,EAAE;YAAO,CAAE;YAAAX,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,gBACrDjB,OAAA;cAAMkB,KAAK,EAAE;gBAAE,GAAGd,mBAAmB;gBAAEuB,UAAU,EAAE,MAAM;gBAAEF,KAAK,EAAE,OAAO;gBAAEG,SAAS,EAAE,MAAM;gBAAEF,YAAY,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,sFAEvJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGkB,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEnH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtB,OAAA;YAAGkB,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEnH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YACEkB,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAElH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtB,OAAA;MAAKgB,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBACjFjB,OAAA;QAAKgB,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAClGjB,OAAA;UAAIkB,KAAK,EAAEhB,eAAgB;UAAAe,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CtB,OAAA;UAAGgB,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEd,mBAAoB;UAAAa,QAAA,GACjEV,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiB,WAAW,EAAEjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,YAAY,CAAC,EAAC,eACzE;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAE7J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YACCkB,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAE7J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtB,OAAA;YAAGgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAE7J;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YACEgB,SAAS,EAAC,OAAO;YAClBE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YACEgB,SAAS,EAAC,oBAAoB;YAC/BE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGgB,SAAS,EAAC,iBAAiB;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YACEgB,SAAS,EAAC,oBAAoB;YAC/BE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAGxE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtB,OAAA;MAAKgB,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAC1FjB,OAAA;QAAKgB,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvGjB,OAAA;UAAIkB,KAAK,EAAEhB,eAAgB;UAAAe,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CtB,OAAA;UAAGgB,SAAS,EAAC,2BAA2B;UAACE,KAAK,EAAEd,mBAAoB;UAAAa,QAAA,GACjEV,kBAAkB,CAACD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEiB,WAAW,EAAEjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,YAAY,CAAC,EAAC,eACzE;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAIkB,KAAK,EAAE;YAAE,GAAGf,gBAAgB;YAAEsB,KAAK,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE1E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAGgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE;YAAQ,CAAE;YAAAR,QAAA,EAAC;UAEpH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGgB,SAAS,EAAC,iBAAiB;cAACE,KAAK,EAAEb,gBAAiB;cAAAY,QAAA,EAAC;YAExD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YACEgB,SAAS,EAAC,OAAO;YAClBE,KAAK,EAAE;cAAE,GAAGd,mBAAmB;cAAEuB,UAAU,EAAE,MAAM;cAAEF,KAAK,EAAE,OAAO;cAAEG,SAAS,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEjH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAGgB,SAAS,EAAC,+BAA+B;YAACE,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAEtE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGkB,KAAK,EAAEb,gBAAiB;YAAAY,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKgB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjB,OAAA;UAAKgB,SAAS,EAAC,2CAA2C;UAACE,KAAK,EAAEf,gBAAiB;UAAAc,QAAA,EAAC;QAEpF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtB,OAAA;UAAGgB,SAAS,EAAC,uCAAuC;UAACE,KAAK,EAAEb,gBAAiB;UAAAY,QAAA,EAAC;QAM9E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtB,OAAA;QACEgB,SAAS,EAAC,uDAAuD;QACjEE,KAAK,EAAE;UAAEW,QAAQ,EAAE,UAAU;UAAEC,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAE;QAAAf,QAAA,eAE9DjB,OAAA;UAAAiB,QAAA,EAAG;QAKH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACW,EAAA,GAvWIhC,aAAa;AAyWnB,eAAeA,aAAa;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}